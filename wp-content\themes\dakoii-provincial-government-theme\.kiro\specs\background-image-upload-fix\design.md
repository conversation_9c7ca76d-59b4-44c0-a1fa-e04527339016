# Design Document

## Overview

The background image upload control issue is caused by a mismatch between the documented custom implementation and the actual WordPress native implementation. The current code uses `add_theme_support('custom-background')` which should automatically create the upload controls, but they're not appearing. The solution is to implement the custom background system as described in the documentation, which provides better control and avoids conflicts.

## Architecture

### Current Implementation Analysis
- **Problem**: Uses WordPress native `custom-background` support but controls aren't showing
- **Root Cause**: Potential conflicts or missing dependencies for native background controls
- **Solution**: Implement the documented custom background system for reliability

### Proposed Custom Implementation
- **Custom Customizer Section**: Independent "Background Image" section
- **Custom Setting**: `bg_image_url` theme modification setting
- **Custom Control**: `WP_Customize_Image_Control` for image upload
- **Custom CSS Output**: PHP function to generate background CSS
- **Fallback System**: Cultural gradient when no image is set

## Components and Interfaces

### 1. Customizer Integration Component
**Location**: `functions.php` (new function: `nols_espa_add_background_customizer`)

**Responsibilities**:
- Create custom "Background Image" section in customizer
- Register `bg_image_url` setting with proper sanitization
- Add image upload control using `WP_Customize_Image_Control`

**Interface**:
```php
function nols_espa_add_background_customizer($wp_customize) {
    // Section creation
    // Setting registration  
    // Control addition
}
```

### 2. CSS Output Component
**Location**: `functions.php` (new function: `nols_espa_output_background_css`)

**Responsibilities**:
- Retrieve background image URL from theme modifications
- Generate appropriate CSS for background display
- Provide fallback to cultural gradient
- Output CSS in HTML head section

**Interface**:
```php
function nols_espa_output_background_css() {
    // Get background image URL
    // Generate CSS based on image availability
    // Output inline styles
}
```

### 3. Media Enqueue Component
**Location**: `functions.php` (existing function: `nols_espa_customizer_scripts`)

**Responsibilities**:
- Ensure media uploader scripts are available in customizer
- Support image selection and upload functionality

## Data Models

### Background Image Setting
- **Setting Name**: `bg_image_url`
- **Storage**: WordPress theme modifications (`theme_mods`)
- **Data Type**: String (URL)
- **Sanitization**: `esc_url_raw`
- **Transport**: `refresh` (requires page reload to see changes)

### CSS Output Structure
```css
/* When image is uploaded */
body {
    background-image: url(IMAGE_URL);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* When no image (fallback) */
body {
    background: linear-gradient(135deg, PNG_GREEN 0%, DARK_GREEN 100%);
}

/* Mobile optimization */
@media (max-width: 768px) {
    body {
        background-attachment: scroll !important;
    }
}
```

## Error Handling

### Image Upload Failures
- **Validation**: Use `esc_url_raw` to sanitize image URLs
- **Fallback**: Always provide cultural gradient fallback
- **Error Prevention**: Check if image URL exists before outputting CSS

### Customizer Integration Issues
- **Media Scripts**: Ensure `wp_enqueue_media()` is called in customizer
- **Control Registration**: Verify `WP_Customize_Image_Control` is available
- **Section Conflicts**: Use unique section ID to avoid conflicts

### CSS Output Problems
- **Hook Priority**: Use appropriate priority for `wp_head` action
- **CSS Conflicts**: Use specific selectors and !important where necessary
- **Performance**: Minimize CSS output and use efficient selectors

## Testing Strategy

### Manual Testing
1. **Customizer Access**: Verify "Background Image" section appears in customizer
2. **Upload Functionality**: Test image upload and selection from media library
3. **Live Preview**: Confirm background image shows in customizer preview
4. **Frontend Display**: Verify background appears on actual website
5. **Fallback Testing**: Confirm gradient shows when no image is set

### Cross-Browser Testing
- Test in Chrome, Firefox, Safari, Edge
- Verify mobile responsiveness
- Check background-attachment behavior on mobile devices

### WordPress Compatibility
- Test with different WordPress versions (5.0+)
- Verify compatibility with other customizer sections
- Check for conflicts with plugins that modify backgrounds

### Performance Testing
- Measure page load times with background images
- Test with various image sizes and formats
- Verify mobile performance with background-attachment: scroll

## Implementation Notes

### Removal of Native Background Support
- Remove `add_theme_support('custom-background')` to prevent conflicts
- Update `nols_espa_custom_background_styles()` function to work with custom implementation
- Ensure no references to `get_background_image()` or `get_background_color()`

### Integration with Existing Color System
- Maintain compatibility with existing cultural color system
- Use existing `nols_espa_get_current_colors()` function for gradient fallback
- Preserve existing responsive optimizations

### Code Organization
- Add new functions to existing customizer section in `functions.php`
- Maintain consistent naming convention (`nols_espa_` prefix)
- Follow WordPress coding standards and security practices