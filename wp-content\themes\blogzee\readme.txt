=== Blogzee ===
Contributors: blazethemes
Tags: blog, entertainment, one-column, two-columns, grid-layout, left-sidebar, right-sidebar, custom-header, flexible-header, custom-background, custom-colors, custom-menu, featured-images, full-width-template, post-formats, sticky-post, rtl-language-support, footer-widgets, theme-options, threaded-comments, translation-ready
Requires at least: 4.5
Tested up to: 6.8
Requires PHP: 5.6
Stable tag: 1.0.5
License: GNU General Public License v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Blogzee Theme is mobile-optimized blog focused theme with options to edit mobile view design and show/hide any page elements on mobile devices.

== Description ==

Blogzee Theme helps to illustrate your distinguished story through its interactive and stylish page layouts.  Adorned with multiple eye-catching demos, Blogzee theme allows you to set up your blog in minimal steps by importing unique demos.  Not only is it beautiful, it’s immensely easy to customize too. You will find SEO options, theme settings, menu settings, mobile view options, design controls, and all in one central place - Live Customizer. After viewing the demo and customizer settings, you will soon realize the dedicated focus that Blogzee Theme has put on mobile users, SEO, and UI/UX design. As a result, it should come as no surprise that there are image Settings - image ratio, image size, image border, image box shadow for each block and widget. It has more to offer you to extend with additional features.

== Installation ==

1. In your admin panel, go to Appearance > Themes and click the Add New button.
2. Click Upload Theme and Choose File, then select the theme's .zip file. Click Install Now.
3. Click Activate to use your new theme right away.

== Frequently Asked Questions ==

= Does this theme support any plugins? =

== Screenshots  ==
    * License: Creative Commons Zero (CC0) Public Domain
    * License Url: https://stocksnap.io/license
    * Sources:
		https://stocksnap.io/photo/art-people-GWDEXDGSE6
		https://stocksnap.io/photo/scenic-castle-RDMQWXDIMT
		https://stocksnap.io/photo/painted-wall-4GJOVOEYTH
		https://stocksnap.io/photo/couple-camping-ZIEGKSHIRP
		https://stocksnap.io/photo/couple-picture-AYHGTVH5BQ
		https://stocksnap.io/photo/person-walking-HZN7IJIKBN
		https://stocksnap.io/photo/active-male-0ZVXOZH4BO

	
Blogzee includes support for WooCommerce and for Infinite Scroll in Jetpack.

== Credits ==
* Based on Underscores https://underscores.me/, (C) 2012-2020 Automattic, Inc., [GPLv2 or later](https://www.gnu.org/licenses/gpl-2.0.html)
* normalize.css https://necolas.github.io/normalize.css/, (C) 2012-2018 Nicolas Gallagher and Jonathan Neal, [MIT](https://opensource.org/licenses/MIT)

Breadcrumb Trail
Author: Justin Tadlock
URL: https://github.com/justintadlock/breadcrumb-trail/blob/master/breadcrumb-trail.php
License: GPLv2 or later

Fontawesome
URL: https://fontawesome.com
License: https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)

Swiper
Author: Vladimir Kharlampidi
URL: https://swiperjs.com
License: The MIT License

Select2
Author: Kevin Brown
URL: https://select2.org
License: The MIT License

jQuery Cookie
Author: Klause Hartl
URL: https://plugins.jquery.com/cookie
License: The MIT License

Magnific Popup
Author: Dmitry Semenov
URL: https://dimsemenov.com/plugins/magnific-popup/
License: The MIT License

== Changelog ==
= 1.0.5 - July 30th, 2025 =
	* Added - header builder css tweaks

= 1.0.4 - July 16th, 2025 =
	* Added - demos added

= 1.0.3 - July 9th, 2025 =
	* Improvements - design improvements

= 1.0.2 - June 13th, 2025 =
	* Added - upsell added

= 1.0.1 - May 26th, 2025 =
	* Added - demo listed

= 1.0.0 - May 20th, 2025 =
	* Initial release