	
	#customize-control-solid_color_preset .components-radio-control__option,
	#customize-control-gradient_color_preset .components-radio-control__option {
		display: initial;
	}
	
	#customize-control-header_builder_section_width,
	#customize-control-header_builder_background,
	#customize-control-header_first_row_column,
	#customize-control-header_first_row_background,
	#customize-control-header_first_row_column_one,
	#customize-control-header_second_row_column,
	#customize-control-header_second_row_background,
	#customize-control-header_second_row_column_one,
	#customize-control-header_third_row_column,
	#customize-control-header_third_row_background,
	#customize-control-header_third_row_column_one,
	#customize-control-footer_builder_section_width,
	#customize-control-footer_builder_background,
	#customize-control-footer_first_row_column,
	#customize-control-footer_first_row_background,
	#customize-control-footer_first_row_column_one,
	#customize-control-footer_second_row_column,
	#customize-control-footer_second_row_background,
	#customize-control-footer_second_row_column_one,
	#customize-control-footer_third_row_column,
	#customize-control-footer_third_row_background,
	#customize-control-footer_third_row_column_one,
	#customize-control-mobile_canvas_reflector,
	#customize-control-bottom_footer_header_or_custom,
	#customize-control-social_icon_color,
	#customize-control-header_menu_hover_effect,
	#customize-control-mobile_canvas_icon_color {
		padding-top: 10px;
	}
	
	.customize-responsive-radio-tab-control .radio-tab-wrapper {
		display: flex;
		justify-content: space-between;
		-webkit-justify-content: space-between;
		align-items: center;
		-webkit-align-items: center;
		gap: 7px;
	}

	.customize-responsive-radio-tab-control .radio-tab-wrapper .customize-control-title {
		margin-bottom: 0;
	}

	.customize-responsive-radio-tab-control .components-button-group {
		border: 1px solid #5fa1ff;
		border-right: none;
	}

	.customize-responsive-radio-tab-control .components-button.is-icon {
		padding-top: 8px;
	}

	.components-button-group .components-button:first-child {
		border-radius: 2px 0 0 2px;
	}

	.customize-responsive-radio-tab-control .components-button {
		height: 25px;
		padding: 6px 5px;
		box-shadow: none;
	}
	
	.customize-responsive-radio-tab-control .components-button.is-primary {
		background: #5fa1ff;
		border: none;
		box-shadow: none;
		border-radius: 0;
	}

	.customize-responsive-radio-tab-control .components-button:nth-child(2) {
		border-right: 1px solid #5fa1ff;
		border-left: 1px solid #5fa1ff;
	}

	.customize-responsive-radio-tab-control .components-button.is-secondary:hover:not(:disabled),
	.customize-responsive-radio-tab-control .components-button:focus:not(:disabled) {
		box-shadow: none;
	}

	.customize-responsive-radio-tab-control .components-button:hover {
		background-color: #f0f0f1;
	}

	.customize-responsive-radio-tab-control .components-button:last-child {
		border-right: 1px solid #5fa1ff;
		margin: 0;
	}

	/* typography preset select */
	.customize-control .dashicon.preset-isactive:before {
		padding: 4px;
		background: #e9f2fd;
		border-radius: 50%;
		border: 1px solid #5fa1ff;
		font-size: 10px;
	}	
	
	
	/*
	==================
	HEADER BUILDER CSS BY AMIR MAHARJAN
	==================
	*/

	.blogzee-builder--on {
		bottom: 268px;
		height: initial;
	}

	.blogzee-builder-collapsed {
		bottom: 49px;
		height: initial;
	}
	
	#customize-control-header_builder,
	#customize-control-footer_builder,
	#customize-control-responsive_header_builder {
		background-color: transparent;
		padding: 0;
	}
	
	.row .components-dropdown .column.has-children .column-item {
		display: flex;
		align-items: center;
		-webkit-align-items: center;
		justify-content: space-between;
		-webkit-justify-content: space-between;
		padding: 5px 14px;
		background: #d5d5d5;
		border: 1px solid #d5d5d5;
		border-radius: 3px;
		height: 20px;
		cursor: grab;
		min-width: 90px;
		white-space: nowrap;
	}

	.row .components-dropdown .column.has-children .column-item .item-label:first-child {
		color: #000;
		font-weight: 500;
		font-family: inherit;
	}

	.row .components-dropdown .column.has-children .column-item .item-label:first-child:before {
		content: '\f58e';
        font-weight: 900;
        font-family: 'Font Awesome 5 Free';
        padding-right: 8px;
        font-size: 11px;
        color: #00000070;
	}

	.row .components-dropdown .column.has-children .column-item:hover,
	.row .components-dropdown .column.has-children .column-item.is-active {
		border-color: #858585;
	}

	.row .components-dropdown .column.has-children .column-item .dashicon:before {
		font-size: 18px;
		margin-left: 5px;
	}
	
	.header-elements-wrapper {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		width: 100%;
		align-items: center;
	}
	
	.header-elements-wrapper .element {
		padding: 12px;
		cursor: pointer;
	}
	
	.header-elements-wrapper .element:hover {
		color: #3582c4;
	}
	
	.header-elements-wrapper .element .item-icon {
		width: inherit;
	}
	
	.header-elements-wrapper .element .item-icon,
	.header-elements-wrapper .element .item-label {
		display: block;
		text-align: center
	}
	
	.blogzee-header-builder-popover {
		width: 18%;
	}
	
	.blogzee-header-builder-popover .components-popover__content {
		padding: 5px;
		width: 100%;
		background-color: #fff;
		box-shadow: 0px 2px 4px 2px #0000001c;
		border-radius: 4px;
	}
	
	#customize-theme-controls .blogzee-builder-related.open ~ #sub-accordion-section-header_builder_section.is-active,
	#customize-theme-controls .blogzee-builder-related.open ~ #sub-accordion-section-footer_builder_section.is-active {
		transform: none;
	}
	
    /* Header Builder selectors */
	#customize-theme-controls .blogzee-builder-related-parent {
		display: none !important;
	}

	/*  */
	/* customizer */
	.customize-control-radio-image .components-flex,
	.customize-responsive-radio-image-control .components-flex {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		flex-direction: initial;
	}

	.customize-control-radio-image .components-radio-control__option,
	.customize-responsive-radio-image-control .components-radio-control__option {
		background-color: transparent;
		flex: 0 1 47%;
		margin: 0;
	}

	.customize-control-radio-image img,
	.customize-responsive-radio-image-control img {
		border: none;
		border-radius: 8px 8px 0 0;
		background-color: transparent;
		padding: 8px 10px 0 10px;
	}

	#customize-controls img {
		box-sizing: border-box;
	}

	.customize-control-radio-image img:hover,
	.customize-control-radio-image img:focus,
	.customize-responsive-radio-image-control img:hover,
	.customize-responsive-radio-image-control img:focus {
		background-color: #dde0e2;
	}

	.customize-control-radio-image input[type="radio"]:checked ~ label img,
	.customize-responsive-radio-image-control input[type="radio"]:checked ~ label img {
		background-color: #5fa1ff;
	}

	.customize-responsive-radio-image-control .control-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.customize-builder-reflector-control .widget-reflector {
		display: flex;
		justify-content: space-between;
		color: #1d2c33;
		padding: 6px 12px;
		text-decoration: none;
		border: 1px dashed #b4b9be;
		background: transparent;
		margin-bottom: 10px;
		text-align: center;
		font-size: 12px;
		cursor: pointer;
	}

	.customize-builder-reflector-control .widget-reflector:hover {
		background-color: #f7f7f7;
	}
	
	.customize-builder-reflector-control .widget-reflector + .widget-reflector {
		margin-top: 12px;
	}

	#customize-theme-controls .blogzee-builder .customize-control-builder,
	#customize-theme-controls .blogzee-builder .customize-control-responsive-builder {
		display: none;
	}

	#customize-theme-controls .blogzee-builder.is-active .customize-control-builder.is-active,
	#customize-theme-controls .blogzee-builder .customize-control-responsive-builder.is-active {
		position: fixed;
		bottom: 0;
		left: 18%;
		right: 0;
		transform: translate3d(0,100%,0);
		transition: all 0.2s;
		width: auto;
		visibility: hidden;
		content-visibility: auto;
		display: block;
	}

	.collapsed #customize-theme-controls .blogzee-builder.is-active .customize-control-builder.is-active,
	.collapsed #customize-theme-controls .blogzee-builder .customize-control-responsive-builder.is-active {
		left: 0;
	}

	#customize-theme-controls .open ~ .blogzee-builder.is-active .customize-control-builder.is-active,
	#customize-theme-controls .open ~ .blogzee-builder .customize-control-responsive-builder.is-active {
		transform: translate3d(0, 0, 0);
		visibility: visible;
	}

	@media (max-width: 1660px) {
		#customize-theme-controls .open ~ .blogzee-builder.is-active .customize-control-builder.is-active,
		#customize-theme-controls .open ~ .blogzee-builder .customize-control-responsive-builder.is-active {
			left: 300px;
		}

		.blogzee-header-builder-popover {
			width: 22%;
		}
	}

	.blogzee-builder .row {
		display: flex;
		align-items: center;
		-webkit-align-items: center;
	}

	.blogzee-builder .rows-wrapper .row-settings {
		margin-right: 10px;
	}

	.blogzee-builder .rows-wrapper .row-settings .dashicon {
		padding: 15px 8px;
		border: 1px solid #d9d9d9;
		background: #f3f4f6;
		cursor: pointer;
		border-radius: 2px;
		transition: none;
	}

	.blogzee-builder .rows-wrapper .row-settings.is-active .dashicon {
		background: #5fa1ff;
		color: #fff;
		border-color: #5fa1ff;
	}

	.blogzee-builder .column-wrapper {
		height: 50px;
		display: grid;
		gap: 4px;
		flex: 1;
	}

	/* column 2 */
	.blogzee-builder .column-2.layout-one .column-wrapper {
		grid-template-columns: repeat(2, 1fr);
	}

	.blogzee-builder .column-2.layout-two .column-wrapper {
        grid-template-columns: 4fr 1fr;
    }

    .blogzee-builder .column-2.layout-three .column-wrapper {
        grid-template-columns: 1fr 4fr;
    }

	.blogzee-builder .layout-four.column-2 .column-wrapper {
		grid-template-columns: repeat(2, 1fr);
	}

	/* column three */
    .blogzee-builder .column-3.layout-one .column-wrapper {
        grid-template-columns: repeat(3, 1fr);
    }

    .blogzee-builder .column-3.layout-two .column-wrapper {
        grid-template-columns: 3fr 1fr 1fr;
    }

    .blogzee-builder .column-3.layout-three .column-wrapper {
        grid-template-columns: 1fr 1fr 3fr;
    }

    .blogzee-builder .column-3.layout-four .column-wrapper {
        grid-template-columns: 1fr 3fr 1fr;
    }

    .blogzee-builder .column-3.layout-five .column-wrapper,
    .blogzee-builder .column-3.layout-six .column-wrapper,
	.blogzee-builder .column-3.layout-seven .column-wrapper {
        grid-template-columns: 1fr 1fr 1fr;
    }

	/* column four */
    .blogzee-builder .column-4.layout-one .column-wrapper {
        grid-template-columns: repeat(4, 1fr);
    }

    .blogzee-builder .column-4.layout-two .column-wrapper {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }

    .blogzee-builder .column-4.layout-three .column-wrapper {
        grid-template-columns: 1fr 1fr 1fr 2fr;
    }

    .blogzee-builder .column-4.layout-four .column-wrapper {
        grid-template-columns: 1fr 2fr 1fr 1fr;
    }

	.blogzee-builder .column-4.layout-five .column-wrapper {
        grid-template-columns: repeat(4, 1fr);
    }

	.blogzee-builder .column-4.layout-six .column-wrapper {
        grid-template-columns: repeat(4, 1fr);
    }

	.blogzee-builder .builder-wrapper {
		background: #eee;
		padding: 20px;
	}

	.blogzee-builder .rows-wrapper {
		display: grid;
		gap: 15px;
	}

	.blogzee-builder .rows-wrapper .components-dropdown {
		width: inherit;
		background-color: #F3F4F6;
		border: 1px solid #e1e1e1;
		cursor: pointer;
		border-radius: 3px;
		transition: all .25s cubic-bezier(.02,.01,.47,1);
		flex: 1;
	}

	.blogzee-builder .rows-wrapper .components-dropdown:hover {
		box-shadow: 0px 0px 12px 0px rgb(0 0 0 / 8%);
		background-color: #f8f8f9;
	}

	.blogzee-builder .components-dropdown .column {
		display: flex;
		gap: 7px;
		align-items: center;
		-webkit-align-items: center;
		justify-content: center;
		-webkit-justify-content: center;
		opacity: 1 !important;
		padding: 10px;
		height: 28px;
		position: relative;
	}
	
	.blogzee-builder .components-dropdown .column.no-children:before {
		content: '+';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		font-size: 18px;
	}

	/* .customize-builder-control .column-3 .components-dropdown:nth-child(2) .column.has-children {
		justify-content: center;
		-webkit-justify-content: center;
	}

	.customize-builder-control .column-3 .components-dropdown:last-child .column.has-children,
	.customize-builder-control .column-2 .components-dropdown:last-child .column.has-children {
		justify-content: flex-end;
		-webkit-justify-content: flex-end;
	} */

	.customize-builder-control .plus-icon {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		color: #222;
		font-size: 22px;
	}

	/* responsive */
	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active {
		grid-template-columns: 1fr 6fr;
		grid-template-rows: repeat(3, 1fr);
	}

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .row {
		order: 2;
	}

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas {
		grid-row: span 3;
		grid-column: span 1;
		flex-direction: column;
		order: 1;
	}

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas .row-settings {
		display: flex;
		gap: 7px;
		box-sizing: border-box;
		padding: 8px 15px;
		width: 100%;
		height: 35px;
		border-bottom: none;
		border-radius: 0;
		border: 1px solid #e1e1e1;
		background: #f3f4f6;
		align-items: center;
		margin: 0;
		cursor: pointer;
	}

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas .row-settings .dashicon {
		padding: 0;
		border: none;
	}

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas .row-settings span:last-child {
		text-transform: uppercase;
		font-weight: 500;
		font-size: 11px;
		letter-spacing: 0.3px;
		line-height: 12px;
	}

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas .column-wrapper {
		width: 100%;
	}

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas .column-wrapper .components-dropdown {
		width: initial;
	}

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas .components-dropdown .column {
		flex-direction: column;
		height: 123px;
	    overflow-y: auto;
		justify-content: flex-start;
		-webkit-justify-content: flex-start;
	}	

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas .components-dropdown .column .column-item {
		width: 100%;
		padding: 14px;
		box-sizing: border-box;
	}

	/* scrollbar */
	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas .components-dropdown .column::-webkit-scrollbar {
		width: 2px;
	}

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas .components-dropdown .column::-webkit-scrollbar-thumb {
		background-color: #5fa1ff;
		border-radius: 40px;
	}

	.customize-responsive-builder-control .rows-wrapper.mobile-canvas--active .mobile-canvas .components-dropdown .column::-webkit-scrollbar-track {
		box-shadow: inset 0 0 5px #5fa1ffba;
		border-radius: 40px;
	}

	/* notice */
	.builder-footer-wrapper {
		/* background-color: #4e98ff; */
		display: flex;
		align-items: center;
		-webkit-align-items: center;
		justify-content: space-between;
		-webkit-justify-content: space-between;
		background: linear-gradient(to right, rgb(225 42 177), rgb(241, 101, 41));
	}

	.builder-footer-wrapper .upgrade-notice-wrapper {
		padding: 6px 20px;
	}

	.builder-footer-wrapper .upgrade-notice-wrapper .upgrade-notice {
		color: #fff;
	}

	.builder-footer-wrapper .upgrade-notice-wrapper .components-button {
		height: initial;
		padding: 0 11px 2px;
		line-height: 26px;
		background: #ffffffe8;
		color: #5fa1ff;
		margin-left: 20px;
		letter-spacing: 0.2px;
	}

	.builder-footer-wrapper .upgrade-notice-wrapper .components-button.is-primary:hover:not(:disabled):hover {
		color: #5fa1ff;
    	background: #ededed;
	}

	.builder-footer-wrapper .show-hide-wrapper .dashicon {
		width: initial;
		height: initial;
		font-size: 13px;
		position: relative;
		padding: 18px 20px;
		color: #fff;
		cursor: pointer;
		font-family: inherit;
	}

	.builder-footer-wrapper .show-hide-wrapper .dashicon:before {
		font-family: 'dashicons';
		padding-right: 7px;
	}

	.builder-footer-wrapper .show-hide-wrapper .dashicon:after {
		content: '';
		width: 1px;
		height: 17px;
		display: inline-block;
		position: absolute;
		top: 16px;
		left: 0;
		background: #fff;
	}

	/* builder collapse */
	.blogzee-builder-collapsed .builder-wrapper {
		margin-bottom: -220px;
	}

	.blogzee-builder-collapsed .builder-footer-wrapper {
		position: relative;
		z-index: 1;
	}

	.blogzee-builder-collapsed ~ #customize-preview {
		bottom: 49px;
		transition: none;
	}

	/* responsive */
	.preview-tablet .wp-full-overlay-main.blogzee-builder--on {
		max-height: 1080px;
		height: initial;
	}

	.preview-mobile .wp-full-overlay-main.blogzee-builder--on {
		max-height: 480px;
		height: initial;
	}