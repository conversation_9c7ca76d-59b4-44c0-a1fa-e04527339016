/**
 * Modern Theme JavaScript functionality
 * Dakoii Provincial Government Theme - Enhanced with Fluid Animations
 */

(function() {
    'use strict';

    // Modern animation configuration
    const ANIMATION_CONFIG = {
        duration: {
            fast: 150,
            base: 300,
            slow: 500,
            bounce: 400
        },
        easing: {
            smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
            bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
            elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
        }
    };

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {

        // Initialize all modern features
        initMobileMenu();
        initSmoothScrolling();
        initBackToTop();
        initAnimationDelays();
        initSearchEnhancements();
        initCommentFormEnhancements();
        initCulturalAnimations();
        initScrollAnimations();
        initParallaxEffects();
        initLoadingAnimations();
        initModernInteractions();
    });

    /**
     * Enhanced Mobile menu functionality with modern animations
     */
    function initMobileMenu() {
        const menuToggle = document.querySelector('.menu-toggle');
        const navMenu = document.querySelector('.nav-menu');
        const body = document.body;

        if (menuToggle && navMenu) {
            // Add modern menu toggle animation
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                const expanded = this.getAttribute('aria-expanded') === 'true' || false;

                // Add loading animation to button
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, ANIMATION_CONFIG.duration.fast);

                // Toggle aria-expanded
                this.setAttribute('aria-expanded', !expanded);

                // Toggle menu visibility with animation
                if (!expanded) {
                    navMenu.classList.add('is-open');
                    body.classList.add('menu-open');
                    animateMenuItems(navMenu, 'in');
                } else {
                    animateMenuItems(navMenu, 'out');
                    setTimeout(() => {
                        navMenu.classList.remove('is-open');
                        body.classList.remove('menu-open');
                    }, ANIMATION_CONFIG.duration.base);
                }

                // Animate menu icon
                animateMenuIcon(this, !expanded);
            });

            // Close menu when clicking outside with animation
            document.addEventListener('click', function(e) {
                if (!menuToggle.contains(e.target) && !navMenu.contains(e.target) && navMenu.classList.contains('is-open')) {
                    closeMenuWithAnimation();
                }
            });

            // Close menu on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && navMenu.classList.contains('is-open')) {
                    closeMenuWithAnimation();
                    menuToggle.focus();
                }
            });

            // Add smooth transitions to menu items
            const menuItems = navMenu.querySelectorAll('.nav-item a');
            menuItems.forEach((item, index) => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(8px) scale(1.02)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                });
            });
        }

        function closeMenuWithAnimation() {
            menuToggle.setAttribute('aria-expanded', 'false');
            animateMenuItems(navMenu, 'out');
            animateMenuIcon(menuToggle, false);
            setTimeout(() => {
                navMenu.classList.remove('is-open');
                body.classList.remove('menu-open');
            }, ANIMATION_CONFIG.duration.base);
        }

        function animateMenuIcon(button, isOpen) {
            const icon = button.querySelector('.menu-icon') || button;
            icon.style.transform = 'rotate(180deg) scale(0.8)';
            setTimeout(() => {
                icon.textContent = isOpen ? '✕' : '☰';
                icon.style.transform = 'rotate(0deg) scale(1)';
            }, ANIMATION_CONFIG.duration.fast);
        }

        function animateMenuItems(menu, direction) {
            const items = menu.querySelectorAll('.nav-item');
            items.forEach((item, index) => {
                const delay = index * 50;
                setTimeout(() => {
                    if (direction === 'in') {
                        item.style.opacity = '0';
                        item.style.transform = 'translateY(-20px)';
                        setTimeout(() => {
                            item.style.transition = `all ${ANIMATION_CONFIG.duration.base}ms ${ANIMATION_CONFIG.easing.bounce}`;
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, 10);
                    } else {
                        item.style.transition = `all ${ANIMATION_CONFIG.duration.base}ms ${ANIMATION_CONFIG.easing.smooth}`;
                        item.style.opacity = '0';
                        item.style.transform = 'translateY(-10px)';
                    }
                }, delay);
            });
        }
    }

    /**
     * Smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        
        anchorLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement && targetId !== '#') {
                    e.preventDefault();
                    
                    const headerHeight = document.querySelector('.site-header').offsetHeight;
                    const navHeight = document.querySelector('.main-nav').offsetHeight;
                    const offset = headerHeight + navHeight + 20;
                    
                    const targetPosition = targetElement.offsetTop - offset;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                    
                    // Update focus for accessibility
                    targetElement.focus();
                }
            });
        });
    }

    /**
     * Back to top button functionality
     */
    function initBackToTop() {
        const backToTop = document.getElementById('back-to-top');
        
        if (backToTop) {
            // Show/hide based on scroll position
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTop.style.display = 'block';
                    setTimeout(function() {
                        backToTop.style.opacity = '1';
                    }, 10);
                } else {
                    backToTop.style.opacity = '0';
                    setTimeout(function() {
                        if (window.pageYOffset <= 300) {
                            backToTop.style.display = 'none';
                        }
                    }, 300);
                }
            });
            
            // Smooth scroll to top
            backToTop.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    }

    /**
     * Animation delays for staggered entrance effects
     */
    function initAnimationDelays() {
        const animatedElements = document.querySelectorAll('.post, .widget, article, .related-post');
        
        animatedElements.forEach(function(element, index) {
            element.style.animationDelay = (index * 0.1) + 's';
        });
    }

    /**
     * Search form enhancements
     */
    function initSearchEnhancements() {
        const searchForms = document.querySelectorAll('.search-form');
        
        searchForms.forEach(function(form) {
            const searchInput = form.querySelector('.search-field');
            const searchSubmit = form.querySelector('.search-submit');
            
            if (searchInput && searchSubmit) {
                // Add loading state on form submission
                form.addEventListener('submit', function() {
                    searchSubmit.disabled = true;
                    searchSubmit.value = 'Searching...';
                });
                
                // Clear button functionality
                if (searchInput.value) {
                    addClearButton(searchInput);
                }
                
                searchInput.addEventListener('input', function() {
                    if (this.value) {
                        addClearButton(this);
                    } else {
                        removeClearButton(this);
                    }
                });
            }
        });
    }

    /**
     * Add clear button to search input
     */
    function addClearButton(input) {
        if (input.parentNode.querySelector('.search-clear')) {
            return;
        }
        
        const clearButton = document.createElement('button');
        clearButton.type = 'button';
        clearButton.className = 'search-clear';
        clearButton.innerHTML = '✕';
        clearButton.title = 'Clear search';
        
        clearButton.addEventListener('click', function() {
            input.value = '';
            input.focus();
            removeClearButton(input);
        });
        
        input.parentNode.appendChild(clearButton);
    }

    /**
     * Remove clear button from search input
     */
    function removeClearButton(input) {
        const clearButton = input.parentNode.querySelector('.search-clear');
        if (clearButton) {
            clearButton.remove();
        }
    }

    /**
     * Comment form enhancements
     */
    function initCommentFormEnhancements() {
        const commentForm = document.getElementById('commentform');
        
        if (commentForm) {
            const requiredFields = commentForm.querySelectorAll('[required]');
            
            // Add visual indicators for required fields
            requiredFields.forEach(function(field) {
                const label = commentForm.querySelector('label[for="' + field.id + '"]');
                if (label && !label.querySelector('.required-indicator')) {
                    const indicator = document.createElement('span');
                    indicator.className = 'required-indicator';
                    indicator.innerHTML = ' *';
                    indicator.style.color = 'var(--png-red)';
                    label.appendChild(indicator);
                }
            });
            
            // Form validation feedback
            commentForm.addEventListener('submit', function(e) {
                let isValid = true;
                
                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        field.style.borderColor = 'var(--png-red)';
                        isValid = false;
                    } else {
                        field.style.borderColor = '';
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    const firstInvalidField = commentForm.querySelector('[style*="border-color"]');
                    if (firstInvalidField) {
                        firstInvalidField.focus();
                    }
                }
            });
        }
    }

    /**
     * Cultural pattern animations and effects
     */
    function initCulturalAnimations() {
        // Add hover effects to cultural patterns
        const culturalPatterns = document.querySelectorAll('.cultural-pattern');
        
        culturalPatterns.forEach(function(pattern) {
            pattern.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.transition = 'transform 0.3s ease';
            });
            
            pattern.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
        
        // Animate logo icon on scroll
        const logoIcon = document.querySelector('.logo-icon');
        if (logoIcon) {
            let lastScrollY = window.pageYOffset;
            
            window.addEventListener('scroll', function() {
                const currentScrollY = window.pageYOffset;
                
                if (currentScrollY > lastScrollY) {
                    // Scrolling down
                    logoIcon.style.transform = 'rotate(5deg)';
                } else {
                    // Scrolling up
                    logoIcon.style.transform = 'rotate(-5deg)';
                }
                
                setTimeout(function() {
                    logoIcon.style.transform = 'rotate(0deg)';
                }, 200);
                
                lastScrollY = currentScrollY;
            });
        }
    }

    /**
     * Intersection Observer for fade-in animations
     */
    function initIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });
            
            const elementsToObserve = document.querySelectorAll('.widget, .post, article');
            elementsToObserve.forEach(function(element) {
                observer.observe(element);
            });
        }
    }

    // Initialize intersection observer
    initIntersectionObserver();

    /**
     * Modern scroll-triggered animations
     */
    function initScrollAnimations() {
        if ('IntersectionObserver' in window) {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        element.style.opacity = '0';
                        element.style.transform = 'translateY(30px)';

                        setTimeout(() => {
                            element.style.transition = `all ${ANIMATION_CONFIG.duration.slow}ms ${ANIMATION_CONFIG.easing.smooth}`;
                            element.style.opacity = '1';
                            element.style.transform = 'translateY(0)';
                        }, 100);

                        observer.unobserve(element);
                    }
                });
            }, observerOptions);

            // Observe elements for scroll animations
            const elementsToAnimate = document.querySelectorAll('.widget, .post, article, .entry-content p, h1, h2, h3');
            elementsToAnimate.forEach(el => observer.observe(el));
        }
    }

    /**
     * Subtle parallax effects
     */
    function initParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.header-banner-image, .cultural-pattern');

        if (parallaxElements.length > 0) {
            let ticking = false;

            function updateParallax() {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;

                parallaxElements.forEach(element => {
                    element.style.transform = `translateY(${rate}px)`;
                });

                ticking = false;
            }

            function requestTick() {
                if (!ticking) {
                    requestAnimationFrame(updateParallax);
                    ticking = true;
                }
            }

            window.addEventListener('scroll', requestTick);
        }
    }

    /**
     * Loading animations for page elements
     */
    function initLoadingAnimations() {
        // Add loading animation to images
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.complete) {
                img.style.opacity = '0';
                img.style.filter = 'blur(5px)';

                img.addEventListener('load', function() {
                    this.style.transition = `all ${ANIMATION_CONFIG.duration.slow}ms ${ANIMATION_CONFIG.easing.smooth}`;
                    this.style.opacity = '1';
                    this.style.filter = 'blur(0px)';
                });
            }
        });

        // Add staggered animation to cards
        const cards = document.querySelectorAll('.article-card, .widget, .post');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = `all ${ANIMATION_CONFIG.duration.base}ms ${ANIMATION_CONFIG.easing.smooth}`;
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100 + 200);
        });
    }

    /**
     * Modern interactive elements
     */
    function initModernInteractions() {
        // Enhanced button interactions
        const buttons = document.querySelectorAll('button, .btn, input[type="submit"], .read-more');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.02)';
                this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.3)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });

            button.addEventListener('mousedown', function() {
                this.style.transform = 'translateY(0) scale(0.98)';
            });

            button.addEventListener('mouseup', function() {
                this.style.transform = 'translateY(-2px) scale(1.02)';
            });
        });

        // Enhanced link interactions
        const links = document.querySelectorAll('a:not(.nav-item a)');
        links.forEach(link => {
            link.addEventListener('mouseenter', function() {
                if (!this.querySelector('img')) {
                    this.style.transform = 'translateY(-1px)';
                }
            });

            link.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });
        });

        // Add ripple effect to clickable elements
        const clickableElements = document.querySelectorAll('button, .btn, .nav-item a');
        clickableElements.forEach(element => {
            element.addEventListener('click', createRippleEffect);
        });
    }

    /**
     * Create ripple effect on click
     */
    function createRippleEffect(e) {
        const button = e.currentTarget;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple ${ANIMATION_CONFIG.duration.base}ms ease-out;
            pointer-events: none;
        `;

        // Add ripple animation keyframes if not already added
        if (!document.querySelector('#ripple-styles')) {
            const style = document.createElement('style');
            style.id = 'ripple-styles';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, ANIMATION_CONFIG.duration.base);
    }

})();
