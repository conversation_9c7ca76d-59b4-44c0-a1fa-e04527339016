!function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=73)}([function(e,t,r){"use strict";e.exports=r(8)},function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return o})),r.d(t,"d",(function(){return s})),r.d(t,"e",(function(){return l}));const{__:n}=wp.i18n,i=n("Thank you for installing [theme] — a fast, flexible, and user-friendly theme for blogs, news sites, and magazines. Get started quickly with our 100+ one-click starter sites — no coding needed.","morenews"),a="/admin-dashboard/assets/images/af-themes-logo.png",o=[{linkeText:n("Unlock Pro Features","morenews"),textUrl:"https://afthemes.com/products/morenews-pro/"},{linkeText:n("Docs","morenews"),textUrl:"https://docs.afthemes.com/morenews/"},{linkeText:n("Need Help?","morenews"),textUrl:"https://afthemes.com/supports/"},{linkeText:n("Rate This Theme","morenews"),textUrl:"https://wordpress.org/support/theme/morenews/reviews/?filter=5"}],s=n("Upgrade to Pro","morenews"),l="https://afthemes.com/products/morenews-pro/";n("Need Help?","morenews")},function(e,t,r){var n=r(14),i=r(16);function a(t,r){return delete e.exports[t],e.exports[t]=r,r}e.exports={Parser:n,Tokenizer:r(15),ElementType:r(5),DomHandler:i,get FeedHandler(){return a("FeedHandler",r(36))},get Stream(){return a("Stream",r(50))},get WritableStream(){return a("WritableStream",r(23))},get ProxyHandler(){return a("ProxyHandler",r(58))},get DomUtils(){return a("DomUtils",r(18))},get CollectingHandler(){return a("CollectingHandler",r(59))},DefaultHandler:i,get RssHandler(){return a("RssHandler",this.FeedHandler)},parseDOM:function(e,t){var r=new i(t);return new n(r,t).end(e),r.dom},parseFeed:function(t,r){var i=new e.exports.FeedHandler(r);return new n(i,r).end(t),i.dom},createDomStream:function(e,t,r){var a=new i(e,t,r);return new n(a,t)},EVENTS:{attribute:2,cdatastart:0,cdataend:0,text:1,processinginstruction:2,comment:1,commentend:0,closetag:1,opentag:2,opentagname:1,error:1,end:0}}},function(e,t,r){"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE){0;try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}}(),e.exports=r(9)},function(e,t,r){"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var n=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable;function o(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},r=0;r<10;r++)t["_"+String.fromCharCode(r)]=r;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach((function(e){n[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(e){return!1}}()?Object.assign:function(e,t){for(var r,s,l=o(e),u=1;u<arguments.length;u++){for(var c in r=Object(arguments[u]))i.call(r,c)&&(l[c]=r[c]);if(n){s=n(r);for(var f=0;f<s.length;f++)a.call(r,s[f])&&(l[s[f]]=r[s[f]])}}return l}},function(e,t){e.exports={Text:"text",Directive:"directive",Comment:"comment",Script:"script",Style:"style",Tag:"tag",CDATA:"cdata",Doctype:"doctype",isTag:function(e){return"tag"===e.type||"script"===e.type||"style"===e.type}}},function(e,t){"function"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.htmlparser2=t.convertNodeToElement=t.processNodes=void 0;var n=r(12);Object.defineProperty(t,"processNodes",{enumerable:!0,get:function(){return s(n).default}});var i=r(13);Object.defineProperty(t,"convertNodeToElement",{enumerable:!0,get:function(){return s(i).default}});var a=r(2);Object.defineProperty(t,"htmlparser2",{enumerable:!0,get:function(){return s(a).default}});var o=s(r(69));function s(e){return e&&e.__esModule?e:{default:e}}t.default=o.default},function(e,t,r){"use strict";
/** @license React v16.14.0
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(4),i="function"==typeof Symbol&&Symbol.for,a=i?Symbol.for("react.element"):60103,o=i?Symbol.for("react.portal"):60106,s=i?Symbol.for("react.fragment"):60107,l=i?Symbol.for("react.strict_mode"):60108,u=i?Symbol.for("react.profiler"):60114,c=i?Symbol.for("react.provider"):60109,f=i?Symbol.for("react.context"):60110,p=i?Symbol.for("react.forward_ref"):60112,d=i?Symbol.for("react.suspense"):60113,h=i?Symbol.for("react.memo"):60115,m=i?Symbol.for("react.lazy"):60116,g="function"==typeof Symbol&&Symbol.iterator;function y(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var b={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},v={};function w(e,t,r){this.props=e,this.context=t,this.refs=v,this.updater=r||b}function _(){}function x(e,t,r){this.props=e,this.context=t,this.refs=v,this.updater=r||b}w.prototype.isReactComponent={},w.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error(y(85));this.updater.enqueueSetState(this,e,t,"setState")},w.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},_.prototype=w.prototype;var E=x.prototype=new _;E.constructor=x,n(E,w.prototype),E.isPureReactComponent=!0;var S={current:null},k=Object.prototype.hasOwnProperty,T={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var n,i={},o=null,s=null;if(null!=t)for(n in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(o=""+t.key),t)k.call(t,n)&&!T.hasOwnProperty(n)&&(i[n]=t[n]);var l=arguments.length-2;if(1===l)i.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(n in l=e.defaultProps)void 0===i[n]&&(i[n]=l[n]);return{$$typeof:a,type:e,key:o,ref:s,props:i,_owner:S.current}}function A(e){return"object"==typeof e&&null!==e&&e.$$typeof===a}var L=/\/+/g,N=[];function P(e,t,r,n){if(N.length){var i=N.pop();return i.result=e,i.keyPrefix=t,i.func=r,i.context=n,i.count=0,i}return{result:e,keyPrefix:t,func:r,context:n,count:0}}function R(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>N.length&&N.push(e)}function D(e,t,r){return null==e?0:function e(t,r,n,i){var s=typeof t;"undefined"!==s&&"boolean"!==s||(t=null);var l=!1;if(null===t)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(t.$$typeof){case a:case o:l=!0}}if(l)return n(i,t,""===r?"."+q(t,0):r),1;if(l=0,r=""===r?".":r+":",Array.isArray(t))for(var u=0;u<t.length;u++){var c=r+q(s=t[u],u);l+=e(s,c,n,i)}else if(null===t||"object"!=typeof t?c=null:c="function"==typeof(c=g&&t[g]||t["@@iterator"])?c:null,"function"==typeof c)for(t=c.call(t),u=0;!(s=t.next()).done;)l+=e(s=s.value,c=r+q(s,u++),n,i);else if("object"===s)throw n=""+t,Error(y(31,"[object Object]"===n?"object with keys {"+Object.keys(t).join(", ")+"}":n,""));return l}(e,"",t,r)}function q(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,(function(e){return t[e]}))}(e.key):t.toString(36)}function M(e,t){e.func.call(e.context,t,e.count++)}function O(e,t,r){var n=e.result,i=e.keyPrefix;e=e.func.call(e.context,t,e.count++),Array.isArray(e)?I(e,n,r,(function(e){return e})):null!=e&&(A(e)&&(e=function(e,t){return{$$typeof:a,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(e,i+(!e.key||t&&t.key===e.key?"":(""+e.key).replace(L,"$&/")+"/")+r)),n.push(e))}function I(e,t,r,n,i){var a="";null!=r&&(a=(""+r).replace(L,"$&/")+"/"),D(e,O,t=P(t,a,n,i)),R(t)}var H={current:null};function B(){var e=H.current;if(null===e)throw Error(y(321));return e}var U={ReactCurrentDispatcher:H,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:S,IsSomeRendererActing:{current:!1},assign:n};t.Children={map:function(e,t,r){if(null==e)return e;var n=[];return I(e,n,null,t,r),n},forEach:function(e,t,r){if(null==e)return e;D(e,M,t=P(null,null,t,r)),R(t)},count:function(e){return D(e,(function(){return null}),null)},toArray:function(e){var t=[];return I(e,t,null,(function(e){return e})),t},only:function(e){if(!A(e))throw Error(y(143));return e}},t.Component=w,t.Fragment=s,t.Profiler=u,t.PureComponent=x,t.StrictMode=l,t.Suspense=d,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=U,t.cloneElement=function(e,t,r){if(null==e)throw Error(y(267,e));var i=n({},e.props),o=e.key,s=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,l=S.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(c in t)k.call(t,c)&&!T.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==u?u[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=r;else if(1<c){u=Array(c);for(var f=0;f<c;f++)u[f]=arguments[f+2];i.children=u}return{$$typeof:a,type:e.type,key:o,ref:s,props:i,_owner:l}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:f,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:c,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:p,render:e}},t.isValidElement=A,t.lazy=function(e){return{$$typeof:m,_ctor:e,_status:-1,_result:null}},t.memo=function(e,t){return{$$typeof:h,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return B().useCallback(e,t)},t.useContext=function(e,t){return B().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return B().useEffect(e,t)},t.useImperativeHandle=function(e,t,r){return B().useImperativeHandle(e,t,r)},t.useLayoutEffect=function(e,t){return B().useLayoutEffect(e,t)},t.useMemo=function(e,t){return B().useMemo(e,t)},t.useReducer=function(e,t,r){return B().useReducer(e,t,r)},t.useRef=function(e){return B().useRef(e)},t.useState=function(e){return B().useState(e)},t.version="16.14.0"},function(e,t,r){"use strict";
/** @license React v16.14.0
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(0),i=r(4),a=r(10);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}if(!n)throw Error(o(227));function s(e,t,r,n,i,a,o,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(r,u)}catch(e){this.onError(e)}}var l=!1,u=null,c=!1,f=null,p={onError:function(e){l=!0,u=e}};function d(e,t,r,n,i,a,o,c,f){l=!1,u=null,s.apply(p,arguments)}var h=null,m=null,g=null;function y(e,t,r){var n=e.type||"unknown-event";e.currentTarget=g(r),function(e,t,r,n,i,a,s,p,h){if(d.apply(this,arguments),l){if(!l)throw Error(o(198));var m=u;l=!1,u=null,c||(c=!0,f=m)}}(n,t,void 0,e),e.currentTarget=null}var b=null,v={};function w(){if(b)for(var e in v){var t=v[e],r=b.indexOf(e);if(!(-1<r))throw Error(o(96,e));if(!x[r]){if(!t.extractEvents)throw Error(o(97,e));for(var n in x[r]=t,r=t.eventTypes){var i=void 0,a=r[n],s=t,l=n;if(E.hasOwnProperty(l))throw Error(o(99,l));E[l]=a;var u=a.phasedRegistrationNames;if(u){for(i in u)u.hasOwnProperty(i)&&_(u[i],s,l);i=!0}else a.registrationName?(_(a.registrationName,s,l),i=!0):i=!1;if(!i)throw Error(o(98,n,e))}}}}function _(e,t,r){if(S[e])throw Error(o(100,e));S[e]=t,k[e]=t.eventTypes[r].dependencies}var x=[],E={},S={},k={};function T(e){var t,r=!1;for(t in e)if(e.hasOwnProperty(t)){var n=e[t];if(!v.hasOwnProperty(t)||v[t]!==n){if(v[t])throw Error(o(102,t));v[t]=n,r=!0}}r&&w()}var C=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),A=null,L=null,N=null;function P(e){if(e=m(e)){if("function"!=typeof A)throw Error(o(280));var t=e.stateNode;t&&(t=h(t),A(e.stateNode,e.type,t))}}function R(e){L?N?N.push(e):N=[e]:L=e}function D(){if(L){var e=L,t=N;if(N=L=null,P(e),t)for(e=0;e<t.length;e++)P(t[e])}}function q(e,t){return e(t)}function M(e,t,r,n,i){return e(t,r,n,i)}function O(){}var I=q,H=!1,B=!1;function U(){null===L&&null===N||(O(),D())}function V(e,t,r){if(B)return e(t,r);B=!0;try{return I(e,t,r)}finally{B=!1,U()}}var z=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,F=Object.prototype.hasOwnProperty,j={},Z={};function G(e,t,r,n,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=n,this.attributeNamespace=i,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=a}var W={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){W[e]=new G(e,0,!1,e,null,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];W[t]=new G(t,1,!1,e[1],null,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){W[e]=new G(e,2,!1,e.toLowerCase(),null,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){W[e]=new G(e,2,!1,e,null,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){W[e]=new G(e,3,!1,e.toLowerCase(),null,!1)})),["checked","multiple","muted","selected"].forEach((function(e){W[e]=new G(e,3,!0,e,null,!1)})),["capture","download"].forEach((function(e){W[e]=new G(e,4,!1,e,null,!1)})),["cols","rows","size","span"].forEach((function(e){W[e]=new G(e,6,!1,e,null,!1)})),["rowSpan","start"].forEach((function(e){W[e]=new G(e,5,!1,e.toLowerCase(),null,!1)}));var Y=/[\-:]([a-z])/g;function Q(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(Y,Q);W[t]=new G(t,1,!1,e,null,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(Y,Q);W[t]=new G(t,1,!1,e,"http://www.w3.org/1999/xlink",!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(Y,Q);W[t]=new G(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1)})),["tabIndex","crossOrigin"].forEach((function(e){W[e]=new G(e,1,!1,e.toLowerCase(),null,!1)})),W.xlinkHref=new G("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0),["src","href","action","formAction"].forEach((function(e){W[e]=new G(e,1,!1,e.toLowerCase(),null,!0)}));var $=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function K(e,t,r,n){var i=W.hasOwnProperty(t)?W[t]:null;(null!==i?0===i.type:!n&&(2<t.length&&("o"===t[0]||"O"===t[0])&&("n"===t[1]||"N"===t[1])))||(function(e,t,r,n){if(null==t||function(e,t,r,n){if(null!==r&&0===r.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!n&&(null!==r?!r.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,r,n))return!0;if(n)return!1;if(null!==r)switch(r.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,r,i,n)&&(r=null),n||null===i?function(e){return!!F.call(Z,e)||!F.call(j,e)&&(z.test(e)?Z[e]=!0:(j[e]=!0,!1))}(t)&&(null===r?e.removeAttribute(t):e.setAttribute(t,""+r)):i.mustUseProperty?e[i.propertyName]=null===r?3!==i.type&&"":r:(t=i.attributeName,n=i.attributeNamespace,null===r?e.removeAttribute(t):(r=3===(i=i.type)||4===i&&!0===r?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}$.hasOwnProperty("ReactCurrentDispatcher")||($.ReactCurrentDispatcher={current:null}),$.hasOwnProperty("ReactCurrentBatchConfig")||($.ReactCurrentBatchConfig={suspense:null});var X=/^(.*)[\\\/]/,J="function"==typeof Symbol&&Symbol.for,ee=J?Symbol.for("react.element"):60103,te=J?Symbol.for("react.portal"):60106,re=J?Symbol.for("react.fragment"):60107,ne=J?Symbol.for("react.strict_mode"):60108,ie=J?Symbol.for("react.profiler"):60114,ae=J?Symbol.for("react.provider"):60109,oe=J?Symbol.for("react.context"):60110,se=J?Symbol.for("react.concurrent_mode"):60111,le=J?Symbol.for("react.forward_ref"):60112,ue=J?Symbol.for("react.suspense"):60113,ce=J?Symbol.for("react.suspense_list"):60120,fe=J?Symbol.for("react.memo"):60115,pe=J?Symbol.for("react.lazy"):60116,de=J?Symbol.for("react.block"):60121,he="function"==typeof Symbol&&Symbol.iterator;function me(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=he&&e[he]||e["@@iterator"])?e:null}function ge(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case re:return"Fragment";case te:return"Portal";case ie:return"Profiler";case ne:return"StrictMode";case ue:return"Suspense";case ce:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case oe:return"Context.Consumer";case ae:return"Context.Provider";case le:var t=e.render;return t=t.displayName||t.name||"",e.displayName||(""!==t?"ForwardRef("+t+")":"ForwardRef");case fe:return ge(e.type);case de:return ge(e.render);case pe:if(e=1===e._status?e._result:null)return ge(e)}return null}function ye(e){var t="";do{e:switch(e.tag){case 3:case 4:case 6:case 7:case 10:case 9:var r="";break e;default:var n=e._debugOwner,i=e._debugSource,a=ge(e.type);r=null,n&&(r=ge(n.type)),n=a,a="",i?a=" (at "+i.fileName.replace(X,"")+":"+i.lineNumber+")":r&&(a=" (created by "+r+")"),r="\n    in "+(n||"Unknown")+a}t+=r,e=e.return}while(e);return t}function be(e){switch(typeof e){case"boolean":case"number":case"object":case"string":case"undefined":return e;default:return""}}function ve(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function we(e){e._valueTracker||(e._valueTracker=function(e){var t=ve(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==r&&"function"==typeof r.get&&"function"==typeof r.set){var i=r.get,a=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){n=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(e){n=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function _e(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=ve(e)?e.checked?"true":"false":e.value),(e=n)!==r&&(t.setValue(e),!0)}function xe(e,t){var r=t.checked;return i({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=r?r:e._wrapperState.initialChecked})}function Ee(e,t){var r=null==t.defaultValue?"":t.defaultValue,n=null!=t.checked?t.checked:t.defaultChecked;r=be(null!=t.value?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Se(e,t){null!=(t=t.checked)&&K(e,"checked",t,!1)}function ke(e,t){Se(e,t);var r=be(t.value),n=t.type;if(null!=r)"number"===n?(0===r&&""===e.value||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if("submit"===n||"reset"===n)return void e.removeAttribute("value");t.hasOwnProperty("value")?Ce(e,t.type,r):t.hasOwnProperty("defaultValue")&&Ce(e,t.type,be(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Te(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!("submit"!==n&&"reset"!==n||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}""!==(r=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==r&&(e.name=r)}function Ce(e,t,r){"number"===t&&e.ownerDocument.activeElement===e||(null==r?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}function Ae(e,t){return e=i({children:void 0},t),(t=function(e){var t="";return n.Children.forEach(e,(function(e){null!=e&&(t+=e)})),t}(t.children))&&(e.children=t),e}function Le(e,t,r,n){if(e=e.options,t){t={};for(var i=0;i<r.length;i++)t["$"+r[i]]=!0;for(r=0;r<e.length;r++)i=t.hasOwnProperty("$"+e[r].value),e[r].selected!==i&&(e[r].selected=i),i&&n&&(e[r].defaultSelected=!0)}else{for(r=""+be(r),t=null,i=0;i<e.length;i++){if(e[i].value===r)return e[i].selected=!0,void(n&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function Ne(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return i({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Pe(e,t){var r=t.value;if(null==r){if(r=t.children,t=t.defaultValue,null!=r){if(null!=t)throw Error(o(92));if(Array.isArray(r)){if(!(1>=r.length))throw Error(o(93));r=r[0]}t=r}null==t&&(t=""),r=t}e._wrapperState={initialValue:be(r)}}function Re(e,t){var r=be(t.value),n=be(t.defaultValue);null!=r&&((r=""+r)!==e.value&&(e.value=r),null==t.defaultValue&&e.defaultValue!==r&&(e.defaultValue=r)),null!=n&&(e.defaultValue=""+n)}function De(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}var qe="http://www.w3.org/1999/xhtml",Me="http://www.w3.org/2000/svg";function Oe(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ie(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?Oe(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var He,Be=function(e){return"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,r,n,i){MSApp.execUnsafeLocalFunction((function(){return e(t,r)}))}:e}((function(e,t){if(e.namespaceURI!==Me||"innerHTML"in e)e.innerHTML=t;else{for((He=He||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=He.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}}));function Ue(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&3===r.nodeType)return void(r.nodeValue=t)}e.textContent=t}function Ve(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var ze={animationend:Ve("Animation","AnimationEnd"),animationiteration:Ve("Animation","AnimationIteration"),animationstart:Ve("Animation","AnimationStart"),transitionend:Ve("Transition","TransitionEnd")},Fe={},je={};function Ze(e){if(Fe[e])return Fe[e];if(!ze[e])return e;var t,r=ze[e];for(t in r)if(r.hasOwnProperty(t)&&t in je)return Fe[e]=r[t];return e}C&&(je=document.createElement("div").style,"AnimationEvent"in window||(delete ze.animationend.animation,delete ze.animationiteration.animation,delete ze.animationstart.animation),"TransitionEvent"in window||delete ze.transitionend.transition);var Ge=Ze("animationend"),We=Ze("animationiteration"),Ye=Ze("animationstart"),Qe=Ze("transitionend"),$e="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ke=new("function"==typeof WeakMap?WeakMap:Map);function Xe(e){var t=Ke.get(e);return void 0===t&&(t=new Map,Ke.set(e,t)),t}function Je(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!=(1026&(t=e).effectTag)&&(r=t.return),e=t.return}while(e)}return 3===t.tag?r:null}function et(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function tt(e){if(Je(e)!==e)throw Error(o(188))}function rt(e){if(!(e=function(e){var t=e.alternate;if(!t){if(null===(t=Je(e)))throw Error(o(188));return t!==e?null:e}for(var r=e,n=t;;){var i=r.return;if(null===i)break;var a=i.alternate;if(null===a){if(null!==(n=i.return)){r=n;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===r)return tt(i),e;if(a===n)return tt(i),t;a=a.sibling}throw Error(o(188))}if(r.return!==n.return)r=i,n=a;else{for(var s=!1,l=i.child;l;){if(l===r){s=!0,r=i,n=a;break}if(l===n){s=!0,n=i,r=a;break}l=l.sibling}if(!s){for(l=a.child;l;){if(l===r){s=!0,r=a,n=i;break}if(l===n){s=!0,n=a,r=i;break}l=l.sibling}if(!s)throw Error(o(189))}}if(r.alternate!==n)throw Error(o(190))}if(3!==r.tag)throw Error(o(188));return r.stateNode.current===r?e:t}(e)))return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}function nt(e,t){if(null==t)throw Error(o(30));return null==e?t:Array.isArray(e)?Array.isArray(t)?(e.push.apply(e,t),e):(e.push(t),e):Array.isArray(t)?[e].concat(t):[e,t]}function it(e,t,r){Array.isArray(e)?e.forEach(t,r):e&&t.call(r,e)}var at=null;function ot(e){if(e){var t=e._dispatchListeners,r=e._dispatchInstances;if(Array.isArray(t))for(var n=0;n<t.length&&!e.isPropagationStopped();n++)y(e,t[n],r[n]);else t&&y(e,t,r);e._dispatchListeners=null,e._dispatchInstances=null,e.isPersistent()||e.constructor.release(e)}}function st(e){if(null!==e&&(at=nt(at,e)),e=at,at=null,e){if(it(e,ot),at)throw Error(o(95));if(c)throw e=f,c=!1,f=null,e}}function lt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}function ut(e){if(!C)return!1;var t=(e="on"+e)in document;return t||((t=document.createElement("div")).setAttribute(e,"return;"),t="function"==typeof t[e]),t}var ct=[];function ft(e){e.topLevelType=null,e.nativeEvent=null,e.targetInst=null,e.ancestors.length=0,10>ct.length&&ct.push(e)}function pt(e,t,r,n){if(ct.length){var i=ct.pop();return i.topLevelType=e,i.eventSystemFlags=n,i.nativeEvent=t,i.targetInst=r,i}return{topLevelType:e,eventSystemFlags:n,nativeEvent:t,targetInst:r,ancestors:[]}}function dt(e){var t=e.targetInst,r=t;do{if(!r){e.ancestors.push(r);break}var n=r;if(3===n.tag)n=n.stateNode.containerInfo;else{for(;n.return;)n=n.return;n=3!==n.tag?null:n.stateNode.containerInfo}if(!n)break;5!==(t=r.tag)&&6!==t||e.ancestors.push(r),r=Tr(n)}while(r);for(r=0;r<e.ancestors.length;r++){t=e.ancestors[r];var i=lt(e.nativeEvent);n=e.topLevelType;var a=e.nativeEvent,o=e.eventSystemFlags;0===r&&(o|=64);for(var s=null,l=0;l<x.length;l++){var u=x[l];u&&(u=u.extractEvents(n,t,a,i,o))&&(s=nt(s,u))}st(s)}}function ht(e,t,r){if(!r.has(e)){switch(e){case"scroll":Yt(t,"scroll",!0);break;case"focus":case"blur":Yt(t,"focus",!0),Yt(t,"blur",!0),r.set("blur",null),r.set("focus",null);break;case"cancel":case"close":ut(e)&&Yt(t,e,!0);break;case"invalid":case"submit":case"reset":break;default:-1===$e.indexOf(e)&&Wt(e,t)}r.set(e,null)}}var mt,gt,yt,bt=!1,vt=[],wt=null,_t=null,xt=null,Et=new Map,St=new Map,kt=[],Tt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput close cancel copy cut paste click change contextmenu reset submit".split(" "),Ct="focus blur dragenter dragleave mouseover mouseout pointerover pointerout gotpointercapture lostpointercapture".split(" ");function At(e,t,r,n,i){return{blockedOn:e,topLevelType:t,eventSystemFlags:32|r,nativeEvent:i,container:n}}function Lt(e,t){switch(e){case"focus":case"blur":wt=null;break;case"dragenter":case"dragleave":_t=null;break;case"mouseover":case"mouseout":xt=null;break;case"pointerover":case"pointerout":Et.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":St.delete(t.pointerId)}}function Nt(e,t,r,n,i,a){return null===e||e.nativeEvent!==a?(e=At(t,r,n,i,a),null!==t&&(null!==(t=Cr(t))&&gt(t)),e):(e.eventSystemFlags|=n,e)}function Pt(e){var t=Tr(e.target);if(null!==t){var r=Je(t);if(null!==r)if(13===(t=r.tag)){if(null!==(t=et(r)))return e.blockedOn=t,void a.unstable_runWithPriority(e.priority,(function(){yt(r)}))}else if(3===t&&r.stateNode.hydrate)return void(e.blockedOn=3===r.tag?r.stateNode.containerInfo:null)}e.blockedOn=null}function Rt(e){if(null!==e.blockedOn)return!1;var t=Xt(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);if(null!==t){var r=Cr(t);return null!==r&&gt(r),e.blockedOn=t,!1}return!0}function Dt(e,t,r){Rt(e)&&r.delete(t)}function qt(){for(bt=!1;0<vt.length;){var e=vt[0];if(null!==e.blockedOn){null!==(e=Cr(e.blockedOn))&&mt(e);break}var t=Xt(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);null!==t?e.blockedOn=t:vt.shift()}null!==wt&&Rt(wt)&&(wt=null),null!==_t&&Rt(_t)&&(_t=null),null!==xt&&Rt(xt)&&(xt=null),Et.forEach(Dt),St.forEach(Dt)}function Mt(e,t){e.blockedOn===t&&(e.blockedOn=null,bt||(bt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,qt)))}function Ot(e){function t(t){return Mt(t,e)}if(0<vt.length){Mt(vt[0],e);for(var r=1;r<vt.length;r++){var n=vt[r];n.blockedOn===e&&(n.blockedOn=null)}}for(null!==wt&&Mt(wt,e),null!==_t&&Mt(_t,e),null!==xt&&Mt(xt,e),Et.forEach(t),St.forEach(t),r=0;r<kt.length;r++)(n=kt[r]).blockedOn===e&&(n.blockedOn=null);for(;0<kt.length&&null===(r=kt[0]).blockedOn;)Pt(r),null===r.blockedOn&&kt.shift()}var It={},Ht=new Map,Bt=new Map,Ut=["abort","abort",Ge,"animationEnd",We,"animationIteration",Ye,"animationStart","canplay","canPlay","canplaythrough","canPlayThrough","durationchange","durationChange","emptied","emptied","encrypted","encrypted","ended","ended","error","error","gotpointercapture","gotPointerCapture","load","load","loadeddata","loadedData","loadedmetadata","loadedMetadata","loadstart","loadStart","lostpointercapture","lostPointerCapture","playing","playing","progress","progress","seeking","seeking","stalled","stalled","suspend","suspend","timeupdate","timeUpdate",Qe,"transitionEnd","waiting","waiting"];function Vt(e,t){for(var r=0;r<e.length;r+=2){var n=e[r],i=e[r+1],a="on"+(i[0].toUpperCase()+i.slice(1));a={phasedRegistrationNames:{bubbled:a,captured:a+"Capture"},dependencies:[n],eventPriority:t},Bt.set(n,t),Ht.set(n,a),It[i]=a}}Vt("blur blur cancel cancel click click close close contextmenu contextMenu copy copy cut cut auxclick auxClick dblclick doubleClick dragend dragEnd dragstart dragStart drop drop focus focus input input invalid invalid keydown keyDown keypress keyPress keyup keyUp mousedown mouseDown mouseup mouseUp paste paste pause pause play play pointercancel pointerCancel pointerdown pointerDown pointerup pointerUp ratechange rateChange reset reset seeked seeked submit submit touchcancel touchCancel touchend touchEnd touchstart touchStart volumechange volumeChange".split(" "),0),Vt("drag drag dragenter dragEnter dragexit dragExit dragleave dragLeave dragover dragOver mousemove mouseMove mouseout mouseOut mouseover mouseOver pointermove pointerMove pointerout pointerOut pointerover pointerOver scroll scroll toggle toggle touchmove touchMove wheel wheel".split(" "),1),Vt(Ut,2);for(var zt="change selectionchange textInput compositionstart compositionend compositionupdate".split(" "),Ft=0;Ft<zt.length;Ft++)Bt.set(zt[Ft],0);var jt=a.unstable_UserBlockingPriority,Zt=a.unstable_runWithPriority,Gt=!0;function Wt(e,t){Yt(t,e,!1)}function Yt(e,t,r){var n=Bt.get(t);switch(void 0===n?2:n){case 0:n=Qt.bind(null,t,1,e);break;case 1:n=$t.bind(null,t,1,e);break;default:n=Kt.bind(null,t,1,e)}r?e.addEventListener(t,n,!0):e.addEventListener(t,n,!1)}function Qt(e,t,r,n){H||O();var i=Kt,a=H;H=!0;try{M(i,e,t,r,n)}finally{(H=a)||U()}}function $t(e,t,r,n){Zt(jt,Kt.bind(null,e,t,r,n))}function Kt(e,t,r,n){if(Gt)if(0<vt.length&&-1<Tt.indexOf(e))e=At(null,e,t,r,n),vt.push(e);else{var i=Xt(e,t,r,n);if(null===i)Lt(e,n);else if(-1<Tt.indexOf(e))e=At(i,e,t,r,n),vt.push(e);else if(!function(e,t,r,n,i){switch(t){case"focus":return wt=Nt(wt,e,t,r,n,i),!0;case"dragenter":return _t=Nt(_t,e,t,r,n,i),!0;case"mouseover":return xt=Nt(xt,e,t,r,n,i),!0;case"pointerover":var a=i.pointerId;return Et.set(a,Nt(Et.get(a)||null,e,t,r,n,i)),!0;case"gotpointercapture":return a=i.pointerId,St.set(a,Nt(St.get(a)||null,e,t,r,n,i)),!0}return!1}(i,e,t,r,n)){Lt(e,n),e=pt(e,n,null,t);try{V(dt,e)}finally{ft(e)}}}}function Xt(e,t,r,n){if(null!==(r=Tr(r=lt(n)))){var i=Je(r);if(null===i)r=null;else{var a=i.tag;if(13===a){if(null!==(r=et(i)))return r;r=null}else if(3===a){if(i.stateNode.hydrate)return 3===i.tag?i.stateNode.containerInfo:null;r=null}else i!==r&&(r=null)}}e=pt(e,n,r,t);try{V(dt,e)}finally{ft(e)}return null}var Jt={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},er=["Webkit","ms","Moz","O"];function tr(e,t,r){return null==t||"boolean"==typeof t||""===t?"":r||"number"!=typeof t||0===t||Jt.hasOwnProperty(e)&&Jt[e]?(""+t).trim():t+"px"}function rr(e,t){for(var r in e=e.style,t)if(t.hasOwnProperty(r)){var n=0===r.indexOf("--"),i=tr(r,t[r],n);"float"===r&&(r="cssFloat"),n?e.setProperty(r,i):e[r]=i}}Object.keys(Jt).forEach((function(e){er.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Jt[t]=Jt[e]}))}));var nr=i({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ir(e,t){if(t){if(nr[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e,""));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(o(62,""))}}function ar(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var or=qe;function sr(e,t){var r=Xe(e=9===e.nodeType||11===e.nodeType?e:e.ownerDocument);t=k[t];for(var n=0;n<t.length;n++)ht(t[n],e,r)}function lr(){}function ur(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function fr(e,t){var r,n=cr(e);for(e=0;n;){if(3===n.nodeType){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=cr(n)}}function pr(){for(var e=window,t=ur();t instanceof e.HTMLIFrameElement;){try{var r="string"==typeof t.contentWindow.location.href}catch(e){r=!1}if(!r)break;t=ur((e=t.contentWindow).document)}return t}function dr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var hr=null,mr=null;function gr(e,t){switch(e){case"button":case"input":case"select":case"textarea":return!!t.autoFocus}return!1}function yr(e,t){return"textarea"===e||"option"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var br="function"==typeof setTimeout?setTimeout:void 0,vr="function"==typeof clearTimeout?clearTimeout:void 0;function wr(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break}return e}function _r(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var r=e.data;if("$"===r||"$!"===r||"$?"===r){if(0===t)return e;t--}else"/$"===r&&t++}e=e.previousSibling}return null}var xr=Math.random().toString(36).slice(2),Er="__reactInternalInstance$"+xr,Sr="__reactEventHandlers$"+xr,kr="__reactContainere$"+xr;function Tr(e){var t=e[Er];if(t)return t;for(var r=e.parentNode;r;){if(t=r[kr]||r[Er]){if(r=t.alternate,null!==t.child||null!==r&&null!==r.child)for(e=_r(e);null!==e;){if(r=e[Er])return r;e=_r(e)}return t}r=(e=r).parentNode}return null}function Cr(e){return!(e=e[Er]||e[kr])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function Ar(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function Lr(e){return e[Sr]||null}function Nr(e){do{e=e.return}while(e&&5!==e.tag);return e||null}function Pr(e,t){var r=e.stateNode;if(!r)return null;var n=h(r);if(!n)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(n=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!n;break e;default:e=!1}if(e)return null;if(r&&"function"!=typeof r)throw Error(o(231,t,typeof r));return r}function Rr(e,t,r){(t=Pr(e,r.dispatchConfig.phasedRegistrationNames[t]))&&(r._dispatchListeners=nt(r._dispatchListeners,t),r._dispatchInstances=nt(r._dispatchInstances,e))}function Dr(e){if(e&&e.dispatchConfig.phasedRegistrationNames){for(var t=e._targetInst,r=[];t;)r.push(t),t=Nr(t);for(t=r.length;0<t--;)Rr(r[t],"captured",e);for(t=0;t<r.length;t++)Rr(r[t],"bubbled",e)}}function qr(e,t,r){e&&r&&r.dispatchConfig.registrationName&&(t=Pr(e,r.dispatchConfig.registrationName))&&(r._dispatchListeners=nt(r._dispatchListeners,t),r._dispatchInstances=nt(r._dispatchInstances,e))}function Mr(e){e&&e.dispatchConfig.registrationName&&qr(e._targetInst,null,e)}function Or(e){it(e,Dr)}var Ir=null,Hr=null,Br=null;function Ur(){if(Br)return Br;var e,t,r=Hr,n=r.length,i="value"in Ir?Ir.value:Ir.textContent,a=i.length;for(e=0;e<n&&r[e]===i[e];e++);var o=n-e;for(t=1;t<=o&&r[n-t]===i[a-t];t++);return Br=i.slice(e,1<t?1-t:void 0)}function Vr(){return!0}function zr(){return!1}function Fr(e,t,r,n){for(var i in this.dispatchConfig=e,this._targetInst=t,this.nativeEvent=r,e=this.constructor.Interface)e.hasOwnProperty(i)&&((t=e[i])?this[i]=t(r):"target"===i?this.target=n:this[i]=r[i]);return this.isDefaultPrevented=(null!=r.defaultPrevented?r.defaultPrevented:!1===r.returnValue)?Vr:zr,this.isPropagationStopped=zr,this}function jr(e,t,r,n){if(this.eventPool.length){var i=this.eventPool.pop();return this.call(i,e,t,r,n),i}return new this(e,t,r,n)}function Zr(e){if(!(e instanceof this))throw Error(o(279));e.destructor(),10>this.eventPool.length&&this.eventPool.push(e)}function Gr(e){e.eventPool=[],e.getPooled=jr,e.release=Zr}i(Fr.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Vr)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Vr)},persist:function(){this.isPersistent=Vr},isPersistent:zr,destructor:function(){var e,t=this.constructor.Interface;for(e in t)this[e]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=null,this.isPropagationStopped=this.isDefaultPrevented=zr,this._dispatchInstances=this._dispatchListeners=null}}),Fr.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null},Fr.extend=function(e){function t(){}function r(){return n.apply(this,arguments)}var n=this;t.prototype=n.prototype;var a=new t;return i(a,r.prototype),r.prototype=a,r.prototype.constructor=r,r.Interface=i({},n.Interface,e),r.extend=n.extend,Gr(r),r},Gr(Fr);var Wr=Fr.extend({data:null}),Yr=Fr.extend({data:null}),Qr=[9,13,27,32],$r=C&&"CompositionEvent"in window,Kr=null;C&&"documentMode"in document&&(Kr=document.documentMode);var Xr=C&&"TextEvent"in window&&!Kr,Jr=C&&(!$r||Kr&&8<Kr&&11>=Kr),en=String.fromCharCode(32),tn={beforeInput:{phasedRegistrationNames:{bubbled:"onBeforeInput",captured:"onBeforeInputCapture"},dependencies:["compositionend","keypress","textInput","paste"]},compositionEnd:{phasedRegistrationNames:{bubbled:"onCompositionEnd",captured:"onCompositionEndCapture"},dependencies:"blur compositionend keydown keypress keyup mousedown".split(" ")},compositionStart:{phasedRegistrationNames:{bubbled:"onCompositionStart",captured:"onCompositionStartCapture"},dependencies:"blur compositionstart keydown keypress keyup mousedown".split(" ")},compositionUpdate:{phasedRegistrationNames:{bubbled:"onCompositionUpdate",captured:"onCompositionUpdateCapture"},dependencies:"blur compositionupdate keydown keypress keyup mousedown".split(" ")}},rn=!1;function nn(e,t){switch(e){case"keyup":return-1!==Qr.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"blur":return!0;default:return!1}}function an(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var on=!1;var sn={eventTypes:tn,extractEvents:function(e,t,r,n){var i;if($r)e:{switch(e){case"compositionstart":var a=tn.compositionStart;break e;case"compositionend":a=tn.compositionEnd;break e;case"compositionupdate":a=tn.compositionUpdate;break e}a=void 0}else on?nn(e,r)&&(a=tn.compositionEnd):"keydown"===e&&229===r.keyCode&&(a=tn.compositionStart);return a?(Jr&&"ko"!==r.locale&&(on||a!==tn.compositionStart?a===tn.compositionEnd&&on&&(i=Ur()):(Hr="value"in(Ir=n)?Ir.value:Ir.textContent,on=!0)),a=Wr.getPooled(a,t,r,n),i?a.data=i:null!==(i=an(r))&&(a.data=i),Or(a),i=a):i=null,(e=Xr?function(e,t){switch(e){case"compositionend":return an(t);case"keypress":return 32!==t.which?null:(rn=!0,en);case"textInput":return(e=t.data)===en&&rn?null:e;default:return null}}(e,r):function(e,t){if(on)return"compositionend"===e||!$r&&nn(e,t)?(e=Ur(),Br=Hr=Ir=null,on=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Jr&&"ko"!==t.locale?null:t.data;default:return null}}(e,r))?((t=Yr.getPooled(tn.beforeInput,t,r,n)).data=e,Or(t)):t=null,null===i?t:null===t?i:[i,t]}},ln={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function un(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!ln[e.type]:"textarea"===t}var cn={change:{phasedRegistrationNames:{bubbled:"onChange",captured:"onChangeCapture"},dependencies:"blur change click focus input keydown keyup selectionchange".split(" ")}};function fn(e,t,r){return(e=Fr.getPooled(cn.change,e,t,r)).type="change",R(r),Or(e),e}var pn=null,dn=null;function hn(e){st(e)}function mn(e){if(_e(Ar(e)))return e}function gn(e,t){if("change"===e)return t}var yn=!1;function bn(){pn&&(pn.detachEvent("onpropertychange",vn),dn=pn=null)}function vn(e){if("value"===e.propertyName&&mn(dn))if(e=fn(dn,e,lt(e)),H)st(e);else{H=!0;try{q(hn,e)}finally{H=!1,U()}}}function wn(e,t,r){"focus"===e?(bn(),dn=r,(pn=t).attachEvent("onpropertychange",vn)):"blur"===e&&bn()}function _n(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return mn(dn)}function xn(e,t){if("click"===e)return mn(t)}function En(e,t){if("input"===e||"change"===e)return mn(t)}C&&(yn=ut("input")&&(!document.documentMode||9<document.documentMode));var Sn={eventTypes:cn,_isInputEventSupported:yn,extractEvents:function(e,t,r,n){var i=t?Ar(t):window,a=i.nodeName&&i.nodeName.toLowerCase();if("select"===a||"input"===a&&"file"===i.type)var o=gn;else if(un(i))if(yn)o=En;else{o=_n;var s=wn}else(a=i.nodeName)&&"input"===a.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(o=xn);if(o&&(o=o(e,t)))return fn(o,r,n);s&&s(e,i,t),"blur"===e&&(e=i._wrapperState)&&e.controlled&&"number"===i.type&&Ce(i,"number",i.value)}},kn=Fr.extend({view:null,detail:null}),Tn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Tn[e])&&!!t[e]}function An(){return Cn}var Ln=0,Nn=0,Pn=!1,Rn=!1,Dn=kn.extend({screenX:null,screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:An,button:null,buttons:null,relatedTarget:function(e){return e.relatedTarget||(e.fromElement===e.srcElement?e.toElement:e.fromElement)},movementX:function(e){if("movementX"in e)return e.movementX;var t=Ln;return Ln=e.screenX,Pn?"mousemove"===e.type?e.screenX-t:0:(Pn=!0,0)},movementY:function(e){if("movementY"in e)return e.movementY;var t=Nn;return Nn=e.screenY,Rn?"mousemove"===e.type?e.screenY-t:0:(Rn=!0,0)}}),qn=Dn.extend({pointerId:null,width:null,height:null,pressure:null,tangentialPressure:null,tiltX:null,tiltY:null,twist:null,pointerType:null,isPrimary:null}),Mn={mouseEnter:{registrationName:"onMouseEnter",dependencies:["mouseout","mouseover"]},mouseLeave:{registrationName:"onMouseLeave",dependencies:["mouseout","mouseover"]},pointerEnter:{registrationName:"onPointerEnter",dependencies:["pointerout","pointerover"]},pointerLeave:{registrationName:"onPointerLeave",dependencies:["pointerout","pointerover"]}},On={eventTypes:Mn,extractEvents:function(e,t,r,n,i){var a="mouseover"===e||"pointerover"===e,o="mouseout"===e||"pointerout"===e;if(a&&0==(32&i)&&(r.relatedTarget||r.fromElement)||!o&&!a)return null;(a=n.window===n?n:(a=n.ownerDocument)?a.defaultView||a.parentWindow:window,o)?(o=t,null!==(t=(t=r.relatedTarget||r.toElement)?Tr(t):null)&&(t!==Je(t)||5!==t.tag&&6!==t.tag)&&(t=null)):o=null;if(o===t)return null;if("mouseout"===e||"mouseover"===e)var s=Dn,l=Mn.mouseLeave,u=Mn.mouseEnter,c="mouse";else"pointerout"!==e&&"pointerover"!==e||(s=qn,l=Mn.pointerLeave,u=Mn.pointerEnter,c="pointer");if(e=null==o?a:Ar(o),a=null==t?a:Ar(t),(l=s.getPooled(l,o,r,n)).type=c+"leave",l.target=e,l.relatedTarget=a,(r=s.getPooled(u,t,r,n)).type=c+"enter",r.target=a,r.relatedTarget=e,c=t,(n=o)&&c)e:{for(u=c,o=0,e=s=n;e;e=Nr(e))o++;for(e=0,t=u;t;t=Nr(t))e++;for(;0<o-e;)s=Nr(s),o--;for(;0<e-o;)u=Nr(u),e--;for(;o--;){if(s===u||s===u.alternate)break e;s=Nr(s),u=Nr(u)}s=null}else s=null;for(u=s,s=[];n&&n!==u&&(null===(o=n.alternate)||o!==u);)s.push(n),n=Nr(n);for(n=[];c&&c!==u&&(null===(o=c.alternate)||o!==u);)n.push(c),c=Nr(c);for(c=0;c<s.length;c++)qr(s[c],"bubbled",l);for(c=n.length;0<c--;)qr(n[c],"captured",r);return 0==(64&i)?[l]:[l,r]}};var In="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},Hn=Object.prototype.hasOwnProperty;function Bn(e,t){if(In(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++)if(!Hn.call(t,r[n])||!In(e[r[n]],t[r[n]]))return!1;return!0}var Un=C&&"documentMode"in document&&11>=document.documentMode,Vn={select:{phasedRegistrationNames:{bubbled:"onSelect",captured:"onSelectCapture"},dependencies:"blur contextmenu dragend focus keydown keyup mousedown mouseup selectionchange".split(" ")}},zn=null,Fn=null,jn=null,Zn=!1;function Gn(e,t){var r=t.window===t?t.document:9===t.nodeType?t:t.ownerDocument;return Zn||null==zn||zn!==ur(r)?null:("selectionStart"in(r=zn)&&dr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},jn&&Bn(jn,r)?null:(jn=r,(e=Fr.getPooled(Vn.select,Fn,e,t)).type="select",e.target=zn,Or(e),e))}var Wn={eventTypes:Vn,extractEvents:function(e,t,r,n,i,a){if(!(a=!(i=a||(n.window===n?n.document:9===n.nodeType?n:n.ownerDocument)))){e:{i=Xe(i),a=k.onSelect;for(var o=0;o<a.length;o++)if(!i.has(a[o])){i=!1;break e}i=!0}a=!i}if(a)return null;switch(i=t?Ar(t):window,e){case"focus":(un(i)||"true"===i.contentEditable)&&(zn=i,Fn=t,jn=null);break;case"blur":jn=Fn=zn=null;break;case"mousedown":Zn=!0;break;case"contextmenu":case"mouseup":case"dragend":return Zn=!1,Gn(r,n);case"selectionchange":if(Un)break;case"keydown":case"keyup":return Gn(r,n)}return null}},Yn=Fr.extend({animationName:null,elapsedTime:null,pseudoElement:null}),Qn=Fr.extend({clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$n=kn.extend({relatedTarget:null});function Kn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}var Xn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ei=kn.extend({key:function(e){if(e.key){var t=Xn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Kn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Jn[e.keyCode]||"Unidentified":""},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:An,charCode:function(e){return"keypress"===e.type?Kn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Kn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),ti=Dn.extend({dataTransfer:null}),ri=kn.extend({touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:An}),ni=Fr.extend({propertyName:null,elapsedTime:null,pseudoElement:null}),ii=Dn.extend({deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:null,deltaMode:null}),ai={eventTypes:It,extractEvents:function(e,t,r,n){var i=Ht.get(e);if(!i)return null;switch(e){case"keypress":if(0===Kn(r))return null;case"keydown":case"keyup":e=ei;break;case"blur":case"focus":e=$n;break;case"click":if(2===r.button)return null;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":e=Dn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":e=ti;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":e=ri;break;case Ge:case We:case Ye:e=Yn;break;case Qe:e=ni;break;case"scroll":e=kn;break;case"wheel":e=ii;break;case"copy":case"cut":case"paste":e=Qn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":e=qn;break;default:e=Fr}return Or(t=e.getPooled(i,t,r,n)),t}};if(b)throw Error(o(101));b=Array.prototype.slice.call("ResponderEventPlugin SimpleEventPlugin EnterLeaveEventPlugin ChangeEventPlugin SelectEventPlugin BeforeInputEventPlugin".split(" ")),w(),h=Lr,m=Cr,g=Ar,T({SimpleEventPlugin:ai,EnterLeaveEventPlugin:On,ChangeEventPlugin:Sn,SelectEventPlugin:Wn,BeforeInputEventPlugin:sn});var oi=[],si=-1;function li(e){0>si||(e.current=oi[si],oi[si]=null,si--)}function ui(e,t){si++,oi[si]=e.current,e.current=t}var ci={},fi={current:ci},pi={current:!1},di=ci;function hi(e,t){var r=e.type.contextTypes;if(!r)return ci;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var i,a={};for(i in r)a[i]=t[i];return n&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function mi(e){return null!=(e=e.childContextTypes)}function gi(){li(pi),li(fi)}function yi(e,t,r){if(fi.current!==ci)throw Error(o(168));ui(fi,t),ui(pi,r)}function bi(e,t,r){var n=e.stateNode;if(e=t.childContextTypes,"function"!=typeof n.getChildContext)return r;for(var a in n=n.getChildContext())if(!(a in e))throw Error(o(108,ge(t)||"Unknown",a));return i({},r,{},n)}function vi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ci,di=fi.current,ui(fi,e),ui(pi,pi.current),!0}function wi(e,t,r){var n=e.stateNode;if(!n)throw Error(o(169));r?(e=bi(e,t,di),n.__reactInternalMemoizedMergedChildContext=e,li(pi),li(fi),ui(fi,e)):li(pi),ui(pi,r)}var _i=a.unstable_runWithPriority,xi=a.unstable_scheduleCallback,Ei=a.unstable_cancelCallback,Si=a.unstable_requestPaint,ki=a.unstable_now,Ti=a.unstable_getCurrentPriorityLevel,Ci=a.unstable_ImmediatePriority,Ai=a.unstable_UserBlockingPriority,Li=a.unstable_NormalPriority,Ni=a.unstable_LowPriority,Pi=a.unstable_IdlePriority,Ri={},Di=a.unstable_shouldYield,qi=void 0!==Si?Si:function(){},Mi=null,Oi=null,Ii=!1,Hi=ki(),Bi=1e4>Hi?ki:function(){return ki()-Hi};function Ui(){switch(Ti()){case Ci:return 99;case Ai:return 98;case Li:return 97;case Ni:return 96;case Pi:return 95;default:throw Error(o(332))}}function Vi(e){switch(e){case 99:return Ci;case 98:return Ai;case 97:return Li;case 96:return Ni;case 95:return Pi;default:throw Error(o(332))}}function zi(e,t){return e=Vi(e),_i(e,t)}function Fi(e,t,r){return e=Vi(e),xi(e,t,r)}function ji(e){return null===Mi?(Mi=[e],Oi=xi(Ci,Gi)):Mi.push(e),Ri}function Zi(){if(null!==Oi){var e=Oi;Oi=null,Ei(e)}Gi()}function Gi(){if(!Ii&&null!==Mi){Ii=!0;var e=0;try{var t=Mi;zi(99,(function(){for(;e<t.length;e++){var r=t[e];do{r=r(!0)}while(null!==r)}})),Mi=null}catch(t){throw null!==Mi&&(Mi=Mi.slice(e+1)),xi(Ci,Zi),t}finally{Ii=!1}}}function Wi(e,t,r){return 1073741821-(1+((1073741821-e+t/10)/(r/=10)|0))*r}function Yi(e,t){if(e&&e.defaultProps)for(var r in t=i({},t),e=e.defaultProps)void 0===t[r]&&(t[r]=e[r]);return t}var Qi={current:null},$i=null,Ki=null,Xi=null;function Ji(){Xi=Ki=$i=null}function ea(e){var t=Qi.current;li(Qi),e.type._context._currentValue=t}function ta(e,t){for(;null!==e;){var r=e.alternate;if(e.childExpirationTime<t)e.childExpirationTime=t,null!==r&&r.childExpirationTime<t&&(r.childExpirationTime=t);else{if(!(null!==r&&r.childExpirationTime<t))break;r.childExpirationTime=t}e=e.return}}function ra(e,t){$i=e,Xi=Ki=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(e.expirationTime>=t&&(No=!0),e.firstContext=null)}function na(e,t){if(Xi!==e&&!1!==t&&0!==t)if("number"==typeof t&&**********!==t||(Xi=e,t=**********),t={context:e,observedBits:t,next:null},null===Ki){if(null===$i)throw Error(o(308));Ki=t,$i.dependencies={expirationTime:0,firstContext:t,responders:null}}else Ki=Ki.next=t;return e._currentValue}var ia=!1;function aa(e){e.updateQueue={baseState:e.memoizedState,baseQueue:null,shared:{pending:null},effects:null}}function oa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,baseQueue:e.baseQueue,shared:e.shared,effects:e.effects})}function sa(e,t){return(e={expirationTime:e,suspenseConfig:t,tag:0,payload:null,callback:null,next:null}).next=e}function la(e,t){if(null!==(e=e.updateQueue)){var r=(e=e.shared).pending;null===r?t.next=t:(t.next=r.next,r.next=t),e.pending=t}}function ua(e,t){var r=e.alternate;null!==r&&oa(r,e),null===(r=(e=e.updateQueue).baseQueue)?(e.baseQueue=t.next=t,t.next=t):(t.next=r.next,r.next=t)}function ca(e,t,r,n){var a=e.updateQueue;ia=!1;var o=a.baseQueue,s=a.shared.pending;if(null!==s){if(null!==o){var l=o.next;o.next=s.next,s.next=l}o=s,a.shared.pending=null,null!==(l=e.alternate)&&(null!==(l=l.updateQueue)&&(l.baseQueue=s))}if(null!==o){l=o.next;var u=a.baseState,c=0,f=null,p=null,d=null;if(null!==l)for(var h=l;;){if((s=h.expirationTime)<n){var m={expirationTime:h.expirationTime,suspenseConfig:h.suspenseConfig,tag:h.tag,payload:h.payload,callback:h.callback,next:null};null===d?(p=d=m,f=u):d=d.next=m,s>c&&(c=s)}else{null!==d&&(d=d.next={expirationTime:**********,suspenseConfig:h.suspenseConfig,tag:h.tag,payload:h.payload,callback:h.callback,next:null}),al(s,h.suspenseConfig);e:{var g=e,y=h;switch(s=t,m=r,y.tag){case 1:if("function"==typeof(g=y.payload)){u=g.call(m,u,s);break e}u=g;break e;case 3:g.effectTag=-4097&g.effectTag|64;case 0:if(null==(s="function"==typeof(g=y.payload)?g.call(m,u,s):g))break e;u=i({},u,s);break e;case 2:ia=!0}}null!==h.callback&&(e.effectTag|=32,null===(s=a.effects)?a.effects=[h]:s.push(h))}if(null===(h=h.next)||h===l){if(null===(s=a.shared.pending))break;h=o.next=s.next,s.next=l,a.baseQueue=o=s,a.shared.pending=null}}null===d?f=u:d.next=p,a.baseState=f,a.baseQueue=d,ol(c),e.expirationTime=c,e.memoizedState=u}}function fa(e,t,r){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var n=e[t],i=n.callback;if(null!==i){if(n.callback=null,n=i,i=r,"function"!=typeof n)throw Error(o(191,n));n.call(i)}}}var pa=$.ReactCurrentBatchConfig,da=(new n.Component).refs;function ha(e,t,r,n){r=null==(r=r(n,t=e.memoizedState))?t:i({},t,r),e.memoizedState=r,0===e.expirationTime&&(e.updateQueue.baseState=r)}var ma={isMounted:function(e){return!!(e=e._reactInternalFiber)&&Je(e)===e},enqueueSetState:function(e,t,r){e=e._reactInternalFiber;var n=Gs(),i=pa.suspense;(i=sa(n=Ws(n,e,i),i)).payload=t,null!=r&&(i.callback=r),la(e,i),Ys(e,n)},enqueueReplaceState:function(e,t,r){e=e._reactInternalFiber;var n=Gs(),i=pa.suspense;(i=sa(n=Ws(n,e,i),i)).tag=1,i.payload=t,null!=r&&(i.callback=r),la(e,i),Ys(e,n)},enqueueForceUpdate:function(e,t){e=e._reactInternalFiber;var r=Gs(),n=pa.suspense;(n=sa(r=Ws(r,e,n),n)).tag=2,null!=t&&(n.callback=t),la(e,n),Ys(e,r)}};function ga(e,t,r,n,i,a,o){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(n,a,o):!t.prototype||!t.prototype.isPureReactComponent||(!Bn(r,n)||!Bn(i,a))}function ya(e,t,r){var n=!1,i=ci,a=t.contextType;return"object"==typeof a&&null!==a?a=na(a):(i=mi(t)?di:fi.current,a=(n=null!=(n=t.contextTypes))?hi(e,i):ci),t=new t(r,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ma,e.stateNode=t,t._reactInternalFiber=e,n&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=a),t}function ba(e,t,r,n){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(r,n),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&ma.enqueueReplaceState(t,t.state,null)}function va(e,t,r,n){var i=e.stateNode;i.props=r,i.state=e.memoizedState,i.refs=da,aa(e);var a=t.contextType;"object"==typeof a&&null!==a?i.context=na(a):(a=mi(t)?di:fi.current,i.context=hi(e,a)),ca(e,r,i,n),i.state=e.memoizedState,"function"==typeof(a=t.getDerivedStateFromProps)&&(ha(e,t,a,r),i.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof i.getSnapshotBeforeUpdate||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||(t=i.state,"function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&ma.enqueueReplaceState(i,i.state,null),ca(e,r,i,n),i.state=e.memoizedState),"function"==typeof i.componentDidMount&&(e.effectTag|=4)}var wa=Array.isArray;function _a(e,t,r){if(null!==(e=r.ref)&&"function"!=typeof e&&"object"!=typeof e){if(r._owner){if(r=r._owner){if(1!==r.tag)throw Error(o(309));var n=r.stateNode}if(!n)throw Error(o(147,e));var i=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===i?t.ref:((t=function(e){var t=n.refs;t===da&&(t=n.refs={}),null===e?delete t[i]:t[i]=e})._stringRef=i,t)}if("string"!=typeof e)throw Error(o(284));if(!r._owner)throw Error(o(290,e))}return e}function xa(e,t){if("textarea"!==e.type)throw Error(o(31,"[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t,""))}function Ea(e){function t(t,r){if(e){var n=t.lastEffect;null!==n?(n.nextEffect=r,t.lastEffect=r):t.firstEffect=t.lastEffect=r,r.nextEffect=null,r.effectTag=8}}function r(r,n){if(!e)return null;for(;null!==n;)t(r,n),n=n.sibling;return null}function n(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Tl(e,t)).index=0,e.sibling=null,e}function a(t,r,n){return t.index=n,e?null!==(n=t.alternate)?(n=n.index)<r?(t.effectTag=2,r):n:(t.effectTag=2,r):r}function s(t){return e&&null===t.alternate&&(t.effectTag=2),t}function l(e,t,r,n){return null===t||6!==t.tag?((t=Ll(r,e.mode,n)).return=e,t):((t=i(t,r)).return=e,t)}function u(e,t,r,n){return null!==t&&t.elementType===r.type?((n=i(t,r.props)).ref=_a(e,t,r),n.return=e,n):((n=Cl(r.type,r.key,r.props,null,e.mode,n)).ref=_a(e,t,r),n.return=e,n)}function c(e,t,r,n){return null===t||4!==t.tag||t.stateNode.containerInfo!==r.containerInfo||t.stateNode.implementation!==r.implementation?((t=Nl(r,e.mode,n)).return=e,t):((t=i(t,r.children||[])).return=e,t)}function f(e,t,r,n,a){return null===t||7!==t.tag?((t=Al(r,e.mode,n,a)).return=e,t):((t=i(t,r)).return=e,t)}function p(e,t,r){if("string"==typeof t||"number"==typeof t)return(t=Ll(""+t,e.mode,r)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case ee:return(r=Cl(t.type,t.key,t.props,null,e.mode,r)).ref=_a(e,null,t),r.return=e,r;case te:return(t=Nl(t,e.mode,r)).return=e,t}if(wa(t)||me(t))return(t=Al(t,e.mode,r,null)).return=e,t;xa(e,t)}return null}function d(e,t,r,n){var i=null!==t?t.key:null;if("string"==typeof r||"number"==typeof r)return null!==i?null:l(e,t,""+r,n);if("object"==typeof r&&null!==r){switch(r.$$typeof){case ee:return r.key===i?r.type===re?f(e,t,r.props.children,n,i):u(e,t,r,n):null;case te:return r.key===i?c(e,t,r,n):null}if(wa(r)||me(r))return null!==i?null:f(e,t,r,n,null);xa(e,r)}return null}function h(e,t,r,n,i){if("string"==typeof n||"number"==typeof n)return l(t,e=e.get(r)||null,""+n,i);if("object"==typeof n&&null!==n){switch(n.$$typeof){case ee:return e=e.get(null===n.key?r:n.key)||null,n.type===re?f(t,e,n.props.children,i,n.key):u(t,e,n,i);case te:return c(t,e=e.get(null===n.key?r:n.key)||null,n,i)}if(wa(n)||me(n))return f(t,e=e.get(r)||null,n,i,null);xa(t,n)}return null}function m(i,o,s,l){for(var u=null,c=null,f=o,m=o=0,g=null;null!==f&&m<s.length;m++){f.index>m?(g=f,f=null):g=f.sibling;var y=d(i,f,s[m],l);if(null===y){null===f&&(f=g);break}e&&f&&null===y.alternate&&t(i,f),o=a(y,o,m),null===c?u=y:c.sibling=y,c=y,f=g}if(m===s.length)return r(i,f),u;if(null===f){for(;m<s.length;m++)null!==(f=p(i,s[m],l))&&(o=a(f,o,m),null===c?u=f:c.sibling=f,c=f);return u}for(f=n(i,f);m<s.length;m++)null!==(g=h(f,i,m,s[m],l))&&(e&&null!==g.alternate&&f.delete(null===g.key?m:g.key),o=a(g,o,m),null===c?u=g:c.sibling=g,c=g);return e&&f.forEach((function(e){return t(i,e)})),u}function g(i,s,l,u){var c=me(l);if("function"!=typeof c)throw Error(o(150));if(null==(l=c.call(l)))throw Error(o(151));for(var f=c=null,m=s,g=s=0,y=null,b=l.next();null!==m&&!b.done;g++,b=l.next()){m.index>g?(y=m,m=null):y=m.sibling;var v=d(i,m,b.value,u);if(null===v){null===m&&(m=y);break}e&&m&&null===v.alternate&&t(i,m),s=a(v,s,g),null===f?c=v:f.sibling=v,f=v,m=y}if(b.done)return r(i,m),c;if(null===m){for(;!b.done;g++,b=l.next())null!==(b=p(i,b.value,u))&&(s=a(b,s,g),null===f?c=b:f.sibling=b,f=b);return c}for(m=n(i,m);!b.done;g++,b=l.next())null!==(b=h(m,i,g,b.value,u))&&(e&&null!==b.alternate&&m.delete(null===b.key?g:b.key),s=a(b,s,g),null===f?c=b:f.sibling=b,f=b);return e&&m.forEach((function(e){return t(i,e)})),c}return function(e,n,a,l){var u="object"==typeof a&&null!==a&&a.type===re&&null===a.key;u&&(a=a.props.children);var c="object"==typeof a&&null!==a;if(c)switch(a.$$typeof){case ee:e:{for(c=a.key,u=n;null!==u;){if(u.key===c){switch(u.tag){case 7:if(a.type===re){r(e,u.sibling),(n=i(u,a.props.children)).return=e,e=n;break e}break;default:if(u.elementType===a.type){r(e,u.sibling),(n=i(u,a.props)).ref=_a(e,u,a),n.return=e,e=n;break e}}r(e,u);break}t(e,u),u=u.sibling}a.type===re?((n=Al(a.props.children,e.mode,l,a.key)).return=e,e=n):((l=Cl(a.type,a.key,a.props,null,e.mode,l)).ref=_a(e,n,a),l.return=e,e=l)}return s(e);case te:e:{for(u=a.key;null!==n;){if(n.key===u){if(4===n.tag&&n.stateNode.containerInfo===a.containerInfo&&n.stateNode.implementation===a.implementation){r(e,n.sibling),(n=i(n,a.children||[])).return=e,e=n;break e}r(e,n);break}t(e,n),n=n.sibling}(n=Nl(a,e.mode,l)).return=e,e=n}return s(e)}if("string"==typeof a||"number"==typeof a)return a=""+a,null!==n&&6===n.tag?(r(e,n.sibling),(n=i(n,a)).return=e,e=n):(r(e,n),(n=Ll(a,e.mode,l)).return=e,e=n),s(e);if(wa(a))return m(e,n,a,l);if(me(a))return g(e,n,a,l);if(c&&xa(e,a),void 0===a&&!u)switch(e.tag){case 1:case 0:throw e=e.type,Error(o(152,e.displayName||e.name||"Component"))}return r(e,n)}}var Sa=Ea(!0),ka=Ea(!1),Ta={},Ca={current:Ta},Aa={current:Ta},La={current:Ta};function Na(e){if(e===Ta)throw Error(o(174));return e}function Pa(e,t){switch(ui(La,t),ui(Aa,e),ui(Ca,Ta),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ie(null,"");break;default:t=Ie(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}li(Ca),ui(Ca,t)}function Ra(){li(Ca),li(Aa),li(La)}function Da(e){Na(La.current);var t=Na(Ca.current),r=Ie(t,e.type);t!==r&&(ui(Aa,e),ui(Ca,r))}function qa(e){Aa.current===e&&(li(Ca),li(Aa))}var Ma={current:0};function Oa(e){for(var t=e;null!==t;){if(13===t.tag){var r=t.memoizedState;if(null!==r&&(null===(r=r.dehydrated)||"$?"===r.data||"$!"===r.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!=(64&t.effectTag))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Ia(e,t){return{responder:e,props:t}}var Ha=$.ReactCurrentDispatcher,Ba=$.ReactCurrentBatchConfig,Ua=0,Va=null,za=null,Fa=null,ja=!1;function Za(){throw Error(o(321))}function Ga(e,t){if(null===t)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!In(e[r],t[r]))return!1;return!0}function Wa(e,t,r,n,i,a){if(Ua=a,Va=t,t.memoizedState=null,t.updateQueue=null,t.expirationTime=0,Ha.current=null===e||null===e.memoizedState?yo:bo,e=r(n,i),t.expirationTime===Ua){a=0;do{if(t.expirationTime=0,!(25>a))throw Error(o(301));a+=1,Fa=za=null,t.updateQueue=null,Ha.current=vo,e=r(n,i)}while(t.expirationTime===Ua)}if(Ha.current=go,t=null!==za&&null!==za.next,Ua=0,Fa=za=Va=null,ja=!1,t)throw Error(o(300));return e}function Ya(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Fa?Va.memoizedState=Fa=e:Fa=Fa.next=e,Fa}function Qa(){if(null===za){var e=Va.alternate;e=null!==e?e.memoizedState:null}else e=za.next;var t=null===Fa?Va.memoizedState:Fa.next;if(null!==t)Fa=t,za=e;else{if(null===e)throw Error(o(310));e={memoizedState:(za=e).memoizedState,baseState:za.baseState,baseQueue:za.baseQueue,queue:za.queue,next:null},null===Fa?Va.memoizedState=Fa=e:Fa=Fa.next=e}return Fa}function $a(e,t){return"function"==typeof t?t(e):t}function Ka(e){var t=Qa(),r=t.queue;if(null===r)throw Error(o(311));r.lastRenderedReducer=e;var n=za,i=n.baseQueue,a=r.pending;if(null!==a){if(null!==i){var s=i.next;i.next=a.next,a.next=s}n.baseQueue=i=a,r.pending=null}if(null!==i){i=i.next,n=n.baseState;var l=s=a=null,u=i;do{var c=u.expirationTime;if(c<Ua){var f={expirationTime:u.expirationTime,suspenseConfig:u.suspenseConfig,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null};null===l?(s=l=f,a=n):l=l.next=f,c>Va.expirationTime&&(Va.expirationTime=c,ol(c))}else null!==l&&(l=l.next={expirationTime:**********,suspenseConfig:u.suspenseConfig,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null}),al(c,u.suspenseConfig),n=u.eagerReducer===e?u.eagerState:e(n,u.action);u=u.next}while(null!==u&&u!==i);null===l?a=n:l.next=s,In(n,t.memoizedState)||(No=!0),t.memoizedState=n,t.baseState=a,t.baseQueue=l,r.lastRenderedState=n}return[t.memoizedState,r.dispatch]}function Xa(e){var t=Qa(),r=t.queue;if(null===r)throw Error(o(311));r.lastRenderedReducer=e;var n=r.dispatch,i=r.pending,a=t.memoizedState;if(null!==i){r.pending=null;var s=i=i.next;do{a=e(a,s.action),s=s.next}while(s!==i);In(a,t.memoizedState)||(No=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),r.lastRenderedState=a}return[a,n]}function Ja(e){var t=Ya();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e=(e=t.queue={pending:null,dispatch:null,lastRenderedReducer:$a,lastRenderedState:e}).dispatch=mo.bind(null,Va,e),[t.memoizedState,e]}function eo(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},null===(t=Va.updateQueue)?(t={lastEffect:null},Va.updateQueue=t,t.lastEffect=e.next=e):null===(r=t.lastEffect)?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e),e}function to(){return Qa().memoizedState}function ro(e,t,r,n){var i=Ya();Va.effectTag|=e,i.memoizedState=eo(1|t,r,void 0,void 0===n?null:n)}function no(e,t,r,n){var i=Qa();n=void 0===n?null:n;var a=void 0;if(null!==za){var o=za.memoizedState;if(a=o.destroy,null!==n&&Ga(n,o.deps))return void eo(t,r,a,n)}Va.effectTag|=e,i.memoizedState=eo(1|t,r,a,n)}function io(e,t){return ro(516,4,e,t)}function ao(e,t){return no(516,4,e,t)}function oo(e,t){return no(4,2,e,t)}function so(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function lo(e,t,r){return r=null!=r?r.concat([e]):null,no(4,2,so.bind(null,t,e),r)}function uo(){}function co(e,t){return Ya().memoizedState=[e,void 0===t?null:t],e}function fo(e,t){var r=Qa();t=void 0===t?null:t;var n=r.memoizedState;return null!==n&&null!==t&&Ga(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function po(e,t){var r=Qa();t=void 0===t?null:t;var n=r.memoizedState;return null!==n&&null!==t&&Ga(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function ho(e,t,r){var n=Ui();zi(98>n?98:n,(function(){e(!0)})),zi(97<n?97:n,(function(){var n=Ba.suspense;Ba.suspense=void 0===t?null:t;try{e(!1),r()}finally{Ba.suspense=n}}))}function mo(e,t,r){var n=Gs(),i=pa.suspense;i={expirationTime:n=Ws(n,e,i),suspenseConfig:i,action:r,eagerReducer:null,eagerState:null,next:null};var a=t.pending;if(null===a?i.next=i:(i.next=a.next,a.next=i),t.pending=i,a=e.alternate,e===Va||null!==a&&a===Va)ja=!0,i.expirationTime=Ua,Va.expirationTime=Ua;else{if(0===e.expirationTime&&(null===a||0===a.expirationTime)&&null!==(a=t.lastRenderedReducer))try{var o=t.lastRenderedState,s=a(o,r);if(i.eagerReducer=a,i.eagerState=s,In(s,o))return}catch(e){}Ys(e,n)}}var go={readContext:na,useCallback:Za,useContext:Za,useEffect:Za,useImperativeHandle:Za,useLayoutEffect:Za,useMemo:Za,useReducer:Za,useRef:Za,useState:Za,useDebugValue:Za,useResponder:Za,useDeferredValue:Za,useTransition:Za},yo={readContext:na,useCallback:co,useContext:na,useEffect:io,useImperativeHandle:function(e,t,r){return r=null!=r?r.concat([e]):null,ro(4,2,so.bind(null,t,e),r)},useLayoutEffect:function(e,t){return ro(4,2,e,t)},useMemo:function(e,t){var r=Ya();return t=void 0===t?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=Ya();return t=void 0!==r?r(t):t,n.memoizedState=n.baseState=t,e=(e=n.queue={pending:null,dispatch:null,lastRenderedReducer:e,lastRenderedState:t}).dispatch=mo.bind(null,Va,e),[n.memoizedState,e]},useRef:function(e){return e={current:e},Ya().memoizedState=e},useState:Ja,useDebugValue:uo,useResponder:Ia,useDeferredValue:function(e,t){var r=Ja(e),n=r[0],i=r[1];return io((function(){var r=Ba.suspense;Ba.suspense=void 0===t?null:t;try{i(e)}finally{Ba.suspense=r}}),[e,t]),n},useTransition:function(e){var t=Ja(!1),r=t[0];return t=t[1],[co(ho.bind(null,t,e),[t,e]),r]}},bo={readContext:na,useCallback:fo,useContext:na,useEffect:ao,useImperativeHandle:lo,useLayoutEffect:oo,useMemo:po,useReducer:Ka,useRef:to,useState:function(){return Ka($a)},useDebugValue:uo,useResponder:Ia,useDeferredValue:function(e,t){var r=Ka($a),n=r[0],i=r[1];return ao((function(){var r=Ba.suspense;Ba.suspense=void 0===t?null:t;try{i(e)}finally{Ba.suspense=r}}),[e,t]),n},useTransition:function(e){var t=Ka($a),r=t[0];return t=t[1],[fo(ho.bind(null,t,e),[t,e]),r]}},vo={readContext:na,useCallback:fo,useContext:na,useEffect:ao,useImperativeHandle:lo,useLayoutEffect:oo,useMemo:po,useReducer:Xa,useRef:to,useState:function(){return Xa($a)},useDebugValue:uo,useResponder:Ia,useDeferredValue:function(e,t){var r=Xa($a),n=r[0],i=r[1];return ao((function(){var r=Ba.suspense;Ba.suspense=void 0===t?null:t;try{i(e)}finally{Ba.suspense=r}}),[e,t]),n},useTransition:function(e){var t=Xa($a),r=t[0];return t=t[1],[fo(ho.bind(null,t,e),[t,e]),r]}},wo=null,_o=null,xo=!1;function Eo(e,t){var r=Sl(5,null,null,0);r.elementType="DELETED",r.type="DELETED",r.stateNode=t,r.return=e,r.effectTag=8,null!==e.lastEffect?(e.lastEffect.nextEffect=r,e.lastEffect=r):e.firstEffect=e.lastEffect=r}function So(e,t){switch(e.tag){case 5:var r=e.type;return null!==(t=1!==t.nodeType||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,!0);case 13:default:return!1}}function ko(e){if(xo){var t=_o;if(t){var r=t;if(!So(e,t)){if(!(t=wr(r.nextSibling))||!So(e,t))return e.effectTag=-1025&e.effectTag|2,xo=!1,void(wo=e);Eo(wo,r)}wo=e,_o=wr(t.firstChild)}else e.effectTag=-1025&e.effectTag|2,xo=!1,wo=e}}function To(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;wo=e}function Co(e){if(e!==wo)return!1;if(!xo)return To(e),xo=!0,!1;var t=e.type;if(5!==e.tag||"head"!==t&&"body"!==t&&!yr(t,e.memoizedProps))for(t=_o;t;)Eo(e,t),t=wr(t.nextSibling);if(To(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var r=e.data;if("/$"===r){if(0===t){_o=wr(e.nextSibling);break e}t--}else"$"!==r&&"$!"!==r&&"$?"!==r||t++}e=e.nextSibling}_o=null}}else _o=wo?wr(e.stateNode.nextSibling):null;return!0}function Ao(){_o=wo=null,xo=!1}var Lo=$.ReactCurrentOwner,No=!1;function Po(e,t,r,n){t.child=null===e?ka(t,null,r,n):Sa(t,e.child,r,n)}function Ro(e,t,r,n,i){r=r.render;var a=t.ref;return ra(t,i),n=Wa(e,t,r,n,a,i),null===e||No?(t.effectTag|=1,Po(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=i&&(e.expirationTime=0),Yo(e,t,i))}function Do(e,t,r,n,i,a){if(null===e){var o=r.type;return"function"!=typeof o||kl(o)||void 0!==o.defaultProps||null!==r.compare||void 0!==r.defaultProps?((e=Cl(r.type,null,n,null,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,qo(e,t,o,n,i,a))}return o=e.child,i<a&&(i=o.memoizedProps,(r=null!==(r=r.compare)?r:Bn)(i,n)&&e.ref===t.ref)?Yo(e,t,a):(t.effectTag|=1,(e=Tl(o,n)).ref=t.ref,e.return=t,t.child=e)}function qo(e,t,r,n,i,a){return null!==e&&Bn(e.memoizedProps,n)&&e.ref===t.ref&&(No=!1,i<a)?(t.expirationTime=e.expirationTime,Yo(e,t,a)):Oo(e,t,r,n,a)}function Mo(e,t){var r=t.ref;(null===e&&null!==r||null!==e&&e.ref!==r)&&(t.effectTag|=128)}function Oo(e,t,r,n,i){var a=mi(r)?di:fi.current;return a=hi(t,a),ra(t,i),r=Wa(e,t,r,n,a,i),null===e||No?(t.effectTag|=1,Po(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=i&&(e.expirationTime=0),Yo(e,t,i))}function Io(e,t,r,n,i){if(mi(r)){var a=!0;vi(t)}else a=!1;if(ra(t,i),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),ya(t,r,n),va(t,r,n,i),n=!0;else if(null===e){var o=t.stateNode,s=t.memoizedProps;o.props=s;var l=o.context,u=r.contextType;"object"==typeof u&&null!==u?u=na(u):u=hi(t,u=mi(r)?di:fi.current);var c=r.getDerivedStateFromProps,f="function"==typeof c||"function"==typeof o.getSnapshotBeforeUpdate;f||"function"!=typeof o.UNSAFE_componentWillReceiveProps&&"function"!=typeof o.componentWillReceiveProps||(s!==n||l!==u)&&ba(t,o,n,u),ia=!1;var p=t.memoizedState;o.state=p,ca(t,n,o,i),l=t.memoizedState,s!==n||p!==l||pi.current||ia?("function"==typeof c&&(ha(t,r,c,n),l=t.memoizedState),(s=ia||ga(t,r,s,n,p,l,u))?(f||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||("function"==typeof o.componentWillMount&&o.componentWillMount(),"function"==typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"==typeof o.componentDidMount&&(t.effectTag|=4)):("function"==typeof o.componentDidMount&&(t.effectTag|=4),t.memoizedProps=n,t.memoizedState=l),o.props=n,o.state=l,o.context=u,n=s):("function"==typeof o.componentDidMount&&(t.effectTag|=4),n=!1)}else o=t.stateNode,oa(e,t),s=t.memoizedProps,o.props=t.type===t.elementType?s:Yi(t.type,s),l=o.context,"object"==typeof(u=r.contextType)&&null!==u?u=na(u):u=hi(t,u=mi(r)?di:fi.current),(f="function"==typeof(c=r.getDerivedStateFromProps)||"function"==typeof o.getSnapshotBeforeUpdate)||"function"!=typeof o.UNSAFE_componentWillReceiveProps&&"function"!=typeof o.componentWillReceiveProps||(s!==n||l!==u)&&ba(t,o,n,u),ia=!1,l=t.memoizedState,o.state=l,ca(t,n,o,i),p=t.memoizedState,s!==n||l!==p||pi.current||ia?("function"==typeof c&&(ha(t,r,c,n),p=t.memoizedState),(c=ia||ga(t,r,s,n,l,p,u))?(f||"function"!=typeof o.UNSAFE_componentWillUpdate&&"function"!=typeof o.componentWillUpdate||("function"==typeof o.componentWillUpdate&&o.componentWillUpdate(n,p,u),"function"==typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(n,p,u)),"function"==typeof o.componentDidUpdate&&(t.effectTag|=4),"function"==typeof o.getSnapshotBeforeUpdate&&(t.effectTag|=256)):("function"!=typeof o.componentDidUpdate||s===e.memoizedProps&&l===e.memoizedState||(t.effectTag|=4),"function"!=typeof o.getSnapshotBeforeUpdate||s===e.memoizedProps&&l===e.memoizedState||(t.effectTag|=256),t.memoizedProps=n,t.memoizedState=p),o.props=n,o.state=p,o.context=u,n=c):("function"!=typeof o.componentDidUpdate||s===e.memoizedProps&&l===e.memoizedState||(t.effectTag|=4),"function"!=typeof o.getSnapshotBeforeUpdate||s===e.memoizedProps&&l===e.memoizedState||(t.effectTag|=256),n=!1);return Ho(e,t,r,n,a,i)}function Ho(e,t,r,n,i,a){Mo(e,t);var o=0!=(64&t.effectTag);if(!n&&!o)return i&&wi(t,r,!1),Yo(e,t,a);n=t.stateNode,Lo.current=t;var s=o&&"function"!=typeof r.getDerivedStateFromError?null:n.render();return t.effectTag|=1,null!==e&&o?(t.child=Sa(t,e.child,null,a),t.child=Sa(t,null,s,a)):Po(e,t,s,a),t.memoizedState=n.state,i&&wi(t,r,!0),t.child}function Bo(e){var t=e.stateNode;t.pendingContext?yi(0,t.pendingContext,t.pendingContext!==t.context):t.context&&yi(0,t.context,!1),Pa(e,t.containerInfo)}var Uo,Vo,zo,Fo={dehydrated:null,retryTime:0};function jo(e,t,r){var n,i=t.mode,a=t.pendingProps,o=Ma.current,s=!1;if((n=0!=(64&t.effectTag))||(n=0!=(2&o)&&(null===e||null!==e.memoizedState)),n?(s=!0,t.effectTag&=-65):null!==e&&null===e.memoizedState||void 0===a.fallback||!0===a.unstable_avoidThisFallback||(o|=1),ui(Ma,1&o),null===e){if(void 0!==a.fallback&&ko(t),s){if(s=a.fallback,(a=Al(null,i,0,null)).return=t,0==(2&t.mode))for(e=null!==t.memoizedState?t.child.child:t.child,a.child=e;null!==e;)e.return=a,e=e.sibling;return(r=Al(s,i,r,null)).return=t,a.sibling=r,t.memoizedState=Fo,t.child=a,r}return i=a.children,t.memoizedState=null,t.child=ka(t,null,i,r)}if(null!==e.memoizedState){if(i=(e=e.child).sibling,s){if(a=a.fallback,(r=Tl(e,e.pendingProps)).return=t,0==(2&t.mode)&&(s=null!==t.memoizedState?t.child.child:t.child)!==e.child)for(r.child=s;null!==s;)s.return=r,s=s.sibling;return(i=Tl(i,a)).return=t,r.sibling=i,r.childExpirationTime=0,t.memoizedState=Fo,t.child=r,i}return r=Sa(t,e.child,a.children,r),t.memoizedState=null,t.child=r}if(e=e.child,s){if(s=a.fallback,(a=Al(null,i,0,null)).return=t,a.child=e,null!==e&&(e.return=a),0==(2&t.mode))for(e=null!==t.memoizedState?t.child.child:t.child,a.child=e;null!==e;)e.return=a,e=e.sibling;return(r=Al(s,i,r,null)).return=t,a.sibling=r,r.effectTag|=2,a.childExpirationTime=0,t.memoizedState=Fo,t.child=a,r}return t.memoizedState=null,t.child=Sa(t,e,a.children,r)}function Zo(e,t){e.expirationTime<t&&(e.expirationTime=t);var r=e.alternate;null!==r&&r.expirationTime<t&&(r.expirationTime=t),ta(e.return,t)}function Go(e,t,r,n,i,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailExpiration:0,tailMode:i,lastEffect:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=n,o.tail=r,o.tailExpiration=0,o.tailMode=i,o.lastEffect=a)}function Wo(e,t,r){var n=t.pendingProps,i=n.revealOrder,a=n.tail;if(Po(e,t,n.children,r),0!=(2&(n=Ma.current)))n=1&n|2,t.effectTag|=64;else{if(null!==e&&0!=(64&e.effectTag))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Zo(e,r);else if(19===e.tag)Zo(e,r);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(ui(Ma,n),0==(2&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(r=t.child,i=null;null!==r;)null!==(e=r.alternate)&&null===Oa(e)&&(i=r),r=r.sibling;null===(r=i)?(i=t.child,t.child=null):(i=r.sibling,r.sibling=null),Go(t,!1,i,r,a,t.lastEffect);break;case"backwards":for(r=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===Oa(e)){t.child=i;break}e=i.sibling,i.sibling=r,r=i,i=e}Go(t,!0,r,null,a,t.lastEffect);break;case"together":Go(t,!1,null,null,void 0,t.lastEffect);break;default:t.memoizedState=null}return t.child}function Yo(e,t,r){null!==e&&(t.dependencies=e.dependencies);var n=t.expirationTime;if(0!==n&&ol(n),t.childExpirationTime<r)return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(r=Tl(e=t.child,e.pendingProps),t.child=r,r.return=t;null!==e.sibling;)e=e.sibling,(r=r.sibling=Tl(e,e.pendingProps)).return=t;r.sibling=null}return t.child}function Qo(e,t){switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;null!==t;)null!==t.alternate&&(r=t),t=t.sibling;null===r?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;null!==r;)null!==r.alternate&&(n=r),r=r.sibling;null===n?t||null===e.tail?e.tail=null:e.tail.sibling=null:n.sibling=null}}function $o(e,t,r){var n=t.pendingProps;switch(t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:return mi(t.type)&&gi(),null;case 3:return Ra(),li(pi),li(fi),(r=t.stateNode).pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||!Co(t)||(t.effectTag|=4),null;case 5:qa(t),r=Na(La.current);var a=t.type;if(null!==e&&null!=t.stateNode)Vo(e,t,a,n,r),e.ref!==t.ref&&(t.effectTag|=128);else{if(!n){if(null===t.stateNode)throw Error(o(166));return null}if(e=Na(Ca.current),Co(t)){n=t.stateNode,a=t.type;var s=t.memoizedProps;switch(n[Er]=t,n[Sr]=s,a){case"iframe":case"object":case"embed":Wt("load",n);break;case"video":case"audio":for(e=0;e<$e.length;e++)Wt($e[e],n);break;case"source":Wt("error",n);break;case"img":case"image":case"link":Wt("error",n),Wt("load",n);break;case"form":Wt("reset",n),Wt("submit",n);break;case"details":Wt("toggle",n);break;case"input":Ee(n,s),Wt("invalid",n),sr(r,"onChange");break;case"select":n._wrapperState={wasMultiple:!!s.multiple},Wt("invalid",n),sr(r,"onChange");break;case"textarea":Pe(n,s),Wt("invalid",n),sr(r,"onChange")}for(var l in ir(a,s),e=null,s)if(s.hasOwnProperty(l)){var u=s[l];"children"===l?"string"==typeof u?n.textContent!==u&&(e=["children",u]):"number"==typeof u&&n.textContent!==""+u&&(e=["children",""+u]):S.hasOwnProperty(l)&&null!=u&&sr(r,l)}switch(a){case"input":we(n),Te(n,s,!0);break;case"textarea":we(n),De(n);break;case"select":case"option":break;default:"function"==typeof s.onClick&&(n.onclick=lr)}r=e,t.updateQueue=r,null!==r&&(t.effectTag|=4)}else{switch(l=9===r.nodeType?r:r.ownerDocument,e===or&&(e=Oe(a)),e===or?"script"===a?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof n.is?e=l.createElement(a,{is:n.is}):(e=l.createElement(a),"select"===a&&(l=e,n.multiple?l.multiple=!0:n.size&&(l.size=n.size))):e=l.createElementNS(e,a),e[Er]=t,e[Sr]=n,Uo(e,t),t.stateNode=e,l=ar(a,n),a){case"iframe":case"object":case"embed":Wt("load",e),u=n;break;case"video":case"audio":for(u=0;u<$e.length;u++)Wt($e[u],e);u=n;break;case"source":Wt("error",e),u=n;break;case"img":case"image":case"link":Wt("error",e),Wt("load",e),u=n;break;case"form":Wt("reset",e),Wt("submit",e),u=n;break;case"details":Wt("toggle",e),u=n;break;case"input":Ee(e,n),u=xe(e,n),Wt("invalid",e),sr(r,"onChange");break;case"option":u=Ae(e,n);break;case"select":e._wrapperState={wasMultiple:!!n.multiple},u=i({},n,{value:void 0}),Wt("invalid",e),sr(r,"onChange");break;case"textarea":Pe(e,n),u=Ne(e,n),Wt("invalid",e),sr(r,"onChange");break;default:u=n}ir(a,u);var c=u;for(s in c)if(c.hasOwnProperty(s)){var f=c[s];"style"===s?rr(e,f):"dangerouslySetInnerHTML"===s?null!=(f=f?f.__html:void 0)&&Be(e,f):"children"===s?"string"==typeof f?("textarea"!==a||""!==f)&&Ue(e,f):"number"==typeof f&&Ue(e,""+f):"suppressContentEditableWarning"!==s&&"suppressHydrationWarning"!==s&&"autoFocus"!==s&&(S.hasOwnProperty(s)?null!=f&&sr(r,s):null!=f&&K(e,s,f,l))}switch(a){case"input":we(e),Te(e,n,!1);break;case"textarea":we(e),De(e);break;case"option":null!=n.value&&e.setAttribute("value",""+be(n.value));break;case"select":e.multiple=!!n.multiple,null!=(r=n.value)?Le(e,!!n.multiple,r,!1):null!=n.defaultValue&&Le(e,!!n.multiple,n.defaultValue,!0);break;default:"function"==typeof u.onClick&&(e.onclick=lr)}gr(a,n)&&(t.effectTag|=4)}null!==t.ref&&(t.effectTag|=128)}return null;case 6:if(e&&null!=t.stateNode)zo(0,t,e.memoizedProps,n);else{if("string"!=typeof n&&null===t.stateNode)throw Error(o(166));r=Na(La.current),Na(Ca.current),Co(t)?(r=t.stateNode,n=t.memoizedProps,r[Er]=t,r.nodeValue!==n&&(t.effectTag|=4)):((r=(9===r.nodeType?r:r.ownerDocument).createTextNode(n))[Er]=t,t.stateNode=r)}return null;case 13:return li(Ma),n=t.memoizedState,0!=(64&t.effectTag)?(t.expirationTime=r,t):(r=null!==n,n=!1,null===e?void 0!==t.memoizedProps.fallback&&Co(t):(n=null!==(a=e.memoizedState),r||null===a||null!==(a=e.child.sibling)&&(null!==(s=t.firstEffect)?(t.firstEffect=a,a.nextEffect=s):(t.firstEffect=t.lastEffect=a,a.nextEffect=null),a.effectTag=8)),r&&!n&&0!=(2&t.mode)&&(null===e&&!0!==t.memoizedProps.unstable_avoidThisFallback||0!=(1&Ma.current)?Cs===ws&&(Cs=_s):(Cs!==ws&&Cs!==_s||(Cs=xs),0!==Rs&&null!==Ss&&(Dl(Ss,Ts),ql(Ss,Rs)))),(r||n)&&(t.effectTag|=4),null);case 4:return Ra(),null;case 10:return ea(t),null;case 17:return mi(t.type)&&gi(),null;case 19:if(li(Ma),null===(n=t.memoizedState))return null;if(a=0!=(64&t.effectTag),null===(s=n.rendering)){if(a)Qo(n,!1);else if(Cs!==ws||null!==e&&0!=(64&e.effectTag))for(s=t.child;null!==s;){if(null!==(e=Oa(s))){for(t.effectTag|=64,Qo(n,!1),null!==(a=e.updateQueue)&&(t.updateQueue=a,t.effectTag|=4),null===n.lastEffect&&(t.firstEffect=null),t.lastEffect=n.lastEffect,n=t.child;null!==n;)s=r,(a=n).effectTag&=2,a.nextEffect=null,a.firstEffect=null,a.lastEffect=null,null===(e=a.alternate)?(a.childExpirationTime=0,a.expirationTime=s,a.child=null,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null):(a.childExpirationTime=e.childExpirationTime,a.expirationTime=e.expirationTime,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,s=e.dependencies,a.dependencies=null===s?null:{expirationTime:s.expirationTime,firstContext:s.firstContext,responders:s.responders}),n=n.sibling;return ui(Ma,1&Ma.current|2),t.child}s=s.sibling}}else{if(!a)if(null!==(e=Oa(s))){if(t.effectTag|=64,a=!0,null!==(r=e.updateQueue)&&(t.updateQueue=r,t.effectTag|=4),Qo(n,!0),null===n.tail&&"hidden"===n.tailMode&&!s.alternate)return null!==(t=t.lastEffect=n.lastEffect)&&(t.nextEffect=null),null}else 2*Bi()-n.renderingStartTime>n.tailExpiration&&1<r&&(t.effectTag|=64,a=!0,Qo(n,!1),t.expirationTime=t.childExpirationTime=r-1);n.isBackwards?(s.sibling=t.child,t.child=s):(null!==(r=n.last)?r.sibling=s:t.child=s,n.last=s)}return null!==n.tail?(0===n.tailExpiration&&(n.tailExpiration=Bi()+500),r=n.tail,n.rendering=r,n.tail=r.sibling,n.lastEffect=t.lastEffect,n.renderingStartTime=Bi(),r.sibling=null,t=Ma.current,ui(Ma,a?1&t|2:1&t),r):null}throw Error(o(156,t.tag))}function Ko(e){switch(e.tag){case 1:mi(e.type)&&gi();var t=e.effectTag;return 4096&t?(e.effectTag=-4097&t|64,e):null;case 3:if(Ra(),li(pi),li(fi),0!=(64&(t=e.effectTag)))throw Error(o(285));return e.effectTag=-4097&t|64,e;case 5:return qa(e),null;case 13:return li(Ma),4096&(t=e.effectTag)?(e.effectTag=-4097&t|64,e):null;case 19:return li(Ma),null;case 4:return Ra(),null;case 10:return ea(e),null;default:return null}}function Xo(e,t){return{value:e,source:t,stack:ye(t)}}Uo=function(e,t){for(var r=t.child;null!==r;){if(5===r.tag||6===r.tag)e.appendChild(r.stateNode);else if(4!==r.tag&&null!==r.child){r.child.return=r,r=r.child;continue}if(r===t)break;for(;null===r.sibling;){if(null===r.return||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Vo=function(e,t,r,n,a){var o=e.memoizedProps;if(o!==n){var s,l,u=t.stateNode;switch(Na(Ca.current),e=null,r){case"input":o=xe(u,o),n=xe(u,n),e=[];break;case"option":o=Ae(u,o),n=Ae(u,n),e=[];break;case"select":o=i({},o,{value:void 0}),n=i({},n,{value:void 0}),e=[];break;case"textarea":o=Ne(u,o),n=Ne(u,n),e=[];break;default:"function"!=typeof o.onClick&&"function"==typeof n.onClick&&(u.onclick=lr)}for(s in ir(r,n),r=null,o)if(!n.hasOwnProperty(s)&&o.hasOwnProperty(s)&&null!=o[s])if("style"===s)for(l in u=o[s])u.hasOwnProperty(l)&&(r||(r={}),r[l]="");else"dangerouslySetInnerHTML"!==s&&"children"!==s&&"suppressContentEditableWarning"!==s&&"suppressHydrationWarning"!==s&&"autoFocus"!==s&&(S.hasOwnProperty(s)?e||(e=[]):(e=e||[]).push(s,null));for(s in n){var c=n[s];if(u=null!=o?o[s]:void 0,n.hasOwnProperty(s)&&c!==u&&(null!=c||null!=u))if("style"===s)if(u){for(l in u)!u.hasOwnProperty(l)||c&&c.hasOwnProperty(l)||(r||(r={}),r[l]="");for(l in c)c.hasOwnProperty(l)&&u[l]!==c[l]&&(r||(r={}),r[l]=c[l])}else r||(e||(e=[]),e.push(s,r)),r=c;else"dangerouslySetInnerHTML"===s?(c=c?c.__html:void 0,u=u?u.__html:void 0,null!=c&&u!==c&&(e=e||[]).push(s,c)):"children"===s?u===c||"string"!=typeof c&&"number"!=typeof c||(e=e||[]).push(s,""+c):"suppressContentEditableWarning"!==s&&"suppressHydrationWarning"!==s&&(S.hasOwnProperty(s)?(null!=c&&sr(a,s),e||u===c||(e=[])):(e=e||[]).push(s,c))}r&&(e=e||[]).push("style",r),a=e,(t.updateQueue=a)&&(t.effectTag|=4)}},zo=function(e,t,r,n){r!==n&&(t.effectTag|=4)};var Jo="function"==typeof WeakSet?WeakSet:Set;function es(e,t){var r=t.source,n=t.stack;null===n&&null!==r&&(n=ye(r)),null!==r&&ge(r.type),t=t.value,null!==e&&1===e.tag&&ge(e.type);try{console.error(t)}catch(e){setTimeout((function(){throw e}))}}function ts(e){var t=e.ref;if(null!==t)if("function"==typeof t)try{t(null)}catch(t){bl(e,t)}else t.current=null}function rs(e,t){switch(t.tag){case 0:case 11:case 15:case 22:return;case 1:if(256&t.effectTag&&null!==e){var r=e.memoizedProps,n=e.memoizedState;t=(e=t.stateNode).getSnapshotBeforeUpdate(t.elementType===t.type?r:Yi(t.type,r),n),e.__reactInternalSnapshotBeforeUpdate=t}return;case 3:case 5:case 6:case 4:case 17:return}throw Error(o(163))}function ns(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var r=t=t.next;do{if((r.tag&e)===e){var n=r.destroy;r.destroy=void 0,void 0!==n&&n()}r=r.next}while(r!==t)}}function is(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function as(e,t,r){switch(r.tag){case 0:case 11:case 15:case 22:return void is(3,r);case 1:if(e=r.stateNode,4&r.effectTag)if(null===t)e.componentDidMount();else{var n=r.elementType===r.type?t.memoizedProps:Yi(r.type,t.memoizedProps);e.componentDidUpdate(n,t.memoizedState,e.__reactInternalSnapshotBeforeUpdate)}return void(null!==(t=r.updateQueue)&&fa(r,t,e));case 3:if(null!==(t=r.updateQueue)){if(e=null,null!==r.child)switch(r.child.tag){case 5:e=r.child.stateNode;break;case 1:e=r.child.stateNode}fa(r,t,e)}return;case 5:return e=r.stateNode,void(null===t&&4&r.effectTag&&gr(r.type,r.memoizedProps)&&e.focus());case 6:case 4:case 12:return;case 13:return void(null===r.memoizedState&&(r=r.alternate,null!==r&&(r=r.memoizedState,null!==r&&(r=r.dehydrated,null!==r&&Ot(r)))));case 19:case 17:case 20:case 21:return}throw Error(o(163))}function os(e,t,r){switch("function"==typeof xl&&xl(t),t.tag){case 0:case 11:case 14:case 15:case 22:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var n=e.next;zi(97<r?97:r,(function(){var e=n;do{var r=e.destroy;if(void 0!==r){var i=t;try{r()}catch(e){bl(i,e)}}e=e.next}while(e!==n)}))}break;case 1:ts(t),"function"==typeof(r=t.stateNode).componentWillUnmount&&function(e,t){try{t.props=e.memoizedProps,t.state=e.memoizedState,t.componentWillUnmount()}catch(t){bl(e,t)}}(t,r);break;case 5:ts(t);break;case 4:cs(e,t,r)}}function ss(e){var t=e.alternate;e.return=null,e.child=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.alternate=null,e.firstEffect=null,e.lastEffect=null,e.pendingProps=null,e.memoizedProps=null,e.stateNode=null,null!==t&&ss(t)}function ls(e){return 5===e.tag||3===e.tag||4===e.tag}function us(e){e:{for(var t=e.return;null!==t;){if(ls(t)){var r=t;break e}t=t.return}throw Error(o(160))}switch(t=r.stateNode,r.tag){case 5:var n=!1;break;case 3:case 4:t=t.containerInfo,n=!0;break;default:throw Error(o(161))}16&r.effectTag&&(Ue(t,""),r.effectTag&=-17);e:t:for(r=e;;){for(;null===r.sibling;){if(null===r.return||ls(r.return)){r=null;break e}r=r.return}for(r.sibling.return=r.return,r=r.sibling;5!==r.tag&&6!==r.tag&&18!==r.tag;){if(2&r.effectTag)continue t;if(null===r.child||4===r.tag)continue t;r.child.return=r,r=r.child}if(!(2&r.effectTag)){r=r.stateNode;break e}}n?function e(t,r,n){var i=t.tag,a=5===i||6===i;if(a)t=a?t.stateNode:t.stateNode.instance,r?8===n.nodeType?n.parentNode.insertBefore(t,r):n.insertBefore(t,r):(8===n.nodeType?(r=n.parentNode).insertBefore(t,n):(r=n).appendChild(t),null!==(n=n._reactRootContainer)&&void 0!==n||null!==r.onclick||(r.onclick=lr));else if(4!==i&&null!==(t=t.child))for(e(t,r,n),t=t.sibling;null!==t;)e(t,r,n),t=t.sibling}(e,r,t):function e(t,r,n){var i=t.tag,a=5===i||6===i;if(a)t=a?t.stateNode:t.stateNode.instance,r?n.insertBefore(t,r):n.appendChild(t);else if(4!==i&&null!==(t=t.child))for(e(t,r,n),t=t.sibling;null!==t;)e(t,r,n),t=t.sibling}(e,r,t)}function cs(e,t,r){for(var n,i,a=t,s=!1;;){if(!s){s=a.return;e:for(;;){if(null===s)throw Error(o(160));switch(n=s.stateNode,s.tag){case 5:i=!1;break e;case 3:case 4:n=n.containerInfo,i=!0;break e}s=s.return}s=!0}if(5===a.tag||6===a.tag){e:for(var l=e,u=a,c=r,f=u;;)if(os(l,f,c),null!==f.child&&4!==f.tag)f.child.return=f,f=f.child;else{if(f===u)break e;for(;null===f.sibling;){if(null===f.return||f.return===u)break e;f=f.return}f.sibling.return=f.return,f=f.sibling}i?(l=n,u=a.stateNode,8===l.nodeType?l.parentNode.removeChild(u):l.removeChild(u)):n.removeChild(a.stateNode)}else if(4===a.tag){if(null!==a.child){n=a.stateNode.containerInfo,i=!0,a.child.return=a,a=a.child;continue}}else if(os(e,a,r),null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break;for(;null===a.sibling;){if(null===a.return||a.return===t)return;4===(a=a.return).tag&&(s=!1)}a.sibling.return=a.return,a=a.sibling}}function fs(e,t){switch(t.tag){case 0:case 11:case 14:case 15:case 22:return void ns(3,t);case 1:return;case 5:var r=t.stateNode;if(null!=r){var n=t.memoizedProps,i=null!==e?e.memoizedProps:n;e=t.type;var a=t.updateQueue;if(t.updateQueue=null,null!==a){for(r[Sr]=n,"input"===e&&"radio"===n.type&&null!=n.name&&Se(r,n),ar(e,i),t=ar(e,n),i=0;i<a.length;i+=2){var s=a[i],l=a[i+1];"style"===s?rr(r,l):"dangerouslySetInnerHTML"===s?Be(r,l):"children"===s?Ue(r,l):K(r,s,l,t)}switch(e){case"input":ke(r,n);break;case"textarea":Re(r,n);break;case"select":t=r._wrapperState.wasMultiple,r._wrapperState.wasMultiple=!!n.multiple,null!=(e=n.value)?Le(r,!!n.multiple,e,!1):t!==!!n.multiple&&(null!=n.defaultValue?Le(r,!!n.multiple,n.defaultValue,!0):Le(r,!!n.multiple,n.multiple?[]:"",!1))}}}return;case 6:if(null===t.stateNode)throw Error(o(162));return void(t.stateNode.nodeValue=t.memoizedProps);case 3:return void((t=t.stateNode).hydrate&&(t.hydrate=!1,Ot(t.containerInfo)));case 12:return;case 13:if(r=t,null===t.memoizedState?n=!1:(n=!0,r=t.child,qs=Bi()),null!==r)e:for(e=r;;){if(5===e.tag)a=e.stateNode,n?"function"==typeof(a=a.style).setProperty?a.setProperty("display","none","important"):a.display="none":(a=e.stateNode,i=null!=(i=e.memoizedProps.style)&&i.hasOwnProperty("display")?i.display:null,a.style.display=tr("display",i));else if(6===e.tag)e.stateNode.nodeValue=n?"":e.memoizedProps;else{if(13===e.tag&&null!==e.memoizedState&&null===e.memoizedState.dehydrated){(a=e.child.sibling).return=e,e=a;continue}if(null!==e.child){e.child.return=e,e=e.child;continue}}if(e===r)break;for(;null===e.sibling;){if(null===e.return||e.return===r)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}return void ps(t);case 19:return void ps(t);case 17:return}throw Error(o(163))}function ps(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var r=e.stateNode;null===r&&(r=e.stateNode=new Jo),t.forEach((function(t){var n=wl.bind(null,e,t);r.has(t)||(r.add(t),t.then(n,n))}))}}var ds="function"==typeof WeakMap?WeakMap:Map;function hs(e,t,r){(r=sa(r,null)).tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){Os||(Os=!0,Is=n),es(e,t)},r}function ms(e,t,r){(r=sa(r,null)).tag=3;var n=e.type.getDerivedStateFromError;if("function"==typeof n){var i=t.value;r.payload=function(){return es(e,t),n(i)}}var a=e.stateNode;return null!==a&&"function"==typeof a.componentDidCatch&&(r.callback=function(){"function"!=typeof n&&(null===Hs?Hs=new Set([this]):Hs.add(this),es(e,t));var r=t.stack;this.componentDidCatch(t.value,{componentStack:null!==r?r:""})}),r}var gs,ys=Math.ceil,bs=$.ReactCurrentDispatcher,vs=$.ReactCurrentOwner,ws=0,_s=3,xs=4,Es=0,Ss=null,ks=null,Ts=0,Cs=ws,As=null,Ls=**********,Ns=**********,Ps=null,Rs=0,Ds=!1,qs=0,Ms=null,Os=!1,Is=null,Hs=null,Bs=!1,Us=null,Vs=90,zs=null,Fs=0,js=null,Zs=0;function Gs(){return 0!=(48&Es)?1073741821-(Bi()/10|0):0!==Zs?Zs:Zs=1073741821-(Bi()/10|0)}function Ws(e,t,r){if(0==(2&(t=t.mode)))return **********;var n=Ui();if(0==(4&t))return 99===n?**********:1073741822;if(0!=(16&Es))return Ts;if(null!==r)e=Wi(e,0|r.timeoutMs||5e3,250);else switch(n){case 99:e=**********;break;case 98:e=Wi(e,150,100);break;case 97:case 96:e=Wi(e,5e3,250);break;case 95:e=2;break;default:throw Error(o(326))}return null!==Ss&&e===Ts&&--e,e}function Ys(e,t){if(50<Fs)throw Fs=0,js=null,Error(o(185));if(null!==(e=Qs(e,t))){var r=Ui();**********===t?0!=(8&Es)&&0==(48&Es)?Js(e):(Ks(e),0===Es&&Zi()):Ks(e),0==(4&Es)||98!==r&&99!==r||(null===zs?zs=new Map([[e,t]]):(void 0===(r=zs.get(e))||r>t)&&zs.set(e,t))}}function Qs(e,t){e.expirationTime<t&&(e.expirationTime=t);var r=e.alternate;null!==r&&r.expirationTime<t&&(r.expirationTime=t);var n=e.return,i=null;if(null===n&&3===e.tag)i=e.stateNode;else for(;null!==n;){if(r=n.alternate,n.childExpirationTime<t&&(n.childExpirationTime=t),null!==r&&r.childExpirationTime<t&&(r.childExpirationTime=t),null===n.return&&3===n.tag){i=n.stateNode;break}n=n.return}return null!==i&&(Ss===i&&(ol(t),Cs===xs&&Dl(i,Ts)),ql(i,t)),i}function $s(e){var t=e.lastExpiredTime;if(0!==t)return t;if(!Rl(e,t=e.firstPendingTime))return t;var r=e.lastPingedTime;return 2>=(e=r>(e=e.nextKnownPendingLevel)?r:e)&&t!==e?0:e}function Ks(e){if(0!==e.lastExpiredTime)e.callbackExpirationTime=**********,e.callbackPriority=99,e.callbackNode=ji(Js.bind(null,e));else{var t=$s(e),r=e.callbackNode;if(0===t)null!==r&&(e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90);else{var n=Gs();if(**********===t?n=99:1===t||2===t?n=95:n=0>=(n=10*(1073741821-t)-10*(1073741821-n))?99:250>=n?98:5250>=n?97:95,null!==r){var i=e.callbackPriority;if(e.callbackExpirationTime===t&&i>=n)return;r!==Ri&&Ei(r)}e.callbackExpirationTime=t,e.callbackPriority=n,t=**********===t?ji(Js.bind(null,e)):Fi(n,Xs.bind(null,e),{timeout:10*(1073741821-t)-Bi()}),e.callbackNode=t}}}function Xs(e,t){if(Zs=0,t)return Ml(e,t=Gs()),Ks(e),null;var r=$s(e);if(0!==r){if(t=e.callbackNode,0!=(48&Es))throw Error(o(327));if(ml(),e===Ss&&r===Ts||rl(e,r),null!==ks){var n=Es;Es|=16;for(var i=il();;)try{ll();break}catch(t){nl(e,t)}if(Ji(),Es=n,bs.current=i,1===Cs)throw t=As,rl(e,r),Dl(e,r),Ks(e),t;if(null===ks)switch(i=e.finishedWork=e.current.alternate,e.finishedExpirationTime=r,n=Cs,Ss=null,n){case ws:case 1:throw Error(o(345));case 2:Ml(e,2<r?2:r);break;case _s:if(Dl(e,r),r===(n=e.lastSuspendedTime)&&(e.nextKnownPendingLevel=fl(i)),**********===Ls&&10<(i=qs+500-Bi())){if(Ds){var a=e.lastPingedTime;if(0===a||a>=r){e.lastPingedTime=r,rl(e,r);break}}if(0!==(a=$s(e))&&a!==r)break;if(0!==n&&n!==r){e.lastPingedTime=n;break}e.timeoutHandle=br(pl.bind(null,e),i);break}pl(e);break;case xs:if(Dl(e,r),r===(n=e.lastSuspendedTime)&&(e.nextKnownPendingLevel=fl(i)),Ds&&(0===(i=e.lastPingedTime)||i>=r)){e.lastPingedTime=r,rl(e,r);break}if(0!==(i=$s(e))&&i!==r)break;if(0!==n&&n!==r){e.lastPingedTime=n;break}if(**********!==Ns?n=10*(1073741821-Ns)-Bi():**********===Ls?n=0:(n=10*(1073741821-Ls)-5e3,0>(n=(i=Bi())-n)&&(n=0),(r=10*(1073741821-r)-i)<(n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*ys(n/1960))-n)&&(n=r)),10<n){e.timeoutHandle=br(pl.bind(null,e),n);break}pl(e);break;case 5:if(**********!==Ls&&null!==Ps){a=Ls;var s=Ps;if(0>=(n=0|s.busyMinDurationMs)?n=0:(i=0|s.busyDelayMs,n=(a=Bi()-(10*(1073741821-a)-(0|s.timeoutMs||5e3)))<=i?0:i+n-a),10<n){Dl(e,r),e.timeoutHandle=br(pl.bind(null,e),n);break}}pl(e);break;default:throw Error(o(329))}if(Ks(e),e.callbackNode===t)return Xs.bind(null,e)}}return null}function Js(e){var t=e.lastExpiredTime;if(t=0!==t?t:**********,0!=(48&Es))throw Error(o(327));if(ml(),e===Ss&&t===Ts||rl(e,t),null!==ks){var r=Es;Es|=16;for(var n=il();;)try{sl();break}catch(t){nl(e,t)}if(Ji(),Es=r,bs.current=n,1===Cs)throw r=As,rl(e,t),Dl(e,t),Ks(e),r;if(null!==ks)throw Error(o(261));e.finishedWork=e.current.alternate,e.finishedExpirationTime=t,Ss=null,pl(e),Ks(e)}return null}function el(e,t){var r=Es;Es|=1;try{return e(t)}finally{0===(Es=r)&&Zi()}}function tl(e,t){var r=Es;Es&=-2,Es|=8;try{return e(t)}finally{0===(Es=r)&&Zi()}}function rl(e,t){e.finishedWork=null,e.finishedExpirationTime=0;var r=e.timeoutHandle;if(-1!==r&&(e.timeoutHandle=-1,vr(r)),null!==ks)for(r=ks.return;null!==r;){var n=r;switch(n.tag){case 1:null!=(n=n.type.childContextTypes)&&gi();break;case 3:Ra(),li(pi),li(fi);break;case 5:qa(n);break;case 4:Ra();break;case 13:case 19:li(Ma);break;case 10:ea(n)}r=r.return}Ss=e,ks=Tl(e.current,null),Ts=t,Cs=ws,As=null,Ns=Ls=**********,Ps=null,Rs=0,Ds=!1}function nl(e,t){for(;;){try{if(Ji(),Ha.current=go,ja)for(var r=Va.memoizedState;null!==r;){var n=r.queue;null!==n&&(n.pending=null),r=r.next}if(Ua=0,Fa=za=Va=null,ja=!1,null===ks||null===ks.return)return Cs=1,As=t,ks=null;e:{var i=e,a=ks.return,o=ks,s=t;if(t=Ts,o.effectTag|=2048,o.firstEffect=o.lastEffect=null,null!==s&&"object"==typeof s&&"function"==typeof s.then){var l=s;if(0==(2&o.mode)){var u=o.alternate;u?(o.updateQueue=u.updateQueue,o.memoizedState=u.memoizedState,o.expirationTime=u.expirationTime):(o.updateQueue=null,o.memoizedState=null)}var c=0!=(1&Ma.current),f=a;do{var p;if(p=13===f.tag){var d=f.memoizedState;if(null!==d)p=null!==d.dehydrated;else{var h=f.memoizedProps;p=void 0!==h.fallback&&(!0!==h.unstable_avoidThisFallback||!c)}}if(p){var m=f.updateQueue;if(null===m){var g=new Set;g.add(l),f.updateQueue=g}else m.add(l);if(0==(2&f.mode)){if(f.effectTag|=64,o.effectTag&=-2981,1===o.tag)if(null===o.alternate)o.tag=17;else{var y=sa(**********,null);y.tag=2,la(o,y)}o.expirationTime=**********;break e}s=void 0,o=t;var b=i.pingCache;if(null===b?(b=i.pingCache=new ds,s=new Set,b.set(l,s)):void 0===(s=b.get(l))&&(s=new Set,b.set(l,s)),!s.has(o)){s.add(o);var v=vl.bind(null,i,l,o);l.then(v,v)}f.effectTag|=4096,f.expirationTime=t;break e}f=f.return}while(null!==f);s=Error((ge(o.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display."+ye(o))}5!==Cs&&(Cs=2),s=Xo(s,o),f=a;do{switch(f.tag){case 3:l=s,f.effectTag|=4096,f.expirationTime=t,ua(f,hs(f,l,t));break e;case 1:l=s;var w=f.type,_=f.stateNode;if(0==(64&f.effectTag)&&("function"==typeof w.getDerivedStateFromError||null!==_&&"function"==typeof _.componentDidCatch&&(null===Hs||!Hs.has(_)))){f.effectTag|=4096,f.expirationTime=t,ua(f,ms(f,l,t));break e}}f=f.return}while(null!==f)}ks=cl(ks)}catch(e){t=e;continue}break}}function il(){var e=bs.current;return bs.current=go,null===e?go:e}function al(e,t){e<Ls&&2<e&&(Ls=e),null!==t&&e<Ns&&2<e&&(Ns=e,Ps=t)}function ol(e){e>Rs&&(Rs=e)}function sl(){for(;null!==ks;)ks=ul(ks)}function ll(){for(;null!==ks&&!Di();)ks=ul(ks)}function ul(e){var t=gs(e.alternate,e,Ts);return e.memoizedProps=e.pendingProps,null===t&&(t=cl(e)),vs.current=null,t}function cl(e){ks=e;do{var t=ks.alternate;if(e=ks.return,0==(2048&ks.effectTag)){if(t=$o(t,ks,Ts),1===Ts||1!==ks.childExpirationTime){for(var r=0,n=ks.child;null!==n;){var i=n.expirationTime,a=n.childExpirationTime;i>r&&(r=i),a>r&&(r=a),n=n.sibling}ks.childExpirationTime=r}if(null!==t)return t;null!==e&&0==(2048&e.effectTag)&&(null===e.firstEffect&&(e.firstEffect=ks.firstEffect),null!==ks.lastEffect&&(null!==e.lastEffect&&(e.lastEffect.nextEffect=ks.firstEffect),e.lastEffect=ks.lastEffect),1<ks.effectTag&&(null!==e.lastEffect?e.lastEffect.nextEffect=ks:e.firstEffect=ks,e.lastEffect=ks))}else{if(null!==(t=Ko(ks)))return t.effectTag&=2047,t;null!==e&&(e.firstEffect=e.lastEffect=null,e.effectTag|=2048)}if(null!==(t=ks.sibling))return t;ks=e}while(null!==ks);return Cs===ws&&(Cs=5),null}function fl(e){var t=e.expirationTime;return t>(e=e.childExpirationTime)?t:e}function pl(e){var t=Ui();return zi(99,dl.bind(null,e,t)),null}function dl(e,t){do{ml()}while(null!==Us);if(0!=(48&Es))throw Error(o(327));var r=e.finishedWork,n=e.finishedExpirationTime;if(null===r)return null;if(e.finishedWork=null,e.finishedExpirationTime=0,r===e.current)throw Error(o(177));e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90,e.nextKnownPendingLevel=0;var i=fl(r);if(e.firstPendingTime=i,n<=e.lastSuspendedTime?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:n<=e.firstSuspendedTime&&(e.firstSuspendedTime=n-1),n<=e.lastPingedTime&&(e.lastPingedTime=0),n<=e.lastExpiredTime&&(e.lastExpiredTime=0),e===Ss&&(ks=Ss=null,Ts=0),1<r.effectTag?null!==r.lastEffect?(r.lastEffect.nextEffect=r,i=r.firstEffect):i=r:i=r.firstEffect,null!==i){var a=Es;Es|=32,vs.current=null,hr=Gt;var s=pr();if(dr(s)){if("selectionStart"in s)var l={start:s.selectionStart,end:s.selectionEnd};else e:{var u=(l=(l=s.ownerDocument)&&l.defaultView||window).getSelection&&l.getSelection();if(u&&0!==u.rangeCount){l=u.anchorNode;var c=u.anchorOffset,f=u.focusNode;u=u.focusOffset;try{l.nodeType,f.nodeType}catch(e){l=null;break e}var p=0,d=-1,h=-1,m=0,g=0,y=s,b=null;t:for(;;){for(var v;y!==l||0!==c&&3!==y.nodeType||(d=p+c),y!==f||0!==u&&3!==y.nodeType||(h=p+u),3===y.nodeType&&(p+=y.nodeValue.length),null!==(v=y.firstChild);)b=y,y=v;for(;;){if(y===s)break t;if(b===l&&++m===c&&(d=p),b===f&&++g===u&&(h=p),null!==(v=y.nextSibling))break;b=(y=b).parentNode}y=v}l=-1===d||-1===h?null:{start:d,end:h}}else l=null}l=l||{start:0,end:0}}else l=null;mr={activeElementDetached:null,focusedElem:s,selectionRange:l},Gt=!1,Ms=i;do{try{hl()}catch(e){if(null===Ms)throw Error(o(330));bl(Ms,e),Ms=Ms.nextEffect}}while(null!==Ms);Ms=i;do{try{for(s=e,l=t;null!==Ms;){var w=Ms.effectTag;if(16&w&&Ue(Ms.stateNode,""),128&w){var _=Ms.alternate;if(null!==_){var x=_.ref;null!==x&&("function"==typeof x?x(null):x.current=null)}}switch(1038&w){case 2:us(Ms),Ms.effectTag&=-3;break;case 6:us(Ms),Ms.effectTag&=-3,fs(Ms.alternate,Ms);break;case 1024:Ms.effectTag&=-1025;break;case 1028:Ms.effectTag&=-1025,fs(Ms.alternate,Ms);break;case 4:fs(Ms.alternate,Ms);break;case 8:cs(s,c=Ms,l),ss(c)}Ms=Ms.nextEffect}}catch(e){if(null===Ms)throw Error(o(330));bl(Ms,e),Ms=Ms.nextEffect}}while(null!==Ms);if(x=mr,_=pr(),w=x.focusedElem,l=x.selectionRange,_!==w&&w&&w.ownerDocument&&function e(t,r){return!(!t||!r)&&(t===r||(!t||3!==t.nodeType)&&(r&&3===r.nodeType?e(t,r.parentNode):"contains"in t?t.contains(r):!!t.compareDocumentPosition&&!!(16&t.compareDocumentPosition(r))))}(w.ownerDocument.documentElement,w)){null!==l&&dr(w)&&(_=l.start,void 0===(x=l.end)&&(x=_),"selectionStart"in w?(w.selectionStart=_,w.selectionEnd=Math.min(x,w.value.length)):(x=(_=w.ownerDocument||document)&&_.defaultView||window).getSelection&&(x=x.getSelection(),c=w.textContent.length,s=Math.min(l.start,c),l=void 0===l.end?s:Math.min(l.end,c),!x.extend&&s>l&&(c=l,l=s,s=c),c=fr(w,s),f=fr(w,l),c&&f&&(1!==x.rangeCount||x.anchorNode!==c.node||x.anchorOffset!==c.offset||x.focusNode!==f.node||x.focusOffset!==f.offset)&&((_=_.createRange()).setStart(c.node,c.offset),x.removeAllRanges(),s>l?(x.addRange(_),x.extend(f.node,f.offset)):(_.setEnd(f.node,f.offset),x.addRange(_))))),_=[];for(x=w;x=x.parentNode;)1===x.nodeType&&_.push({element:x,left:x.scrollLeft,top:x.scrollTop});for("function"==typeof w.focus&&w.focus(),w=0;w<_.length;w++)(x=_[w]).element.scrollLeft=x.left,x.element.scrollTop=x.top}Gt=!!hr,mr=hr=null,e.current=r,Ms=i;do{try{for(w=e;null!==Ms;){var E=Ms.effectTag;if(36&E&&as(w,Ms.alternate,Ms),128&E){_=void 0;var S=Ms.ref;if(null!==S){var k=Ms.stateNode;switch(Ms.tag){case 5:_=k;break;default:_=k}"function"==typeof S?S(_):S.current=_}}Ms=Ms.nextEffect}}catch(e){if(null===Ms)throw Error(o(330));bl(Ms,e),Ms=Ms.nextEffect}}while(null!==Ms);Ms=null,qi(),Es=a}else e.current=r;if(Bs)Bs=!1,Us=e,Vs=t;else for(Ms=i;null!==Ms;)t=Ms.nextEffect,Ms.nextEffect=null,Ms=t;if(0===(t=e.firstPendingTime)&&(Hs=null),**********===t?e===js?Fs++:(Fs=0,js=e):Fs=0,"function"==typeof _l&&_l(r.stateNode,n),Ks(e),Os)throw Os=!1,e=Is,Is=null,e;return 0!=(8&Es)||Zi(),null}function hl(){for(;null!==Ms;){var e=Ms.effectTag;0!=(256&e)&&rs(Ms.alternate,Ms),0==(512&e)||Bs||(Bs=!0,Fi(97,(function(){return ml(),null}))),Ms=Ms.nextEffect}}function ml(){if(90!==Vs){var e=97<Vs?97:Vs;return Vs=90,zi(e,gl)}}function gl(){if(null===Us)return!1;var e=Us;if(Us=null,0!=(48&Es))throw Error(o(331));var t=Es;for(Es|=32,e=e.current.firstEffect;null!==e;){try{var r=e;if(0!=(512&r.effectTag))switch(r.tag){case 0:case 11:case 15:case 22:ns(5,r),is(5,r)}}catch(t){if(null===e)throw Error(o(330));bl(e,t)}r=e.nextEffect,e.nextEffect=null,e=r}return Es=t,Zi(),!0}function yl(e,t,r){la(e,t=hs(e,t=Xo(r,t),**********)),null!==(e=Qs(e,**********))&&Ks(e)}function bl(e,t){if(3===e.tag)yl(e,e,t);else for(var r=e.return;null!==r;){if(3===r.tag){yl(r,e,t);break}if(1===r.tag){var n=r.stateNode;if("function"==typeof r.type.getDerivedStateFromError||"function"==typeof n.componentDidCatch&&(null===Hs||!Hs.has(n))){la(r,e=ms(r,e=Xo(t,e),**********)),null!==(r=Qs(r,**********))&&Ks(r);break}}r=r.return}}function vl(e,t,r){var n=e.pingCache;null!==n&&n.delete(t),Ss===e&&Ts===r?Cs===xs||Cs===_s&&**********===Ls&&Bi()-qs<500?rl(e,Ts):Ds=!0:Rl(e,r)&&(0!==(t=e.lastPingedTime)&&t<r||(e.lastPingedTime=r,Ks(e)))}function wl(e,t){var r=e.stateNode;null!==r&&r.delete(t),0===(t=0)&&(t=Ws(t=Gs(),e,null)),null!==(e=Qs(e,t))&&Ks(e)}gs=function(e,t,r){var n=t.expirationTime;if(null!==e){var i=t.pendingProps;if(e.memoizedProps!==i||pi.current)No=!0;else{if(n<r){switch(No=!1,t.tag){case 3:Bo(t),Ao();break;case 5:if(Da(t),4&t.mode&&1!==r&&i.hidden)return t.expirationTime=t.childExpirationTime=1,null;break;case 1:mi(t.type)&&vi(t);break;case 4:Pa(t,t.stateNode.containerInfo);break;case 10:n=t.memoizedProps.value,i=t.type._context,ui(Qi,i._currentValue),i._currentValue=n;break;case 13:if(null!==t.memoizedState)return 0!==(n=t.child.childExpirationTime)&&n>=r?jo(e,t,r):(ui(Ma,1&Ma.current),null!==(t=Yo(e,t,r))?t.sibling:null);ui(Ma,1&Ma.current);break;case 19:if(n=t.childExpirationTime>=r,0!=(64&e.effectTag)){if(n)return Wo(e,t,r);t.effectTag|=64}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null),ui(Ma,Ma.current),!n)return null}return Yo(e,t,r)}No=!1}}else No=!1;switch(t.expirationTime=0,t.tag){case 2:if(n=t.type,null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),e=t.pendingProps,i=hi(t,fi.current),ra(t,r),i=Wa(null,t,n,e,i,r),t.effectTag|=1,"object"==typeof i&&null!==i&&"function"==typeof i.render&&void 0===i.$$typeof){if(t.tag=1,t.memoizedState=null,t.updateQueue=null,mi(n)){var a=!0;vi(t)}else a=!1;t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,aa(t);var s=n.getDerivedStateFromProps;"function"==typeof s&&ha(t,n,s,e),i.updater=ma,t.stateNode=i,i._reactInternalFiber=t,va(t,n,e,r),t=Ho(null,t,n,!0,a,r)}else t.tag=0,Po(null,t,i,r),t=t.child;return t;case 16:e:{if(i=t.elementType,null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),e=t.pendingProps,function(e){if(-1===e._status){e._status=0;var t=e._ctor;t=t(),e._result=t,t.then((function(t){0===e._status&&(t=t.default,e._status=1,e._result=t)}),(function(t){0===e._status&&(e._status=2,e._result=t)}))}}(i),1!==i._status)throw i._result;switch(i=i._result,t.type=i,a=t.tag=function(e){if("function"==typeof e)return kl(e)?1:0;if(null!=e){if((e=e.$$typeof)===le)return 11;if(e===fe)return 14}return 2}(i),e=Yi(i,e),a){case 0:t=Oo(null,t,i,e,r);break e;case 1:t=Io(null,t,i,e,r);break e;case 11:t=Ro(null,t,i,e,r);break e;case 14:t=Do(null,t,i,Yi(i.type,e),n,r);break e}throw Error(o(306,i,""))}return t;case 0:return n=t.type,i=t.pendingProps,Oo(e,t,n,i=t.elementType===n?i:Yi(n,i),r);case 1:return n=t.type,i=t.pendingProps,Io(e,t,n,i=t.elementType===n?i:Yi(n,i),r);case 3:if(Bo(t),n=t.updateQueue,null===e||null===n)throw Error(o(282));if(n=t.pendingProps,i=null!==(i=t.memoizedState)?i.element:null,oa(e,t),ca(t,n,null,r),(n=t.memoizedState.element)===i)Ao(),t=Yo(e,t,r);else{if((i=t.stateNode.hydrate)&&(_o=wr(t.stateNode.containerInfo.firstChild),wo=t,i=xo=!0),i)for(r=ka(t,null,n,r),t.child=r;r;)r.effectTag=-3&r.effectTag|1024,r=r.sibling;else Po(e,t,n,r),Ao();t=t.child}return t;case 5:return Da(t),null===e&&ko(t),n=t.type,i=t.pendingProps,a=null!==e?e.memoizedProps:null,s=i.children,yr(n,i)?s=null:null!==a&&yr(n,a)&&(t.effectTag|=16),Mo(e,t),4&t.mode&&1!==r&&i.hidden?(t.expirationTime=t.childExpirationTime=1,t=null):(Po(e,t,s,r),t=t.child),t;case 6:return null===e&&ko(t),null;case 13:return jo(e,t,r);case 4:return Pa(t,t.stateNode.containerInfo),n=t.pendingProps,null===e?t.child=Sa(t,null,n,r):Po(e,t,n,r),t.child;case 11:return n=t.type,i=t.pendingProps,Ro(e,t,n,i=t.elementType===n?i:Yi(n,i),r);case 7:return Po(e,t,t.pendingProps,r),t.child;case 8:case 12:return Po(e,t,t.pendingProps.children,r),t.child;case 10:e:{n=t.type._context,i=t.pendingProps,s=t.memoizedProps,a=i.value;var l=t.type._context;if(ui(Qi,l._currentValue),l._currentValue=a,null!==s)if(l=s.value,0===(a=In(l,a)?0:0|("function"==typeof n._calculateChangedBits?n._calculateChangedBits(l,a):**********))){if(s.children===i.children&&!pi.current){t=Yo(e,t,r);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var u=l.dependencies;if(null!==u){s=l.child;for(var c=u.firstContext;null!==c;){if(c.context===n&&0!=(c.observedBits&a)){1===l.tag&&((c=sa(r,null)).tag=2,la(l,c)),l.expirationTime<r&&(l.expirationTime=r),null!==(c=l.alternate)&&c.expirationTime<r&&(c.expirationTime=r),ta(l.return,r),u.expirationTime<r&&(u.expirationTime=r);break}c=c.next}}else s=10===l.tag&&l.type===t.type?null:l.child;if(null!==s)s.return=l;else for(s=l;null!==s;){if(s===t){s=null;break}if(null!==(l=s.sibling)){l.return=s.return,s=l;break}s=s.return}l=s}Po(e,t,i.children,r),t=t.child}return t;case 9:return i=t.type,n=(a=t.pendingProps).children,ra(t,r),n=n(i=na(i,a.unstable_observedBits)),t.effectTag|=1,Po(e,t,n,r),t.child;case 14:return a=Yi(i=t.type,t.pendingProps),Do(e,t,i,a=Yi(i.type,a),n,r);case 15:return qo(e,t,t.type,t.pendingProps,n,r);case 17:return n=t.type,i=t.pendingProps,i=t.elementType===n?i:Yi(n,i),null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),t.tag=1,mi(n)?(e=!0,vi(t)):e=!1,ra(t,r),ya(t,n,i),va(t,n,i,r),Ho(null,t,n,!0,e,r);case 19:return Wo(e,t,r)}throw Error(o(156,t.tag))};var _l=null,xl=null;function El(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.effectTag=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childExpirationTime=this.expirationTime=0,this.alternate=null}function Sl(e,t,r,n){return new El(e,t,r,n)}function kl(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Tl(e,t){var r=e.alternate;return null===r?((r=Sl(e.tag,t,e.key,e.mode)).elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.effectTag=0,r.nextEffect=null,r.firstEffect=null,r.lastEffect=null),r.childExpirationTime=e.childExpirationTime,r.expirationTime=e.expirationTime,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=null===t?null:{expirationTime:t.expirationTime,firstContext:t.firstContext,responders:t.responders},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function Cl(e,t,r,n,i,a){var s=2;if(n=e,"function"==typeof e)kl(e)&&(s=1);else if("string"==typeof e)s=5;else e:switch(e){case re:return Al(r.children,i,a,t);case se:s=8,i|=7;break;case ne:s=8,i|=1;break;case ie:return(e=Sl(12,r,t,8|i)).elementType=ie,e.type=ie,e.expirationTime=a,e;case ue:return(e=Sl(13,r,t,i)).type=ue,e.elementType=ue,e.expirationTime=a,e;case ce:return(e=Sl(19,r,t,i)).elementType=ce,e.expirationTime=a,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case ae:s=10;break e;case oe:s=9;break e;case le:s=11;break e;case fe:s=14;break e;case pe:s=16,n=null;break e;case de:s=22;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Sl(s,r,t,i)).elementType=e,t.type=n,t.expirationTime=a,t}function Al(e,t,r,n){return(e=Sl(7,e,n,t)).expirationTime=r,e}function Ll(e,t,r){return(e=Sl(6,e,null,t)).expirationTime=r,e}function Nl(e,t,r){return(t=Sl(4,null!==e.children?e.children:[],e.key,t)).expirationTime=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Pl(e,t,r){this.tag=t,this.current=null,this.containerInfo=e,this.pingCache=this.pendingChildren=null,this.finishedExpirationTime=0,this.finishedWork=null,this.timeoutHandle=-1,this.pendingContext=this.context=null,this.hydrate=r,this.callbackNode=null,this.callbackPriority=90,this.lastExpiredTime=this.lastPingedTime=this.nextKnownPendingLevel=this.lastSuspendedTime=this.firstSuspendedTime=this.firstPendingTime=0}function Rl(e,t){var r=e.firstSuspendedTime;return e=e.lastSuspendedTime,0!==r&&r>=t&&e<=t}function Dl(e,t){var r=e.firstSuspendedTime,n=e.lastSuspendedTime;r<t&&(e.firstSuspendedTime=t),(n>t||0===r)&&(e.lastSuspendedTime=t),t<=e.lastPingedTime&&(e.lastPingedTime=0),t<=e.lastExpiredTime&&(e.lastExpiredTime=0)}function ql(e,t){t>e.firstPendingTime&&(e.firstPendingTime=t);var r=e.firstSuspendedTime;0!==r&&(t>=r?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:t>=e.lastSuspendedTime&&(e.lastSuspendedTime=t+1),t>e.nextKnownPendingLevel&&(e.nextKnownPendingLevel=t))}function Ml(e,t){var r=e.lastExpiredTime;(0===r||r>t)&&(e.lastExpiredTime=t)}function Ol(e,t,r,n){var i=t.current,a=Gs(),s=pa.suspense;a=Ws(a,i,s);e:if(r){t:{if(Je(r=r._reactInternalFiber)!==r||1!==r.tag)throw Error(o(170));var l=r;do{switch(l.tag){case 3:l=l.stateNode.context;break t;case 1:if(mi(l.type)){l=l.stateNode.__reactInternalMemoizedMergedChildContext;break t}}l=l.return}while(null!==l);throw Error(o(171))}if(1===r.tag){var u=r.type;if(mi(u)){r=bi(r,u,l);break e}}r=l}else r=ci;return null===t.context?t.context=r:t.pendingContext=r,(t=sa(a,s)).payload={element:e},null!==(n=void 0===n?null:n)&&(t.callback=n),la(i,t),Ys(i,a),a}function Il(e){if(!(e=e.current).child)return null;switch(e.child.tag){case 5:default:return e.child.stateNode}}function Hl(e,t){null!==(e=e.memoizedState)&&null!==e.dehydrated&&e.retryTime<t&&(e.retryTime=t)}function Bl(e,t){Hl(e,t),(e=e.alternate)&&Hl(e,t)}function Ul(e,t,r){var n=new Pl(e,t,r=null!=r&&!0===r.hydrate),i=Sl(3,null,null,2===t?7:1===t?3:0);n.current=i,i.stateNode=n,aa(i),e[kr]=n.current,r&&0!==t&&function(e,t){var r=Xe(t);Tt.forEach((function(e){ht(e,t,r)})),Ct.forEach((function(e){ht(e,t,r)}))}(0,9===e.nodeType?e:e.ownerDocument),this._internalRoot=n}function Vl(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function zl(e,t,r,n,i){var a=r._reactRootContainer;if(a){var o=a._internalRoot;if("function"==typeof i){var s=i;i=function(){var e=Il(o);s.call(e)}}Ol(t,o,e,i)}else{if(a=r._reactRootContainer=function(e,t){if(t||(t=!(!(t=e?9===e.nodeType?e.documentElement:e.firstChild:null)||1!==t.nodeType||!t.hasAttribute("data-reactroot"))),!t)for(var r;r=e.lastChild;)e.removeChild(r);return new Ul(e,0,t?{hydrate:!0}:void 0)}(r,n),o=a._internalRoot,"function"==typeof i){var l=i;i=function(){var e=Il(o);l.call(e)}}tl((function(){Ol(t,o,e,i)}))}return Il(o)}function Fl(e,t,r){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:te,key:null==n?null:""+n,children:e,containerInfo:t,implementation:r}}function jl(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Vl(t))throw Error(o(200));return Fl(e,t,null,r)}Ul.prototype.render=function(e){Ol(e,this._internalRoot,null,null)},Ul.prototype.unmount=function(){var e=this._internalRoot,t=e.containerInfo;Ol(null,e,null,(function(){t[kr]=null}))},mt=function(e){if(13===e.tag){var t=Wi(Gs(),150,100);Ys(e,t),Bl(e,t)}},gt=function(e){13===e.tag&&(Ys(e,3),Bl(e,3))},yt=function(e){if(13===e.tag){var t=Gs();Ys(e,t=Ws(t,e,null)),Bl(e,t)}},A=function(e,t,r){switch(t){case"input":if(ke(e,r),t=r.name,"radio"===r.type&&null!=t){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var i=Lr(n);if(!i)throw Error(o(90));_e(n),ke(n,i)}}}break;case"textarea":Re(e,r);break;case"select":null!=(t=r.value)&&Le(e,!!r.multiple,t,!1)}},q=el,M=function(e,t,r,n,i){var a=Es;Es|=4;try{return zi(98,e.bind(null,t,r,n,i))}finally{0===(Es=a)&&Zi()}},O=function(){0==(49&Es)&&(function(){if(null!==zs){var e=zs;zs=null,e.forEach((function(e,t){Ml(t,e),Ks(t)})),Zi()}}(),ml())},I=function(e,t){var r=Es;Es|=2;try{return e(t)}finally{0===(Es=r)&&Zi()}};var Zl,Gl,Wl={Events:[Cr,Ar,Lr,T,E,Or,function(e){it(e,Mr)},R,D,Kt,st,ml,{current:!1}]};Gl=(Zl={findFiberByHostInstance:Tr,bundleType:0,version:"16.14.0",rendererPackageName:"react-dom"}).findFiberByHostInstance,function(e){if("undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled||!t.supportsFiber)return!0;try{var r=t.inject(e);_l=function(e){try{t.onCommitFiberRoot(r,e,void 0,64==(64&e.current.effectTag))}catch(e){}},xl=function(e){try{t.onCommitFiberUnmount(r,e)}catch(e){}}}catch(e){}}(i({},Zl,{overrideHookState:null,overrideProps:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:$.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=rt(e))?null:e.stateNode},findFiberByHostInstance:function(e){return Gl?Gl(e):null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null})),t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Wl,t.createPortal=jl,t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternalFiber;if(void 0===t){if("function"==typeof e.render)throw Error(o(188));throw Error(o(268,Object.keys(e)))}return e=null===(e=rt(t))?null:e.stateNode},t.flushSync=function(e,t){if(0!=(48&Es))throw Error(o(187));var r=Es;Es|=1;try{return zi(99,e.bind(null,t))}finally{Es=r,Zi()}},t.hydrate=function(e,t,r){if(!Vl(t))throw Error(o(200));return zl(null,e,t,!0,r)},t.render=function(e,t,r){if(!Vl(t))throw Error(o(200));return zl(null,e,t,!1,r)},t.unmountComponentAtNode=function(e){if(!Vl(e))throw Error(o(40));return!!e._reactRootContainer&&(tl((function(){zl(null,null,e,!1,(function(){e._reactRootContainer=null,e[kr]=null}))})),!0)},t.unstable_batchedUpdates=el,t.unstable_createPortal=function(e,t){return jl(e,t,2<arguments.length&&void 0!==arguments[2]?arguments[2]:null)},t.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!Vl(r))throw Error(o(200));if(null==e||void 0===e._reactInternalFiber)throw Error(o(38));return zl(e,t,r,!1,n)},t.version="16.14.0"},function(e,t,r){"use strict";e.exports=r(11)},function(e,t,r){"use strict";
/** @license React v0.19.1
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n,i,a,o,s;if("undefined"==typeof window||"function"!=typeof MessageChannel){var l=null,u=null,c=function(){if(null!==l)try{var e=t.unstable_now();l(!0,e),l=null}catch(e){throw setTimeout(c,0),e}},f=Date.now();t.unstable_now=function(){return Date.now()-f},n=function(e){null!==l?setTimeout(n,0,e):(l=e,setTimeout(c,0))},i=function(e,t){u=setTimeout(e,t)},a=function(){clearTimeout(u)},o=function(){return!1},s=t.unstable_forceFrameRate=function(){}}else{var p=window.performance,d=window.Date,h=window.setTimeout,m=window.clearTimeout;if("undefined"!=typeof console){var g=window.cancelAnimationFrame;"function"!=typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"),"function"!=typeof g&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills")}if("object"==typeof p&&"function"==typeof p.now)t.unstable_now=function(){return p.now()};else{var y=d.now();t.unstable_now=function(){return d.now()-y}}var b=!1,v=null,w=-1,_=5,x=0;o=function(){return t.unstable_now()>=x},s=function(){},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing framerates higher than 125 fps is not unsupported"):_=0<e?Math.floor(1e3/e):5};var E=new MessageChannel,S=E.port2;E.port1.onmessage=function(){if(null!==v){var e=t.unstable_now();x=e+_;try{v(!0,e)?S.postMessage(null):(b=!1,v=null)}catch(e){throw S.postMessage(null),e}}else b=!1},n=function(e){v=e,b||(b=!0,S.postMessage(null))},i=function(e,r){w=h((function(){e(t.unstable_now())}),r)},a=function(){m(w),w=-1}}function k(e,t){var r=e.length;e.push(t);e:for(;;){var n=r-1>>>1,i=e[n];if(!(void 0!==i&&0<A(i,t)))break e;e[n]=t,e[r]=i,r=n}}function T(e){return void 0===(e=e[0])?null:e}function C(e){var t=e[0];if(void 0!==t){var r=e.pop();if(r!==t){e[0]=r;e:for(var n=0,i=e.length;n<i;){var a=2*(n+1)-1,o=e[a],s=a+1,l=e[s];if(void 0!==o&&0>A(o,r))void 0!==l&&0>A(l,o)?(e[n]=l,e[s]=r,n=s):(e[n]=o,e[a]=r,n=a);else{if(!(void 0!==l&&0>A(l,r)))break e;e[n]=l,e[s]=r,n=s}}}return t}return null}function A(e,t){var r=e.sortIndex-t.sortIndex;return 0!==r?r:e.id-t.id}var L=[],N=[],P=1,R=null,D=3,q=!1,M=!1,O=!1;function I(e){for(var t=T(N);null!==t;){if(null===t.callback)C(N);else{if(!(t.startTime<=e))break;C(N),t.sortIndex=t.expirationTime,k(L,t)}t=T(N)}}function H(e){if(O=!1,I(e),!M)if(null!==T(L))M=!0,n(B);else{var t=T(N);null!==t&&i(H,t.startTime-e)}}function B(e,r){M=!1,O&&(O=!1,a()),q=!0;var n=D;try{for(I(r),R=T(L);null!==R&&(!(R.expirationTime>r)||e&&!o());){var s=R.callback;if(null!==s){R.callback=null,D=R.priorityLevel;var l=s(R.expirationTime<=r);r=t.unstable_now(),"function"==typeof l?R.callback=l:R===T(L)&&C(L),I(r)}else C(L);R=T(L)}if(null!==R)var u=!0;else{var c=T(N);null!==c&&i(H,c.startTime-r),u=!1}return u}finally{R=null,D=n,q=!1}}function U(e){switch(e){case 1:return-1;case 2:return 250;case 5:return **********;case 4:return 1e4;default:return 5e3}}var V=s;t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){M||q||(M=!0,n(B))},t.unstable_getCurrentPriorityLevel=function(){return D},t.unstable_getFirstCallbackNode=function(){return T(L)},t.unstable_next=function(e){switch(D){case 1:case 2:case 3:var t=3;break;default:t=D}var r=D;D=t;try{return e()}finally{D=r}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=V,t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var r=D;D=e;try{return t()}finally{D=r}},t.unstable_scheduleCallback=function(e,r,o){var s=t.unstable_now();if("object"==typeof o&&null!==o){var l=o.delay;l="number"==typeof l&&0<l?s+l:s,o="number"==typeof o.timeout?o.timeout:U(e)}else o=U(e),l=s;return e={id:P++,callback:r,priorityLevel:e,startTime:l,expirationTime:o=l+o,sortIndex:-1},l>s?(e.sortIndex=l,k(N,e),null===T(L)&&e===T(N)&&(O?a():O=!0,i(H,l-s))):(e.sortIndex=o,k(L,e),M||q||(M=!0,n(B))),e},t.unstable_shouldYield=function(){var e=t.unstable_now();I(e);var r=T(L);return r!==R&&null!==R&&null!==r&&null!==r.callback&&r.startTime<=e&&r.expirationTime<R.expirationTime||o()},t.unstable_wrapCallback=function(e){var t=D;return function(){var r=D;D=t;try{return e.apply(this,arguments)}finally{D=r}}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return e.filter((function(e){return!(0,n.default)(e)})).map((function(e,r){var n=void 0;return"function"!=typeof t||null!==(n=t(e,r))&&!n?(0,i.default)(e,r,t):n}))};var n=a(r(27)),i=a(r(13));function a(e){return e&&e.__esModule?e:{default:e}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){return a.default[e.type](e,t,r)};var n,i=r(28),a=(n=i)&&n.__esModule?n:{default:n}},function(e,t,r){var n=r(15),i={input:!0,option:!0,optgroup:!0,select:!0,button:!0,datalist:!0,textarea:!0},a={tr:{tr:!0,th:!0,td:!0},th:{th:!0},td:{thead:!0,th:!0,td:!0},body:{head:!0,link:!0,script:!0},li:{li:!0},p:{p:!0},h1:{p:!0},h2:{p:!0},h3:{p:!0},h4:{p:!0},h5:{p:!0},h6:{p:!0},select:i,input:i,output:i,button:i,datalist:i,textarea:i,option:{option:!0},optgroup:{optgroup:!0}},o={__proto__:null,area:!0,base:!0,basefont:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,isindex:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},s={__proto__:null,math:!0,svg:!0},l={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0,foreignObject:!0,desc:!0,title:!0},u=/\s|\//;function c(e,t){this._options=t||{},this._cbs=e||{},this._tagname="",this._attribname="",this._attribvalue="",this._attribs=null,this._stack=[],this._foreignContext=[],this.startIndex=0,this.endIndex=null,this._lowerCaseTagNames="lowerCaseTags"in this._options?!!this._options.lowerCaseTags:!this._options.xmlMode,this._lowerCaseAttributeNames="lowerCaseAttributeNames"in this._options?!!this._options.lowerCaseAttributeNames:!this._options.xmlMode,this._options.Tokenizer&&(n=this._options.Tokenizer),this._tokenizer=new n(this._options,this),this._cbs.onparserinit&&this._cbs.onparserinit(this)}r(6)(c,r(34).EventEmitter),c.prototype._updatePosition=function(e){null===this.endIndex?this._tokenizer._sectionStart<=e?this.startIndex=0:this.startIndex=this._tokenizer._sectionStart-e:this.startIndex=this.endIndex+1,this.endIndex=this._tokenizer.getAbsoluteIndex()},c.prototype.ontext=function(e){this._updatePosition(1),this.endIndex--,this._cbs.ontext&&this._cbs.ontext(e)},c.prototype.onopentagname=function(e){if(this._lowerCaseTagNames&&(e=e.toLowerCase()),this._tagname=e,!this._options.xmlMode&&e in a)for(var t;(t=this._stack[this._stack.length-1])in a[e];this.onclosetag(t));!this._options.xmlMode&&e in o||(this._stack.push(e),e in s?this._foreignContext.push(!0):e in l&&this._foreignContext.push(!1)),this._cbs.onopentagname&&this._cbs.onopentagname(e),this._cbs.onopentag&&(this._attribs={})},c.prototype.onopentagend=function(){this._updatePosition(1),this._attribs&&(this._cbs.onopentag&&this._cbs.onopentag(this._tagname,this._attribs),this._attribs=null),!this._options.xmlMode&&this._cbs.onclosetag&&this._tagname in o&&this._cbs.onclosetag(this._tagname),this._tagname=""},c.prototype.onclosetag=function(e){if(this._updatePosition(1),this._lowerCaseTagNames&&(e=e.toLowerCase()),(e in s||e in l)&&this._foreignContext.pop(),!this._stack.length||e in o&&!this._options.xmlMode)this._options.xmlMode||"br"!==e&&"p"!==e||(this.onopentagname(e),this._closeCurrentTag());else{var t=this._stack.lastIndexOf(e);if(-1!==t)if(this._cbs.onclosetag)for(t=this._stack.length-t;t--;)this._cbs.onclosetag(this._stack.pop());else this._stack.length=t;else"p"!==e||this._options.xmlMode||(this.onopentagname(e),this._closeCurrentTag())}},c.prototype.onselfclosingtag=function(){this._options.xmlMode||this._options.recognizeSelfClosing||this._foreignContext[this._foreignContext.length-1]?this._closeCurrentTag():this.onopentagend()},c.prototype._closeCurrentTag=function(){var e=this._tagname;this.onopentagend(),this._stack[this._stack.length-1]===e&&(this._cbs.onclosetag&&this._cbs.onclosetag(e),this._stack.pop())},c.prototype.onattribname=function(e){this._lowerCaseAttributeNames&&(e=e.toLowerCase()),this._attribname=e},c.prototype.onattribdata=function(e){this._attribvalue+=e},c.prototype.onattribend=function(){this._cbs.onattribute&&this._cbs.onattribute(this._attribname,this._attribvalue),this._attribs&&!Object.prototype.hasOwnProperty.call(this._attribs,this._attribname)&&(this._attribs[this._attribname]=this._attribvalue),this._attribname="",this._attribvalue=""},c.prototype._getInstructionName=function(e){var t=e.search(u),r=t<0?e:e.substr(0,t);return this._lowerCaseTagNames&&(r=r.toLowerCase()),r},c.prototype.ondeclaration=function(e){if(this._cbs.onprocessinginstruction){var t=this._getInstructionName(e);this._cbs.onprocessinginstruction("!"+t,"!"+e)}},c.prototype.onprocessinginstruction=function(e){if(this._cbs.onprocessinginstruction){var t=this._getInstructionName(e);this._cbs.onprocessinginstruction("?"+t,"?"+e)}},c.prototype.oncomment=function(e){this._updatePosition(4),this._cbs.oncomment&&this._cbs.oncomment(e),this._cbs.oncommentend&&this._cbs.oncommentend()},c.prototype.oncdata=function(e){this._updatePosition(1),this._options.xmlMode||this._options.recognizeCDATA?(this._cbs.oncdatastart&&this._cbs.oncdatastart(),this._cbs.ontext&&this._cbs.ontext(e),this._cbs.oncdataend&&this._cbs.oncdataend()):this.oncomment("[CDATA["+e+"]]")},c.prototype.onerror=function(e){this._cbs.onerror&&this._cbs.onerror(e)},c.prototype.onend=function(){if(this._cbs.onclosetag)for(var e=this._stack.length;e>0;this._cbs.onclosetag(this._stack[--e]));this._cbs.onend&&this._cbs.onend()},c.prototype.reset=function(){this._cbs.onreset&&this._cbs.onreset(),this._tokenizer.reset(),this._tagname="",this._attribname="",this._attribs=null,this._stack=[],this._cbs.onparserinit&&this._cbs.onparserinit(this)},c.prototype.parseComplete=function(e){this.reset(),this.end(e)},c.prototype.write=function(e){this._tokenizer.write(e)},c.prototype.end=function(e){this._tokenizer.end(e)},c.prototype.pause=function(){this._tokenizer.pause()},c.prototype.resume=function(){this._tokenizer.resume()},c.prototype.parseChunk=c.prototype.write,c.prototype.done=c.prototype.end,e.exports=c},function(e,t,r){e.exports=ge;var n=r(29),i=r(31),a=r(32),o=r(33),s=0,l=s++,u=s++,c=s++,f=s++,p=s++,d=s++,h=s++,m=s++,g=s++,y=s++,b=s++,v=s++,w=s++,_=s++,x=s++,E=s++,S=s++,k=s++,T=s++,C=s++,A=s++,L=s++,N=s++,P=s++,R=s++,D=s++,q=s++,M=s++,O=s++,I=s++,H=s++,B=s++,U=s++,V=s++,z=s++,F=s++,j=s++,Z=s++,G=s++,W=s++,Y=s++,Q=s++,$=s++,K=s++,X=s++,J=s++,ee=s++,te=s++,re=s++,ne=s++,ie=s++,ae=s++,oe=s++,se=s++,le=s++,ue=0,ce=ue++,fe=ue++,pe=ue++;function de(e){return" "===e||"\n"===e||"\t"===e||"\f"===e||"\r"===e}function he(e,t,r){var n=e.toLowerCase();return e===n?function(e){e===n?this._state=t:(this._state=r,this._index--)}:function(i){i===n||i===e?this._state=t:(this._state=r,this._index--)}}function me(e,t){var r=e.toLowerCase();return function(n){n===r||n===e?this._state=t:(this._state=c,this._index--)}}function ge(e,t){this._state=l,this._buffer="",this._sectionStart=0,this._index=0,this._bufferOffset=0,this._baseState=l,this._special=ce,this._cbs=t,this._running=!0,this._ended=!1,this._xmlMode=!(!e||!e.xmlMode),this._decodeEntities=!(!e||!e.decodeEntities)}ge.prototype._stateText=function(e){"<"===e?(this._index>this._sectionStart&&this._cbs.ontext(this._getSection()),this._state=u,this._sectionStart=this._index):this._decodeEntities&&this._special===ce&&"&"===e&&(this._index>this._sectionStart&&this._cbs.ontext(this._getSection()),this._baseState=l,this._state=ie,this._sectionStart=this._index)},ge.prototype._stateBeforeTagName=function(e){"/"===e?this._state=p:"<"===e?(this._cbs.ontext(this._getSection()),this._sectionStart=this._index):">"===e||this._special!==ce||de(e)?this._state=l:"!"===e?(this._state=x,this._sectionStart=this._index+1):"?"===e?(this._state=S,this._sectionStart=this._index+1):(this._state=this._xmlMode||"s"!==e&&"S"!==e?c:H,this._sectionStart=this._index)},ge.prototype._stateInTagName=function(e){("/"===e||">"===e||de(e))&&(this._emitToken("onopentagname"),this._state=m,this._index--)},ge.prototype._stateBeforeCloseingTagName=function(e){de(e)||(">"===e?this._state=l:this._special!==ce?"s"===e||"S"===e?this._state=B:(this._state=l,this._index--):(this._state=d,this._sectionStart=this._index))},ge.prototype._stateInCloseingTagName=function(e){(">"===e||de(e))&&(this._emitToken("onclosetag"),this._state=h,this._index--)},ge.prototype._stateAfterCloseingTagName=function(e){">"===e&&(this._state=l,this._sectionStart=this._index+1)},ge.prototype._stateBeforeAttributeName=function(e){">"===e?(this._cbs.onopentagend(),this._state=l,this._sectionStart=this._index+1):"/"===e?this._state=f:de(e)||(this._state=g,this._sectionStart=this._index)},ge.prototype._stateInSelfClosingTag=function(e){">"===e?(this._cbs.onselfclosingtag(),this._state=l,this._sectionStart=this._index+1):de(e)||(this._state=m,this._index--)},ge.prototype._stateInAttributeName=function(e){("="===e||"/"===e||">"===e||de(e))&&(this._cbs.onattribname(this._getSection()),this._sectionStart=-1,this._state=y,this._index--)},ge.prototype._stateAfterAttributeName=function(e){"="===e?this._state=b:"/"===e||">"===e?(this._cbs.onattribend(),this._state=m,this._index--):de(e)||(this._cbs.onattribend(),this._state=g,this._sectionStart=this._index)},ge.prototype._stateBeforeAttributeValue=function(e){'"'===e?(this._state=v,this._sectionStart=this._index+1):"'"===e?(this._state=w,this._sectionStart=this._index+1):de(e)||(this._state=_,this._sectionStart=this._index,this._index--)},ge.prototype._stateInAttributeValueDoubleQuotes=function(e){'"'===e?(this._emitToken("onattribdata"),this._cbs.onattribend(),this._state=m):this._decodeEntities&&"&"===e&&(this._emitToken("onattribdata"),this._baseState=this._state,this._state=ie,this._sectionStart=this._index)},ge.prototype._stateInAttributeValueSingleQuotes=function(e){"'"===e?(this._emitToken("onattribdata"),this._cbs.onattribend(),this._state=m):this._decodeEntities&&"&"===e&&(this._emitToken("onattribdata"),this._baseState=this._state,this._state=ie,this._sectionStart=this._index)},ge.prototype._stateInAttributeValueNoQuotes=function(e){de(e)||">"===e?(this._emitToken("onattribdata"),this._cbs.onattribend(),this._state=m,this._index--):this._decodeEntities&&"&"===e&&(this._emitToken("onattribdata"),this._baseState=this._state,this._state=ie,this._sectionStart=this._index)},ge.prototype._stateBeforeDeclaration=function(e){this._state="["===e?L:"-"===e?k:E},ge.prototype._stateInDeclaration=function(e){">"===e&&(this._cbs.ondeclaration(this._getSection()),this._state=l,this._sectionStart=this._index+1)},ge.prototype._stateInProcessingInstruction=function(e){">"===e&&(this._cbs.onprocessinginstruction(this._getSection()),this._state=l,this._sectionStart=this._index+1)},ge.prototype._stateBeforeComment=function(e){"-"===e?(this._state=T,this._sectionStart=this._index+1):this._state=E},ge.prototype._stateInComment=function(e){"-"===e&&(this._state=C)},ge.prototype._stateAfterComment1=function(e){this._state="-"===e?A:T},ge.prototype._stateAfterComment2=function(e){">"===e?(this._cbs.oncomment(this._buffer.substring(this._sectionStart,this._index-2)),this._state=l,this._sectionStart=this._index+1):"-"!==e&&(this._state=T)},ge.prototype._stateBeforeCdata1=he("C",N,E),ge.prototype._stateBeforeCdata2=he("D",P,E),ge.prototype._stateBeforeCdata3=he("A",R,E),ge.prototype._stateBeforeCdata4=he("T",D,E),ge.prototype._stateBeforeCdata5=he("A",q,E),ge.prototype._stateBeforeCdata6=function(e){"["===e?(this._state=M,this._sectionStart=this._index+1):(this._state=E,this._index--)},ge.prototype._stateInCdata=function(e){"]"===e&&(this._state=O)},ge.prototype._stateAfterCdata1=function(e){this._state="]"===e?I:M},ge.prototype._stateAfterCdata2=function(e){">"===e?(this._cbs.oncdata(this._buffer.substring(this._sectionStart,this._index-2)),this._state=l,this._sectionStart=this._index+1):"]"!==e&&(this._state=M)},ge.prototype._stateBeforeSpecial=function(e){"c"===e||"C"===e?this._state=U:"t"===e||"T"===e?this._state=$:(this._state=c,this._index--)},ge.prototype._stateBeforeSpecialEnd=function(e){this._special!==fe||"c"!==e&&"C"!==e?this._special!==pe||"t"!==e&&"T"!==e?this._state=l:this._state=ee:this._state=Z},ge.prototype._stateBeforeScript1=me("R",V),ge.prototype._stateBeforeScript2=me("I",z),ge.prototype._stateBeforeScript3=me("P",F),ge.prototype._stateBeforeScript4=me("T",j),ge.prototype._stateBeforeScript5=function(e){("/"===e||">"===e||de(e))&&(this._special=fe),this._state=c,this._index--},ge.prototype._stateAfterScript1=he("R",G,l),ge.prototype._stateAfterScript2=he("I",W,l),ge.prototype._stateAfterScript3=he("P",Y,l),ge.prototype._stateAfterScript4=he("T",Q,l),ge.prototype._stateAfterScript5=function(e){">"===e||de(e)?(this._special=ce,this._state=d,this._sectionStart=this._index-6,this._index--):this._state=l},ge.prototype._stateBeforeStyle1=me("Y",K),ge.prototype._stateBeforeStyle2=me("L",X),ge.prototype._stateBeforeStyle3=me("E",J),ge.prototype._stateBeforeStyle4=function(e){("/"===e||">"===e||de(e))&&(this._special=pe),this._state=c,this._index--},ge.prototype._stateAfterStyle1=he("Y",te,l),ge.prototype._stateAfterStyle2=he("L",re,l),ge.prototype._stateAfterStyle3=he("E",ne,l),ge.prototype._stateAfterStyle4=function(e){">"===e||de(e)?(this._special=ce,this._state=d,this._sectionStart=this._index-5,this._index--):this._state=l},ge.prototype._stateBeforeEntity=he("#",ae,oe),ge.prototype._stateBeforeNumericEntity=he("X",le,se),ge.prototype._parseNamedEntityStrict=function(){if(this._sectionStart+1<this._index){var e=this._buffer.substring(this._sectionStart+1,this._index),t=this._xmlMode?o:i;t.hasOwnProperty(e)&&(this._emitPartial(t[e]),this._sectionStart=this._index+1)}},ge.prototype._parseLegacyEntity=function(){var e=this._sectionStart+1,t=this._index-e;for(t>6&&(t=6);t>=2;){var r=this._buffer.substr(e,t);if(a.hasOwnProperty(r))return this._emitPartial(a[r]),void(this._sectionStart+=t+1);t--}},ge.prototype._stateInNamedEntity=function(e){";"===e?(this._parseNamedEntityStrict(),this._sectionStart+1<this._index&&!this._xmlMode&&this._parseLegacyEntity(),this._state=this._baseState):(e<"a"||e>"z")&&(e<"A"||e>"Z")&&(e<"0"||e>"9")&&(this._xmlMode||this._sectionStart+1===this._index||(this._baseState!==l?"="!==e&&this._parseNamedEntityStrict():this._parseLegacyEntity()),this._state=this._baseState,this._index--)},ge.prototype._decodeNumericEntity=function(e,t){var r=this._sectionStart+e;if(r!==this._index){var i=this._buffer.substring(r,this._index),a=parseInt(i,t);this._emitPartial(n(a)),this._sectionStart=this._index}else this._sectionStart--;this._state=this._baseState},ge.prototype._stateInNumericEntity=function(e){";"===e?(this._decodeNumericEntity(2,10),this._sectionStart++):(e<"0"||e>"9")&&(this._xmlMode?this._state=this._baseState:this._decodeNumericEntity(2,10),this._index--)},ge.prototype._stateInHexEntity=function(e){";"===e?(this._decodeNumericEntity(3,16),this._sectionStart++):(e<"a"||e>"f")&&(e<"A"||e>"F")&&(e<"0"||e>"9")&&(this._xmlMode?this._state=this._baseState:this._decodeNumericEntity(3,16),this._index--)},ge.prototype._cleanup=function(){this._sectionStart<0?(this._buffer="",this._bufferOffset+=this._index,this._index=0):this._running&&(this._state===l?(this._sectionStart!==this._index&&this._cbs.ontext(this._buffer.substr(this._sectionStart)),this._buffer="",this._bufferOffset+=this._index,this._index=0):this._sectionStart===this._index?(this._buffer="",this._bufferOffset+=this._index,this._index=0):(this._buffer=this._buffer.substr(this._sectionStart),this._index-=this._sectionStart,this._bufferOffset+=this._sectionStart),this._sectionStart=0)},ge.prototype.write=function(e){this._ended&&this._cbs.onerror(Error(".write() after done!")),this._buffer+=e,this._parse()},ge.prototype._parse=function(){for(;this._index<this._buffer.length&&this._running;){var e=this._buffer.charAt(this._index);this._state===l?this._stateText(e):this._state===u?this._stateBeforeTagName(e):this._state===c?this._stateInTagName(e):this._state===p?this._stateBeforeCloseingTagName(e):this._state===d?this._stateInCloseingTagName(e):this._state===h?this._stateAfterCloseingTagName(e):this._state===f?this._stateInSelfClosingTag(e):this._state===m?this._stateBeforeAttributeName(e):this._state===g?this._stateInAttributeName(e):this._state===y?this._stateAfterAttributeName(e):this._state===b?this._stateBeforeAttributeValue(e):this._state===v?this._stateInAttributeValueDoubleQuotes(e):this._state===w?this._stateInAttributeValueSingleQuotes(e):this._state===_?this._stateInAttributeValueNoQuotes(e):this._state===x?this._stateBeforeDeclaration(e):this._state===E?this._stateInDeclaration(e):this._state===S?this._stateInProcessingInstruction(e):this._state===k?this._stateBeforeComment(e):this._state===T?this._stateInComment(e):this._state===C?this._stateAfterComment1(e):this._state===A?this._stateAfterComment2(e):this._state===L?this._stateBeforeCdata1(e):this._state===N?this._stateBeforeCdata2(e):this._state===P?this._stateBeforeCdata3(e):this._state===R?this._stateBeforeCdata4(e):this._state===D?this._stateBeforeCdata5(e):this._state===q?this._stateBeforeCdata6(e):this._state===M?this._stateInCdata(e):this._state===O?this._stateAfterCdata1(e):this._state===I?this._stateAfterCdata2(e):this._state===H?this._stateBeforeSpecial(e):this._state===B?this._stateBeforeSpecialEnd(e):this._state===U?this._stateBeforeScript1(e):this._state===V?this._stateBeforeScript2(e):this._state===z?this._stateBeforeScript3(e):this._state===F?this._stateBeforeScript4(e):this._state===j?this._stateBeforeScript5(e):this._state===Z?this._stateAfterScript1(e):this._state===G?this._stateAfterScript2(e):this._state===W?this._stateAfterScript3(e):this._state===Y?this._stateAfterScript4(e):this._state===Q?this._stateAfterScript5(e):this._state===$?this._stateBeforeStyle1(e):this._state===K?this._stateBeforeStyle2(e):this._state===X?this._stateBeforeStyle3(e):this._state===J?this._stateBeforeStyle4(e):this._state===ee?this._stateAfterStyle1(e):this._state===te?this._stateAfterStyle2(e):this._state===re?this._stateAfterStyle3(e):this._state===ne?this._stateAfterStyle4(e):this._state===ie?this._stateBeforeEntity(e):this._state===ae?this._stateBeforeNumericEntity(e):this._state===oe?this._stateInNamedEntity(e):this._state===se?this._stateInNumericEntity(e):this._state===le?this._stateInHexEntity(e):this._cbs.onerror(Error("unknown _state"),this._state),this._index++}this._cleanup()},ge.prototype.pause=function(){this._running=!1},ge.prototype.resume=function(){this._running=!0,this._index<this._buffer.length&&this._parse(),this._ended&&this._finish()},ge.prototype.end=function(e){this._ended&&this._cbs.onerror(Error(".end() after done!")),e&&this.write(e),this._ended=!0,this._running&&this._finish()},ge.prototype._finish=function(){this._sectionStart<this._index&&this._handleTrailingData(),this._cbs.onend()},ge.prototype._handleTrailingData=function(){var e=this._buffer.substr(this._sectionStart);this._state===M||this._state===O||this._state===I?this._cbs.oncdata(e):this._state===T||this._state===C||this._state===A?this._cbs.oncomment(e):this._state!==oe||this._xmlMode?this._state!==se||this._xmlMode?this._state!==le||this._xmlMode?this._state!==c&&this._state!==m&&this._state!==b&&this._state!==y&&this._state!==g&&this._state!==w&&this._state!==v&&this._state!==_&&this._state!==d&&this._cbs.ontext(e):(this._decodeNumericEntity(3,16),this._sectionStart<this._index&&(this._state=this._baseState,this._handleTrailingData())):(this._decodeNumericEntity(2,10),this._sectionStart<this._index&&(this._state=this._baseState,this._handleTrailingData())):(this._parseLegacyEntity(),this._sectionStart<this._index&&(this._state=this._baseState,this._handleTrailingData()))},ge.prototype.reset=function(){ge.call(this,{xmlMode:this._xmlMode,decodeEntities:this._decodeEntities},this._cbs)},ge.prototype.getAbsoluteIndex=function(){return this._bufferOffset+this._index},ge.prototype._getSection=function(){return this._buffer.substring(this._sectionStart,this._index)},ge.prototype._emitToken=function(e){this._cbs[e](this._getSection()),this._sectionStart=-1},ge.prototype._emitPartial=function(e){this._baseState!==l?this._cbs.onattribdata(e):this._cbs.ontext(e)}},function(e,t,r){var n=r(5),i=/\s+/g,a=r(17),o=r(35);function s(e,t,r){"object"==typeof e?(r=t,t=e,e=null):"function"==typeof t&&(r=t,t=l),this._callback=e,this._options=t||l,this._elementCB=r,this.dom=[],this._done=!1,this._tagStack=[],this._parser=this._parser||null}var l={normalizeWhitespace:!1,withStartIndices:!1,withEndIndices:!1};s.prototype.onparserinit=function(e){this._parser=e},s.prototype.onreset=function(){s.call(this,this._callback,this._options,this._elementCB)},s.prototype.onend=function(){this._done||(this._done=!0,this._parser=null,this._handleCallback(null))},s.prototype._handleCallback=s.prototype.onerror=function(e){if("function"==typeof this._callback)this._callback(e,this.dom);else if(e)throw e},s.prototype.onclosetag=function(){var e=this._tagStack.pop();this._options.withEndIndices&&e&&(e.endIndex=this._parser.endIndex),this._elementCB&&this._elementCB(e)},s.prototype._createDomElement=function(e){if(!this._options.withDomLvl1)return e;var t;for(var r in t="tag"===e.type?Object.create(o):Object.create(a),e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t},s.prototype._addDomElement=function(e){var t=this._tagStack[this._tagStack.length-1],r=t?t.children:this.dom,n=r[r.length-1];e.next=null,this._options.withStartIndices&&(e.startIndex=this._parser.startIndex),this._options.withEndIndices&&(e.endIndex=this._parser.endIndex),n?(e.prev=n,n.next=e):e.prev=null,r.push(e),e.parent=t||null},s.prototype.onopentag=function(e,t){var r={type:"script"===e?n.Script:"style"===e?n.Style:n.Tag,name:e,attribs:t,children:[]},i=this._createDomElement(r);this._addDomElement(i),this._tagStack.push(i)},s.prototype.ontext=function(e){var t,r=this._options.normalizeWhitespace||this._options.ignoreWhitespace;if(!this._tagStack.length&&this.dom.length&&(t=this.dom[this.dom.length-1]).type===n.Text)r?t.data=(t.data+e).replace(i," "):t.data+=e;else if(this._tagStack.length&&(t=this._tagStack[this._tagStack.length-1])&&(t=t.children[t.children.length-1])&&t.type===n.Text)r?t.data=(t.data+e).replace(i," "):t.data+=e;else{r&&(e=e.replace(i," "));var a=this._createDomElement({data:e,type:n.Text});this._addDomElement(a)}},s.prototype.oncomment=function(e){var t=this._tagStack[this._tagStack.length-1];if(t&&t.type===n.Comment)t.data+=e;else{var r={data:e,type:n.Comment},i=this._createDomElement(r);this._addDomElement(i),this._tagStack.push(i)}},s.prototype.oncdatastart=function(){var e={children:[{data:"",type:n.Text}],type:n.CDATA},t=this._createDomElement(e);this._addDomElement(t),this._tagStack.push(t)},s.prototype.oncommentend=s.prototype.oncdataend=function(){this._tagStack.pop()},s.prototype.onprocessinginstruction=function(e,t){var r=this._createDomElement({name:e,data:t,type:n.Directive});this._addDomElement(r)},e.exports=s},function(e,t){var r=e.exports={get firstChild(){var e=this.children;return e&&e[0]||null},get lastChild(){var e=this.children;return e&&e[e.length-1]||null},get nodeType(){return i[this.type]||i.element}},n={tagName:"name",childNodes:"children",parentNode:"parent",previousSibling:"prev",nextSibling:"next",nodeValue:"data"},i={element:1,text:3,cdata:4,comment:8};Object.keys(n).forEach((function(e){var t=n[e];Object.defineProperty(r,e,{get:function(){return this[t]||null},set:function(e){return this[t]=e,e}})}))},function(e,t,r){var n=e.exports;[r(37),r(45),r(46),r(47),r(48),r(49)].forEach((function(e){Object.keys(e).forEach((function(t){n[t]=e[t].bind(n)}))}))},function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.decodeHTML=t.decodeHTMLStrict=t.decodeXML=void 0;var i=n(r(20)),a=n(r(41)),o=n(r(21)),s=n(r(42)),l=/&(?:[a-zA-Z0-9]+|#[xX][\da-fA-F]+|#\d+);/g;function u(e){var t=f(e);return function(e){return String(e).replace(l,t)}}t.decodeXML=u(o.default),t.decodeHTMLStrict=u(i.default);var c=function(e,t){return e<t?1:-1};function f(e){return function(t){if("#"===t.charAt(1)){var r=t.charAt(2);return"X"===r||"x"===r?s.default(parseInt(t.substr(3),16)):s.default(parseInt(t.substr(2),10))}return e[t.slice(1,-1)]||t}}t.decodeHTML=function(){for(var e=Object.keys(a.default).sort(c),t=Object.keys(i.default).sort(c),r=0,n=0;r<t.length;r++)e[n]===t[r]?(t[r]+=";?",n++):t[r]+=";";var o=new RegExp("&(?:"+t.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g"),s=f(i.default);function l(e){return";"!==e.substr(-1)&&(e+=";"),s(e)}return function(e){return String(e).replace(o,l)}}()},function(e){e.exports=JSON.parse('{"Aacute":"Á","aacute":"á","Abreve":"Ă","abreve":"ă","ac":"∾","acd":"∿","acE":"∾̳","Acirc":"Â","acirc":"â","acute":"´","Acy":"А","acy":"а","AElig":"Æ","aelig":"æ","af":"⁡","Afr":"𝔄","afr":"𝔞","Agrave":"À","agrave":"à","alefsym":"ℵ","aleph":"ℵ","Alpha":"Α","alpha":"α","Amacr":"Ā","amacr":"ā","amalg":"⨿","amp":"&","AMP":"&","andand":"⩕","And":"⩓","and":"∧","andd":"⩜","andslope":"⩘","andv":"⩚","ang":"∠","ange":"⦤","angle":"∠","angmsdaa":"⦨","angmsdab":"⦩","angmsdac":"⦪","angmsdad":"⦫","angmsdae":"⦬","angmsdaf":"⦭","angmsdag":"⦮","angmsdah":"⦯","angmsd":"∡","angrt":"∟","angrtvb":"⊾","angrtvbd":"⦝","angsph":"∢","angst":"Å","angzarr":"⍼","Aogon":"Ą","aogon":"ą","Aopf":"𝔸","aopf":"𝕒","apacir":"⩯","ap":"≈","apE":"⩰","ape":"≊","apid":"≋","apos":"\'","ApplyFunction":"⁡","approx":"≈","approxeq":"≊","Aring":"Å","aring":"å","Ascr":"𝒜","ascr":"𝒶","Assign":"≔","ast":"*","asymp":"≈","asympeq":"≍","Atilde":"Ã","atilde":"ã","Auml":"Ä","auml":"ä","awconint":"∳","awint":"⨑","backcong":"≌","backepsilon":"϶","backprime":"‵","backsim":"∽","backsimeq":"⋍","Backslash":"∖","Barv":"⫧","barvee":"⊽","barwed":"⌅","Barwed":"⌆","barwedge":"⌅","bbrk":"⎵","bbrktbrk":"⎶","bcong":"≌","Bcy":"Б","bcy":"б","bdquo":"„","becaus":"∵","because":"∵","Because":"∵","bemptyv":"⦰","bepsi":"϶","bernou":"ℬ","Bernoullis":"ℬ","Beta":"Β","beta":"β","beth":"ℶ","between":"≬","Bfr":"𝔅","bfr":"𝔟","bigcap":"⋂","bigcirc":"◯","bigcup":"⋃","bigodot":"⨀","bigoplus":"⨁","bigotimes":"⨂","bigsqcup":"⨆","bigstar":"★","bigtriangledown":"▽","bigtriangleup":"△","biguplus":"⨄","bigvee":"⋁","bigwedge":"⋀","bkarow":"⤍","blacklozenge":"⧫","blacksquare":"▪","blacktriangle":"▴","blacktriangledown":"▾","blacktriangleleft":"◂","blacktriangleright":"▸","blank":"␣","blk12":"▒","blk14":"░","blk34":"▓","block":"█","bne":"=⃥","bnequiv":"≡⃥","bNot":"⫭","bnot":"⌐","Bopf":"𝔹","bopf":"𝕓","bot":"⊥","bottom":"⊥","bowtie":"⋈","boxbox":"⧉","boxdl":"┐","boxdL":"╕","boxDl":"╖","boxDL":"╗","boxdr":"┌","boxdR":"╒","boxDr":"╓","boxDR":"╔","boxh":"─","boxH":"═","boxhd":"┬","boxHd":"╤","boxhD":"╥","boxHD":"╦","boxhu":"┴","boxHu":"╧","boxhU":"╨","boxHU":"╩","boxminus":"⊟","boxplus":"⊞","boxtimes":"⊠","boxul":"┘","boxuL":"╛","boxUl":"╜","boxUL":"╝","boxur":"└","boxuR":"╘","boxUr":"╙","boxUR":"╚","boxv":"│","boxV":"║","boxvh":"┼","boxvH":"╪","boxVh":"╫","boxVH":"╬","boxvl":"┤","boxvL":"╡","boxVl":"╢","boxVL":"╣","boxvr":"├","boxvR":"╞","boxVr":"╟","boxVR":"╠","bprime":"‵","breve":"˘","Breve":"˘","brvbar":"¦","bscr":"𝒷","Bscr":"ℬ","bsemi":"⁏","bsim":"∽","bsime":"⋍","bsolb":"⧅","bsol":"\\\\","bsolhsub":"⟈","bull":"•","bullet":"•","bump":"≎","bumpE":"⪮","bumpe":"≏","Bumpeq":"≎","bumpeq":"≏","Cacute":"Ć","cacute":"ć","capand":"⩄","capbrcup":"⩉","capcap":"⩋","cap":"∩","Cap":"⋒","capcup":"⩇","capdot":"⩀","CapitalDifferentialD":"ⅅ","caps":"∩︀","caret":"⁁","caron":"ˇ","Cayleys":"ℭ","ccaps":"⩍","Ccaron":"Č","ccaron":"č","Ccedil":"Ç","ccedil":"ç","Ccirc":"Ĉ","ccirc":"ĉ","Cconint":"∰","ccups":"⩌","ccupssm":"⩐","Cdot":"Ċ","cdot":"ċ","cedil":"¸","Cedilla":"¸","cemptyv":"⦲","cent":"¢","centerdot":"·","CenterDot":"·","cfr":"𝔠","Cfr":"ℭ","CHcy":"Ч","chcy":"ч","check":"✓","checkmark":"✓","Chi":"Χ","chi":"χ","circ":"ˆ","circeq":"≗","circlearrowleft":"↺","circlearrowright":"↻","circledast":"⊛","circledcirc":"⊚","circleddash":"⊝","CircleDot":"⊙","circledR":"®","circledS":"Ⓢ","CircleMinus":"⊖","CirclePlus":"⊕","CircleTimes":"⊗","cir":"○","cirE":"⧃","cire":"≗","cirfnint":"⨐","cirmid":"⫯","cirscir":"⧂","ClockwiseContourIntegral":"∲","CloseCurlyDoubleQuote":"”","CloseCurlyQuote":"’","clubs":"♣","clubsuit":"♣","colon":":","Colon":"∷","Colone":"⩴","colone":"≔","coloneq":"≔","comma":",","commat":"@","comp":"∁","compfn":"∘","complement":"∁","complexes":"ℂ","cong":"≅","congdot":"⩭","Congruent":"≡","conint":"∮","Conint":"∯","ContourIntegral":"∮","copf":"𝕔","Copf":"ℂ","coprod":"∐","Coproduct":"∐","copy":"©","COPY":"©","copysr":"℗","CounterClockwiseContourIntegral":"∳","crarr":"↵","cross":"✗","Cross":"⨯","Cscr":"𝒞","cscr":"𝒸","csub":"⫏","csube":"⫑","csup":"⫐","csupe":"⫒","ctdot":"⋯","cudarrl":"⤸","cudarrr":"⤵","cuepr":"⋞","cuesc":"⋟","cularr":"↶","cularrp":"⤽","cupbrcap":"⩈","cupcap":"⩆","CupCap":"≍","cup":"∪","Cup":"⋓","cupcup":"⩊","cupdot":"⊍","cupor":"⩅","cups":"∪︀","curarr":"↷","curarrm":"⤼","curlyeqprec":"⋞","curlyeqsucc":"⋟","curlyvee":"⋎","curlywedge":"⋏","curren":"¤","curvearrowleft":"↶","curvearrowright":"↷","cuvee":"⋎","cuwed":"⋏","cwconint":"∲","cwint":"∱","cylcty":"⌭","dagger":"†","Dagger":"‡","daleth":"ℸ","darr":"↓","Darr":"↡","dArr":"⇓","dash":"‐","Dashv":"⫤","dashv":"⊣","dbkarow":"⤏","dblac":"˝","Dcaron":"Ď","dcaron":"ď","Dcy":"Д","dcy":"д","ddagger":"‡","ddarr":"⇊","DD":"ⅅ","dd":"ⅆ","DDotrahd":"⤑","ddotseq":"⩷","deg":"°","Del":"∇","Delta":"Δ","delta":"δ","demptyv":"⦱","dfisht":"⥿","Dfr":"𝔇","dfr":"𝔡","dHar":"⥥","dharl":"⇃","dharr":"⇂","DiacriticalAcute":"´","DiacriticalDot":"˙","DiacriticalDoubleAcute":"˝","DiacriticalGrave":"`","DiacriticalTilde":"˜","diam":"⋄","diamond":"⋄","Diamond":"⋄","diamondsuit":"♦","diams":"♦","die":"¨","DifferentialD":"ⅆ","digamma":"ϝ","disin":"⋲","div":"÷","divide":"÷","divideontimes":"⋇","divonx":"⋇","DJcy":"Ђ","djcy":"ђ","dlcorn":"⌞","dlcrop":"⌍","dollar":"$","Dopf":"𝔻","dopf":"𝕕","Dot":"¨","dot":"˙","DotDot":"⃜","doteq":"≐","doteqdot":"≑","DotEqual":"≐","dotminus":"∸","dotplus":"∔","dotsquare":"⊡","doublebarwedge":"⌆","DoubleContourIntegral":"∯","DoubleDot":"¨","DoubleDownArrow":"⇓","DoubleLeftArrow":"⇐","DoubleLeftRightArrow":"⇔","DoubleLeftTee":"⫤","DoubleLongLeftArrow":"⟸","DoubleLongLeftRightArrow":"⟺","DoubleLongRightArrow":"⟹","DoubleRightArrow":"⇒","DoubleRightTee":"⊨","DoubleUpArrow":"⇑","DoubleUpDownArrow":"⇕","DoubleVerticalBar":"∥","DownArrowBar":"⤓","downarrow":"↓","DownArrow":"↓","Downarrow":"⇓","DownArrowUpArrow":"⇵","DownBreve":"̑","downdownarrows":"⇊","downharpoonleft":"⇃","downharpoonright":"⇂","DownLeftRightVector":"⥐","DownLeftTeeVector":"⥞","DownLeftVectorBar":"⥖","DownLeftVector":"↽","DownRightTeeVector":"⥟","DownRightVectorBar":"⥗","DownRightVector":"⇁","DownTeeArrow":"↧","DownTee":"⊤","drbkarow":"⤐","drcorn":"⌟","drcrop":"⌌","Dscr":"𝒟","dscr":"𝒹","DScy":"Ѕ","dscy":"ѕ","dsol":"⧶","Dstrok":"Đ","dstrok":"đ","dtdot":"⋱","dtri":"▿","dtrif":"▾","duarr":"⇵","duhar":"⥯","dwangle":"⦦","DZcy":"Џ","dzcy":"џ","dzigrarr":"⟿","Eacute":"É","eacute":"é","easter":"⩮","Ecaron":"Ě","ecaron":"ě","Ecirc":"Ê","ecirc":"ê","ecir":"≖","ecolon":"≕","Ecy":"Э","ecy":"э","eDDot":"⩷","Edot":"Ė","edot":"ė","eDot":"≑","ee":"ⅇ","efDot":"≒","Efr":"𝔈","efr":"𝔢","eg":"⪚","Egrave":"È","egrave":"è","egs":"⪖","egsdot":"⪘","el":"⪙","Element":"∈","elinters":"⏧","ell":"ℓ","els":"⪕","elsdot":"⪗","Emacr":"Ē","emacr":"ē","empty":"∅","emptyset":"∅","EmptySmallSquare":"◻","emptyv":"∅","EmptyVerySmallSquare":"▫","emsp13":" ","emsp14":" ","emsp":" ","ENG":"Ŋ","eng":"ŋ","ensp":" ","Eogon":"Ę","eogon":"ę","Eopf":"𝔼","eopf":"𝕖","epar":"⋕","eparsl":"⧣","eplus":"⩱","epsi":"ε","Epsilon":"Ε","epsilon":"ε","epsiv":"ϵ","eqcirc":"≖","eqcolon":"≕","eqsim":"≂","eqslantgtr":"⪖","eqslantless":"⪕","Equal":"⩵","equals":"=","EqualTilde":"≂","equest":"≟","Equilibrium":"⇌","equiv":"≡","equivDD":"⩸","eqvparsl":"⧥","erarr":"⥱","erDot":"≓","escr":"ℯ","Escr":"ℰ","esdot":"≐","Esim":"⩳","esim":"≂","Eta":"Η","eta":"η","ETH":"Ð","eth":"ð","Euml":"Ë","euml":"ë","euro":"€","excl":"!","exist":"∃","Exists":"∃","expectation":"ℰ","exponentiale":"ⅇ","ExponentialE":"ⅇ","fallingdotseq":"≒","Fcy":"Ф","fcy":"ф","female":"♀","ffilig":"ﬃ","fflig":"ﬀ","ffllig":"ﬄ","Ffr":"𝔉","ffr":"𝔣","filig":"ﬁ","FilledSmallSquare":"◼","FilledVerySmallSquare":"▪","fjlig":"fj","flat":"♭","fllig":"ﬂ","fltns":"▱","fnof":"ƒ","Fopf":"𝔽","fopf":"𝕗","forall":"∀","ForAll":"∀","fork":"⋔","forkv":"⫙","Fouriertrf":"ℱ","fpartint":"⨍","frac12":"½","frac13":"⅓","frac14":"¼","frac15":"⅕","frac16":"⅙","frac18":"⅛","frac23":"⅔","frac25":"⅖","frac34":"¾","frac35":"⅗","frac38":"⅜","frac45":"⅘","frac56":"⅚","frac58":"⅝","frac78":"⅞","frasl":"⁄","frown":"⌢","fscr":"𝒻","Fscr":"ℱ","gacute":"ǵ","Gamma":"Γ","gamma":"γ","Gammad":"Ϝ","gammad":"ϝ","gap":"⪆","Gbreve":"Ğ","gbreve":"ğ","Gcedil":"Ģ","Gcirc":"Ĝ","gcirc":"ĝ","Gcy":"Г","gcy":"г","Gdot":"Ġ","gdot":"ġ","ge":"≥","gE":"≧","gEl":"⪌","gel":"⋛","geq":"≥","geqq":"≧","geqslant":"⩾","gescc":"⪩","ges":"⩾","gesdot":"⪀","gesdoto":"⪂","gesdotol":"⪄","gesl":"⋛︀","gesles":"⪔","Gfr":"𝔊","gfr":"𝔤","gg":"≫","Gg":"⋙","ggg":"⋙","gimel":"ℷ","GJcy":"Ѓ","gjcy":"ѓ","gla":"⪥","gl":"≷","glE":"⪒","glj":"⪤","gnap":"⪊","gnapprox":"⪊","gne":"⪈","gnE":"≩","gneq":"⪈","gneqq":"≩","gnsim":"⋧","Gopf":"𝔾","gopf":"𝕘","grave":"`","GreaterEqual":"≥","GreaterEqualLess":"⋛","GreaterFullEqual":"≧","GreaterGreater":"⪢","GreaterLess":"≷","GreaterSlantEqual":"⩾","GreaterTilde":"≳","Gscr":"𝒢","gscr":"ℊ","gsim":"≳","gsime":"⪎","gsiml":"⪐","gtcc":"⪧","gtcir":"⩺","gt":">","GT":">","Gt":"≫","gtdot":"⋗","gtlPar":"⦕","gtquest":"⩼","gtrapprox":"⪆","gtrarr":"⥸","gtrdot":"⋗","gtreqless":"⋛","gtreqqless":"⪌","gtrless":"≷","gtrsim":"≳","gvertneqq":"≩︀","gvnE":"≩︀","Hacek":"ˇ","hairsp":" ","half":"½","hamilt":"ℋ","HARDcy":"Ъ","hardcy":"ъ","harrcir":"⥈","harr":"↔","hArr":"⇔","harrw":"↭","Hat":"^","hbar":"ℏ","Hcirc":"Ĥ","hcirc":"ĥ","hearts":"♥","heartsuit":"♥","hellip":"…","hercon":"⊹","hfr":"𝔥","Hfr":"ℌ","HilbertSpace":"ℋ","hksearow":"⤥","hkswarow":"⤦","hoarr":"⇿","homtht":"∻","hookleftarrow":"↩","hookrightarrow":"↪","hopf":"𝕙","Hopf":"ℍ","horbar":"―","HorizontalLine":"─","hscr":"𝒽","Hscr":"ℋ","hslash":"ℏ","Hstrok":"Ħ","hstrok":"ħ","HumpDownHump":"≎","HumpEqual":"≏","hybull":"⁃","hyphen":"‐","Iacute":"Í","iacute":"í","ic":"⁣","Icirc":"Î","icirc":"î","Icy":"И","icy":"и","Idot":"İ","IEcy":"Е","iecy":"е","iexcl":"¡","iff":"⇔","ifr":"𝔦","Ifr":"ℑ","Igrave":"Ì","igrave":"ì","ii":"ⅈ","iiiint":"⨌","iiint":"∭","iinfin":"⧜","iiota":"℩","IJlig":"Ĳ","ijlig":"ĳ","Imacr":"Ī","imacr":"ī","image":"ℑ","ImaginaryI":"ⅈ","imagline":"ℐ","imagpart":"ℑ","imath":"ı","Im":"ℑ","imof":"⊷","imped":"Ƶ","Implies":"⇒","incare":"℅","in":"∈","infin":"∞","infintie":"⧝","inodot":"ı","intcal":"⊺","int":"∫","Int":"∬","integers":"ℤ","Integral":"∫","intercal":"⊺","Intersection":"⋂","intlarhk":"⨗","intprod":"⨼","InvisibleComma":"⁣","InvisibleTimes":"⁢","IOcy":"Ё","iocy":"ё","Iogon":"Į","iogon":"į","Iopf":"𝕀","iopf":"𝕚","Iota":"Ι","iota":"ι","iprod":"⨼","iquest":"¿","iscr":"𝒾","Iscr":"ℐ","isin":"∈","isindot":"⋵","isinE":"⋹","isins":"⋴","isinsv":"⋳","isinv":"∈","it":"⁢","Itilde":"Ĩ","itilde":"ĩ","Iukcy":"І","iukcy":"і","Iuml":"Ï","iuml":"ï","Jcirc":"Ĵ","jcirc":"ĵ","Jcy":"Й","jcy":"й","Jfr":"𝔍","jfr":"𝔧","jmath":"ȷ","Jopf":"𝕁","jopf":"𝕛","Jscr":"𝒥","jscr":"𝒿","Jsercy":"Ј","jsercy":"ј","Jukcy":"Є","jukcy":"є","Kappa":"Κ","kappa":"κ","kappav":"ϰ","Kcedil":"Ķ","kcedil":"ķ","Kcy":"К","kcy":"к","Kfr":"𝔎","kfr":"𝔨","kgreen":"ĸ","KHcy":"Х","khcy":"х","KJcy":"Ќ","kjcy":"ќ","Kopf":"𝕂","kopf":"𝕜","Kscr":"𝒦","kscr":"𝓀","lAarr":"⇚","Lacute":"Ĺ","lacute":"ĺ","laemptyv":"⦴","lagran":"ℒ","Lambda":"Λ","lambda":"λ","lang":"⟨","Lang":"⟪","langd":"⦑","langle":"⟨","lap":"⪅","Laplacetrf":"ℒ","laquo":"«","larrb":"⇤","larrbfs":"⤟","larr":"←","Larr":"↞","lArr":"⇐","larrfs":"⤝","larrhk":"↩","larrlp":"↫","larrpl":"⤹","larrsim":"⥳","larrtl":"↢","latail":"⤙","lAtail":"⤛","lat":"⪫","late":"⪭","lates":"⪭︀","lbarr":"⤌","lBarr":"⤎","lbbrk":"❲","lbrace":"{","lbrack":"[","lbrke":"⦋","lbrksld":"⦏","lbrkslu":"⦍","Lcaron":"Ľ","lcaron":"ľ","Lcedil":"Ļ","lcedil":"ļ","lceil":"⌈","lcub":"{","Lcy":"Л","lcy":"л","ldca":"⤶","ldquo":"“","ldquor":"„","ldrdhar":"⥧","ldrushar":"⥋","ldsh":"↲","le":"≤","lE":"≦","LeftAngleBracket":"⟨","LeftArrowBar":"⇤","leftarrow":"←","LeftArrow":"←","Leftarrow":"⇐","LeftArrowRightArrow":"⇆","leftarrowtail":"↢","LeftCeiling":"⌈","LeftDoubleBracket":"⟦","LeftDownTeeVector":"⥡","LeftDownVectorBar":"⥙","LeftDownVector":"⇃","LeftFloor":"⌊","leftharpoondown":"↽","leftharpoonup":"↼","leftleftarrows":"⇇","leftrightarrow":"↔","LeftRightArrow":"↔","Leftrightarrow":"⇔","leftrightarrows":"⇆","leftrightharpoons":"⇋","leftrightsquigarrow":"↭","LeftRightVector":"⥎","LeftTeeArrow":"↤","LeftTee":"⊣","LeftTeeVector":"⥚","leftthreetimes":"⋋","LeftTriangleBar":"⧏","LeftTriangle":"⊲","LeftTriangleEqual":"⊴","LeftUpDownVector":"⥑","LeftUpTeeVector":"⥠","LeftUpVectorBar":"⥘","LeftUpVector":"↿","LeftVectorBar":"⥒","LeftVector":"↼","lEg":"⪋","leg":"⋚","leq":"≤","leqq":"≦","leqslant":"⩽","lescc":"⪨","les":"⩽","lesdot":"⩿","lesdoto":"⪁","lesdotor":"⪃","lesg":"⋚︀","lesges":"⪓","lessapprox":"⪅","lessdot":"⋖","lesseqgtr":"⋚","lesseqqgtr":"⪋","LessEqualGreater":"⋚","LessFullEqual":"≦","LessGreater":"≶","lessgtr":"≶","LessLess":"⪡","lesssim":"≲","LessSlantEqual":"⩽","LessTilde":"≲","lfisht":"⥼","lfloor":"⌊","Lfr":"𝔏","lfr":"𝔩","lg":"≶","lgE":"⪑","lHar":"⥢","lhard":"↽","lharu":"↼","lharul":"⥪","lhblk":"▄","LJcy":"Љ","ljcy":"љ","llarr":"⇇","ll":"≪","Ll":"⋘","llcorner":"⌞","Lleftarrow":"⇚","llhard":"⥫","lltri":"◺","Lmidot":"Ŀ","lmidot":"ŀ","lmoustache":"⎰","lmoust":"⎰","lnap":"⪉","lnapprox":"⪉","lne":"⪇","lnE":"≨","lneq":"⪇","lneqq":"≨","lnsim":"⋦","loang":"⟬","loarr":"⇽","lobrk":"⟦","longleftarrow":"⟵","LongLeftArrow":"⟵","Longleftarrow":"⟸","longleftrightarrow":"⟷","LongLeftRightArrow":"⟷","Longleftrightarrow":"⟺","longmapsto":"⟼","longrightarrow":"⟶","LongRightArrow":"⟶","Longrightarrow":"⟹","looparrowleft":"↫","looparrowright":"↬","lopar":"⦅","Lopf":"𝕃","lopf":"𝕝","loplus":"⨭","lotimes":"⨴","lowast":"∗","lowbar":"_","LowerLeftArrow":"↙","LowerRightArrow":"↘","loz":"◊","lozenge":"◊","lozf":"⧫","lpar":"(","lparlt":"⦓","lrarr":"⇆","lrcorner":"⌟","lrhar":"⇋","lrhard":"⥭","lrm":"‎","lrtri":"⊿","lsaquo":"‹","lscr":"𝓁","Lscr":"ℒ","lsh":"↰","Lsh":"↰","lsim":"≲","lsime":"⪍","lsimg":"⪏","lsqb":"[","lsquo":"‘","lsquor":"‚","Lstrok":"Ł","lstrok":"ł","ltcc":"⪦","ltcir":"⩹","lt":"<","LT":"<","Lt":"≪","ltdot":"⋖","lthree":"⋋","ltimes":"⋉","ltlarr":"⥶","ltquest":"⩻","ltri":"◃","ltrie":"⊴","ltrif":"◂","ltrPar":"⦖","lurdshar":"⥊","luruhar":"⥦","lvertneqq":"≨︀","lvnE":"≨︀","macr":"¯","male":"♂","malt":"✠","maltese":"✠","Map":"⤅","map":"↦","mapsto":"↦","mapstodown":"↧","mapstoleft":"↤","mapstoup":"↥","marker":"▮","mcomma":"⨩","Mcy":"М","mcy":"м","mdash":"—","mDDot":"∺","measuredangle":"∡","MediumSpace":" ","Mellintrf":"ℳ","Mfr":"𝔐","mfr":"𝔪","mho":"℧","micro":"µ","midast":"*","midcir":"⫰","mid":"∣","middot":"·","minusb":"⊟","minus":"−","minusd":"∸","minusdu":"⨪","MinusPlus":"∓","mlcp":"⫛","mldr":"…","mnplus":"∓","models":"⊧","Mopf":"𝕄","mopf":"𝕞","mp":"∓","mscr":"𝓂","Mscr":"ℳ","mstpos":"∾","Mu":"Μ","mu":"μ","multimap":"⊸","mumap":"⊸","nabla":"∇","Nacute":"Ń","nacute":"ń","nang":"∠⃒","nap":"≉","napE":"⩰̸","napid":"≋̸","napos":"ŉ","napprox":"≉","natural":"♮","naturals":"ℕ","natur":"♮","nbsp":" ","nbump":"≎̸","nbumpe":"≏̸","ncap":"⩃","Ncaron":"Ň","ncaron":"ň","Ncedil":"Ņ","ncedil":"ņ","ncong":"≇","ncongdot":"⩭̸","ncup":"⩂","Ncy":"Н","ncy":"н","ndash":"–","nearhk":"⤤","nearr":"↗","neArr":"⇗","nearrow":"↗","ne":"≠","nedot":"≐̸","NegativeMediumSpace":"​","NegativeThickSpace":"​","NegativeThinSpace":"​","NegativeVeryThinSpace":"​","nequiv":"≢","nesear":"⤨","nesim":"≂̸","NestedGreaterGreater":"≫","NestedLessLess":"≪","NewLine":"\\n","nexist":"∄","nexists":"∄","Nfr":"𝔑","nfr":"𝔫","ngE":"≧̸","nge":"≱","ngeq":"≱","ngeqq":"≧̸","ngeqslant":"⩾̸","nges":"⩾̸","nGg":"⋙̸","ngsim":"≵","nGt":"≫⃒","ngt":"≯","ngtr":"≯","nGtv":"≫̸","nharr":"↮","nhArr":"⇎","nhpar":"⫲","ni":"∋","nis":"⋼","nisd":"⋺","niv":"∋","NJcy":"Њ","njcy":"њ","nlarr":"↚","nlArr":"⇍","nldr":"‥","nlE":"≦̸","nle":"≰","nleftarrow":"↚","nLeftarrow":"⇍","nleftrightarrow":"↮","nLeftrightarrow":"⇎","nleq":"≰","nleqq":"≦̸","nleqslant":"⩽̸","nles":"⩽̸","nless":"≮","nLl":"⋘̸","nlsim":"≴","nLt":"≪⃒","nlt":"≮","nltri":"⋪","nltrie":"⋬","nLtv":"≪̸","nmid":"∤","NoBreak":"⁠","NonBreakingSpace":" ","nopf":"𝕟","Nopf":"ℕ","Not":"⫬","not":"¬","NotCongruent":"≢","NotCupCap":"≭","NotDoubleVerticalBar":"∦","NotElement":"∉","NotEqual":"≠","NotEqualTilde":"≂̸","NotExists":"∄","NotGreater":"≯","NotGreaterEqual":"≱","NotGreaterFullEqual":"≧̸","NotGreaterGreater":"≫̸","NotGreaterLess":"≹","NotGreaterSlantEqual":"⩾̸","NotGreaterTilde":"≵","NotHumpDownHump":"≎̸","NotHumpEqual":"≏̸","notin":"∉","notindot":"⋵̸","notinE":"⋹̸","notinva":"∉","notinvb":"⋷","notinvc":"⋶","NotLeftTriangleBar":"⧏̸","NotLeftTriangle":"⋪","NotLeftTriangleEqual":"⋬","NotLess":"≮","NotLessEqual":"≰","NotLessGreater":"≸","NotLessLess":"≪̸","NotLessSlantEqual":"⩽̸","NotLessTilde":"≴","NotNestedGreaterGreater":"⪢̸","NotNestedLessLess":"⪡̸","notni":"∌","notniva":"∌","notnivb":"⋾","notnivc":"⋽","NotPrecedes":"⊀","NotPrecedesEqual":"⪯̸","NotPrecedesSlantEqual":"⋠","NotReverseElement":"∌","NotRightTriangleBar":"⧐̸","NotRightTriangle":"⋫","NotRightTriangleEqual":"⋭","NotSquareSubset":"⊏̸","NotSquareSubsetEqual":"⋢","NotSquareSuperset":"⊐̸","NotSquareSupersetEqual":"⋣","NotSubset":"⊂⃒","NotSubsetEqual":"⊈","NotSucceeds":"⊁","NotSucceedsEqual":"⪰̸","NotSucceedsSlantEqual":"⋡","NotSucceedsTilde":"≿̸","NotSuperset":"⊃⃒","NotSupersetEqual":"⊉","NotTilde":"≁","NotTildeEqual":"≄","NotTildeFullEqual":"≇","NotTildeTilde":"≉","NotVerticalBar":"∤","nparallel":"∦","npar":"∦","nparsl":"⫽⃥","npart":"∂̸","npolint":"⨔","npr":"⊀","nprcue":"⋠","nprec":"⊀","npreceq":"⪯̸","npre":"⪯̸","nrarrc":"⤳̸","nrarr":"↛","nrArr":"⇏","nrarrw":"↝̸","nrightarrow":"↛","nRightarrow":"⇏","nrtri":"⋫","nrtrie":"⋭","nsc":"⊁","nsccue":"⋡","nsce":"⪰̸","Nscr":"𝒩","nscr":"𝓃","nshortmid":"∤","nshortparallel":"∦","nsim":"≁","nsime":"≄","nsimeq":"≄","nsmid":"∤","nspar":"∦","nsqsube":"⋢","nsqsupe":"⋣","nsub":"⊄","nsubE":"⫅̸","nsube":"⊈","nsubset":"⊂⃒","nsubseteq":"⊈","nsubseteqq":"⫅̸","nsucc":"⊁","nsucceq":"⪰̸","nsup":"⊅","nsupE":"⫆̸","nsupe":"⊉","nsupset":"⊃⃒","nsupseteq":"⊉","nsupseteqq":"⫆̸","ntgl":"≹","Ntilde":"Ñ","ntilde":"ñ","ntlg":"≸","ntriangleleft":"⋪","ntrianglelefteq":"⋬","ntriangleright":"⋫","ntrianglerighteq":"⋭","Nu":"Ν","nu":"ν","num":"#","numero":"№","numsp":" ","nvap":"≍⃒","nvdash":"⊬","nvDash":"⊭","nVdash":"⊮","nVDash":"⊯","nvge":"≥⃒","nvgt":">⃒","nvHarr":"⤄","nvinfin":"⧞","nvlArr":"⤂","nvle":"≤⃒","nvlt":"<⃒","nvltrie":"⊴⃒","nvrArr":"⤃","nvrtrie":"⊵⃒","nvsim":"∼⃒","nwarhk":"⤣","nwarr":"↖","nwArr":"⇖","nwarrow":"↖","nwnear":"⤧","Oacute":"Ó","oacute":"ó","oast":"⊛","Ocirc":"Ô","ocirc":"ô","ocir":"⊚","Ocy":"О","ocy":"о","odash":"⊝","Odblac":"Ő","odblac":"ő","odiv":"⨸","odot":"⊙","odsold":"⦼","OElig":"Œ","oelig":"œ","ofcir":"⦿","Ofr":"𝔒","ofr":"𝔬","ogon":"˛","Ograve":"Ò","ograve":"ò","ogt":"⧁","ohbar":"⦵","ohm":"Ω","oint":"∮","olarr":"↺","olcir":"⦾","olcross":"⦻","oline":"‾","olt":"⧀","Omacr":"Ō","omacr":"ō","Omega":"Ω","omega":"ω","Omicron":"Ο","omicron":"ο","omid":"⦶","ominus":"⊖","Oopf":"𝕆","oopf":"𝕠","opar":"⦷","OpenCurlyDoubleQuote":"“","OpenCurlyQuote":"‘","operp":"⦹","oplus":"⊕","orarr":"↻","Or":"⩔","or":"∨","ord":"⩝","order":"ℴ","orderof":"ℴ","ordf":"ª","ordm":"º","origof":"⊶","oror":"⩖","orslope":"⩗","orv":"⩛","oS":"Ⓢ","Oscr":"𝒪","oscr":"ℴ","Oslash":"Ø","oslash":"ø","osol":"⊘","Otilde":"Õ","otilde":"õ","otimesas":"⨶","Otimes":"⨷","otimes":"⊗","Ouml":"Ö","ouml":"ö","ovbar":"⌽","OverBar":"‾","OverBrace":"⏞","OverBracket":"⎴","OverParenthesis":"⏜","para":"¶","parallel":"∥","par":"∥","parsim":"⫳","parsl":"⫽","part":"∂","PartialD":"∂","Pcy":"П","pcy":"п","percnt":"%","period":".","permil":"‰","perp":"⊥","pertenk":"‱","Pfr":"𝔓","pfr":"𝔭","Phi":"Φ","phi":"φ","phiv":"ϕ","phmmat":"ℳ","phone":"☎","Pi":"Π","pi":"π","pitchfork":"⋔","piv":"ϖ","planck":"ℏ","planckh":"ℎ","plankv":"ℏ","plusacir":"⨣","plusb":"⊞","pluscir":"⨢","plus":"+","plusdo":"∔","plusdu":"⨥","pluse":"⩲","PlusMinus":"±","plusmn":"±","plussim":"⨦","plustwo":"⨧","pm":"±","Poincareplane":"ℌ","pointint":"⨕","popf":"𝕡","Popf":"ℙ","pound":"£","prap":"⪷","Pr":"⪻","pr":"≺","prcue":"≼","precapprox":"⪷","prec":"≺","preccurlyeq":"≼","Precedes":"≺","PrecedesEqual":"⪯","PrecedesSlantEqual":"≼","PrecedesTilde":"≾","preceq":"⪯","precnapprox":"⪹","precneqq":"⪵","precnsim":"⋨","pre":"⪯","prE":"⪳","precsim":"≾","prime":"′","Prime":"″","primes":"ℙ","prnap":"⪹","prnE":"⪵","prnsim":"⋨","prod":"∏","Product":"∏","profalar":"⌮","profline":"⌒","profsurf":"⌓","prop":"∝","Proportional":"∝","Proportion":"∷","propto":"∝","prsim":"≾","prurel":"⊰","Pscr":"𝒫","pscr":"𝓅","Psi":"Ψ","psi":"ψ","puncsp":" ","Qfr":"𝔔","qfr":"𝔮","qint":"⨌","qopf":"𝕢","Qopf":"ℚ","qprime":"⁗","Qscr":"𝒬","qscr":"𝓆","quaternions":"ℍ","quatint":"⨖","quest":"?","questeq":"≟","quot":"\\"","QUOT":"\\"","rAarr":"⇛","race":"∽̱","Racute":"Ŕ","racute":"ŕ","radic":"√","raemptyv":"⦳","rang":"⟩","Rang":"⟫","rangd":"⦒","range":"⦥","rangle":"⟩","raquo":"»","rarrap":"⥵","rarrb":"⇥","rarrbfs":"⤠","rarrc":"⤳","rarr":"→","Rarr":"↠","rArr":"⇒","rarrfs":"⤞","rarrhk":"↪","rarrlp":"↬","rarrpl":"⥅","rarrsim":"⥴","Rarrtl":"⤖","rarrtl":"↣","rarrw":"↝","ratail":"⤚","rAtail":"⤜","ratio":"∶","rationals":"ℚ","rbarr":"⤍","rBarr":"⤏","RBarr":"⤐","rbbrk":"❳","rbrace":"}","rbrack":"]","rbrke":"⦌","rbrksld":"⦎","rbrkslu":"⦐","Rcaron":"Ř","rcaron":"ř","Rcedil":"Ŗ","rcedil":"ŗ","rceil":"⌉","rcub":"}","Rcy":"Р","rcy":"р","rdca":"⤷","rdldhar":"⥩","rdquo":"”","rdquor":"”","rdsh":"↳","real":"ℜ","realine":"ℛ","realpart":"ℜ","reals":"ℝ","Re":"ℜ","rect":"▭","reg":"®","REG":"®","ReverseElement":"∋","ReverseEquilibrium":"⇋","ReverseUpEquilibrium":"⥯","rfisht":"⥽","rfloor":"⌋","rfr":"𝔯","Rfr":"ℜ","rHar":"⥤","rhard":"⇁","rharu":"⇀","rharul":"⥬","Rho":"Ρ","rho":"ρ","rhov":"ϱ","RightAngleBracket":"⟩","RightArrowBar":"⇥","rightarrow":"→","RightArrow":"→","Rightarrow":"⇒","RightArrowLeftArrow":"⇄","rightarrowtail":"↣","RightCeiling":"⌉","RightDoubleBracket":"⟧","RightDownTeeVector":"⥝","RightDownVectorBar":"⥕","RightDownVector":"⇂","RightFloor":"⌋","rightharpoondown":"⇁","rightharpoonup":"⇀","rightleftarrows":"⇄","rightleftharpoons":"⇌","rightrightarrows":"⇉","rightsquigarrow":"↝","RightTeeArrow":"↦","RightTee":"⊢","RightTeeVector":"⥛","rightthreetimes":"⋌","RightTriangleBar":"⧐","RightTriangle":"⊳","RightTriangleEqual":"⊵","RightUpDownVector":"⥏","RightUpTeeVector":"⥜","RightUpVectorBar":"⥔","RightUpVector":"↾","RightVectorBar":"⥓","RightVector":"⇀","ring":"˚","risingdotseq":"≓","rlarr":"⇄","rlhar":"⇌","rlm":"‏","rmoustache":"⎱","rmoust":"⎱","rnmid":"⫮","roang":"⟭","roarr":"⇾","robrk":"⟧","ropar":"⦆","ropf":"𝕣","Ropf":"ℝ","roplus":"⨮","rotimes":"⨵","RoundImplies":"⥰","rpar":")","rpargt":"⦔","rppolint":"⨒","rrarr":"⇉","Rrightarrow":"⇛","rsaquo":"›","rscr":"𝓇","Rscr":"ℛ","rsh":"↱","Rsh":"↱","rsqb":"]","rsquo":"’","rsquor":"’","rthree":"⋌","rtimes":"⋊","rtri":"▹","rtrie":"⊵","rtrif":"▸","rtriltri":"⧎","RuleDelayed":"⧴","ruluhar":"⥨","rx":"℞","Sacute":"Ś","sacute":"ś","sbquo":"‚","scap":"⪸","Scaron":"Š","scaron":"š","Sc":"⪼","sc":"≻","sccue":"≽","sce":"⪰","scE":"⪴","Scedil":"Ş","scedil":"ş","Scirc":"Ŝ","scirc":"ŝ","scnap":"⪺","scnE":"⪶","scnsim":"⋩","scpolint":"⨓","scsim":"≿","Scy":"С","scy":"с","sdotb":"⊡","sdot":"⋅","sdote":"⩦","searhk":"⤥","searr":"↘","seArr":"⇘","searrow":"↘","sect":"§","semi":";","seswar":"⤩","setminus":"∖","setmn":"∖","sext":"✶","Sfr":"𝔖","sfr":"𝔰","sfrown":"⌢","sharp":"♯","SHCHcy":"Щ","shchcy":"щ","SHcy":"Ш","shcy":"ш","ShortDownArrow":"↓","ShortLeftArrow":"←","shortmid":"∣","shortparallel":"∥","ShortRightArrow":"→","ShortUpArrow":"↑","shy":"­","Sigma":"Σ","sigma":"σ","sigmaf":"ς","sigmav":"ς","sim":"∼","simdot":"⩪","sime":"≃","simeq":"≃","simg":"⪞","simgE":"⪠","siml":"⪝","simlE":"⪟","simne":"≆","simplus":"⨤","simrarr":"⥲","slarr":"←","SmallCircle":"∘","smallsetminus":"∖","smashp":"⨳","smeparsl":"⧤","smid":"∣","smile":"⌣","smt":"⪪","smte":"⪬","smtes":"⪬︀","SOFTcy":"Ь","softcy":"ь","solbar":"⌿","solb":"⧄","sol":"/","Sopf":"𝕊","sopf":"𝕤","spades":"♠","spadesuit":"♠","spar":"∥","sqcap":"⊓","sqcaps":"⊓︀","sqcup":"⊔","sqcups":"⊔︀","Sqrt":"√","sqsub":"⊏","sqsube":"⊑","sqsubset":"⊏","sqsubseteq":"⊑","sqsup":"⊐","sqsupe":"⊒","sqsupset":"⊐","sqsupseteq":"⊒","square":"□","Square":"□","SquareIntersection":"⊓","SquareSubset":"⊏","SquareSubsetEqual":"⊑","SquareSuperset":"⊐","SquareSupersetEqual":"⊒","SquareUnion":"⊔","squarf":"▪","squ":"□","squf":"▪","srarr":"→","Sscr":"𝒮","sscr":"𝓈","ssetmn":"∖","ssmile":"⌣","sstarf":"⋆","Star":"⋆","star":"☆","starf":"★","straightepsilon":"ϵ","straightphi":"ϕ","strns":"¯","sub":"⊂","Sub":"⋐","subdot":"⪽","subE":"⫅","sube":"⊆","subedot":"⫃","submult":"⫁","subnE":"⫋","subne":"⊊","subplus":"⪿","subrarr":"⥹","subset":"⊂","Subset":"⋐","subseteq":"⊆","subseteqq":"⫅","SubsetEqual":"⊆","subsetneq":"⊊","subsetneqq":"⫋","subsim":"⫇","subsub":"⫕","subsup":"⫓","succapprox":"⪸","succ":"≻","succcurlyeq":"≽","Succeeds":"≻","SucceedsEqual":"⪰","SucceedsSlantEqual":"≽","SucceedsTilde":"≿","succeq":"⪰","succnapprox":"⪺","succneqq":"⪶","succnsim":"⋩","succsim":"≿","SuchThat":"∋","sum":"∑","Sum":"∑","sung":"♪","sup1":"¹","sup2":"²","sup3":"³","sup":"⊃","Sup":"⋑","supdot":"⪾","supdsub":"⫘","supE":"⫆","supe":"⊇","supedot":"⫄","Superset":"⊃","SupersetEqual":"⊇","suphsol":"⟉","suphsub":"⫗","suplarr":"⥻","supmult":"⫂","supnE":"⫌","supne":"⊋","supplus":"⫀","supset":"⊃","Supset":"⋑","supseteq":"⊇","supseteqq":"⫆","supsetneq":"⊋","supsetneqq":"⫌","supsim":"⫈","supsub":"⫔","supsup":"⫖","swarhk":"⤦","swarr":"↙","swArr":"⇙","swarrow":"↙","swnwar":"⤪","szlig":"ß","Tab":"\\t","target":"⌖","Tau":"Τ","tau":"τ","tbrk":"⎴","Tcaron":"Ť","tcaron":"ť","Tcedil":"Ţ","tcedil":"ţ","Tcy":"Т","tcy":"т","tdot":"⃛","telrec":"⌕","Tfr":"𝔗","tfr":"𝔱","there4":"∴","therefore":"∴","Therefore":"∴","Theta":"Θ","theta":"θ","thetasym":"ϑ","thetav":"ϑ","thickapprox":"≈","thicksim":"∼","ThickSpace":"  ","ThinSpace":" ","thinsp":" ","thkap":"≈","thksim":"∼","THORN":"Þ","thorn":"þ","tilde":"˜","Tilde":"∼","TildeEqual":"≃","TildeFullEqual":"≅","TildeTilde":"≈","timesbar":"⨱","timesb":"⊠","times":"×","timesd":"⨰","tint":"∭","toea":"⤨","topbot":"⌶","topcir":"⫱","top":"⊤","Topf":"𝕋","topf":"𝕥","topfork":"⫚","tosa":"⤩","tprime":"‴","trade":"™","TRADE":"™","triangle":"▵","triangledown":"▿","triangleleft":"◃","trianglelefteq":"⊴","triangleq":"≜","triangleright":"▹","trianglerighteq":"⊵","tridot":"◬","trie":"≜","triminus":"⨺","TripleDot":"⃛","triplus":"⨹","trisb":"⧍","tritime":"⨻","trpezium":"⏢","Tscr":"𝒯","tscr":"𝓉","TScy":"Ц","tscy":"ц","TSHcy":"Ћ","tshcy":"ћ","Tstrok":"Ŧ","tstrok":"ŧ","twixt":"≬","twoheadleftarrow":"↞","twoheadrightarrow":"↠","Uacute":"Ú","uacute":"ú","uarr":"↑","Uarr":"↟","uArr":"⇑","Uarrocir":"⥉","Ubrcy":"Ў","ubrcy":"ў","Ubreve":"Ŭ","ubreve":"ŭ","Ucirc":"Û","ucirc":"û","Ucy":"У","ucy":"у","udarr":"⇅","Udblac":"Ű","udblac":"ű","udhar":"⥮","ufisht":"⥾","Ufr":"𝔘","ufr":"𝔲","Ugrave":"Ù","ugrave":"ù","uHar":"⥣","uharl":"↿","uharr":"↾","uhblk":"▀","ulcorn":"⌜","ulcorner":"⌜","ulcrop":"⌏","ultri":"◸","Umacr":"Ū","umacr":"ū","uml":"¨","UnderBar":"_","UnderBrace":"⏟","UnderBracket":"⎵","UnderParenthesis":"⏝","Union":"⋃","UnionPlus":"⊎","Uogon":"Ų","uogon":"ų","Uopf":"𝕌","uopf":"𝕦","UpArrowBar":"⤒","uparrow":"↑","UpArrow":"↑","Uparrow":"⇑","UpArrowDownArrow":"⇅","updownarrow":"↕","UpDownArrow":"↕","Updownarrow":"⇕","UpEquilibrium":"⥮","upharpoonleft":"↿","upharpoonright":"↾","uplus":"⊎","UpperLeftArrow":"↖","UpperRightArrow":"↗","upsi":"υ","Upsi":"ϒ","upsih":"ϒ","Upsilon":"Υ","upsilon":"υ","UpTeeArrow":"↥","UpTee":"⊥","upuparrows":"⇈","urcorn":"⌝","urcorner":"⌝","urcrop":"⌎","Uring":"Ů","uring":"ů","urtri":"◹","Uscr":"𝒰","uscr":"𝓊","utdot":"⋰","Utilde":"Ũ","utilde":"ũ","utri":"▵","utrif":"▴","uuarr":"⇈","Uuml":"Ü","uuml":"ü","uwangle":"⦧","vangrt":"⦜","varepsilon":"ϵ","varkappa":"ϰ","varnothing":"∅","varphi":"ϕ","varpi":"ϖ","varpropto":"∝","varr":"↕","vArr":"⇕","varrho":"ϱ","varsigma":"ς","varsubsetneq":"⊊︀","varsubsetneqq":"⫋︀","varsupsetneq":"⊋︀","varsupsetneqq":"⫌︀","vartheta":"ϑ","vartriangleleft":"⊲","vartriangleright":"⊳","vBar":"⫨","Vbar":"⫫","vBarv":"⫩","Vcy":"В","vcy":"в","vdash":"⊢","vDash":"⊨","Vdash":"⊩","VDash":"⊫","Vdashl":"⫦","veebar":"⊻","vee":"∨","Vee":"⋁","veeeq":"≚","vellip":"⋮","verbar":"|","Verbar":"‖","vert":"|","Vert":"‖","VerticalBar":"∣","VerticalLine":"|","VerticalSeparator":"❘","VerticalTilde":"≀","VeryThinSpace":" ","Vfr":"𝔙","vfr":"𝔳","vltri":"⊲","vnsub":"⊂⃒","vnsup":"⊃⃒","Vopf":"𝕍","vopf":"𝕧","vprop":"∝","vrtri":"⊳","Vscr":"𝒱","vscr":"𝓋","vsubnE":"⫋︀","vsubne":"⊊︀","vsupnE":"⫌︀","vsupne":"⊋︀","Vvdash":"⊪","vzigzag":"⦚","Wcirc":"Ŵ","wcirc":"ŵ","wedbar":"⩟","wedge":"∧","Wedge":"⋀","wedgeq":"≙","weierp":"℘","Wfr":"𝔚","wfr":"𝔴","Wopf":"𝕎","wopf":"𝕨","wp":"℘","wr":"≀","wreath":"≀","Wscr":"𝒲","wscr":"𝓌","xcap":"⋂","xcirc":"◯","xcup":"⋃","xdtri":"▽","Xfr":"𝔛","xfr":"𝔵","xharr":"⟷","xhArr":"⟺","Xi":"Ξ","xi":"ξ","xlarr":"⟵","xlArr":"⟸","xmap":"⟼","xnis":"⋻","xodot":"⨀","Xopf":"𝕏","xopf":"𝕩","xoplus":"⨁","xotime":"⨂","xrarr":"⟶","xrArr":"⟹","Xscr":"𝒳","xscr":"𝓍","xsqcup":"⨆","xuplus":"⨄","xutri":"△","xvee":"⋁","xwedge":"⋀","Yacute":"Ý","yacute":"ý","YAcy":"Я","yacy":"я","Ycirc":"Ŷ","ycirc":"ŷ","Ycy":"Ы","ycy":"ы","yen":"¥","Yfr":"𝔜","yfr":"𝔶","YIcy":"Ї","yicy":"ї","Yopf":"𝕐","yopf":"𝕪","Yscr":"𝒴","yscr":"𝓎","YUcy":"Ю","yucy":"ю","yuml":"ÿ","Yuml":"Ÿ","Zacute":"Ź","zacute":"ź","Zcaron":"Ž","zcaron":"ž","Zcy":"З","zcy":"з","Zdot":"Ż","zdot":"ż","zeetrf":"ℨ","ZeroWidthSpace":"​","Zeta":"Ζ","zeta":"ζ","zfr":"𝔷","Zfr":"ℨ","ZHcy":"Ж","zhcy":"ж","zigrarr":"⇝","zopf":"𝕫","Zopf":"ℤ","Zscr":"𝒵","zscr":"𝓏","zwj":"‍","zwnj":"‌"}')},function(e){e.exports=JSON.parse('{"amp":"&","apos":"\'","gt":">","lt":"<","quot":"\\""}')},function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.escapeUTF8=t.escape=t.encodeNonAsciiHTML=t.encodeHTML=t.encodeXML=void 0;var i=c(n(r(21)).default),a=f(i);t.encodeXML=g(i);var o,s,l=c(n(r(20)).default),u=f(l);function c(e){return Object.keys(e).sort().reduce((function(t,r){return t[e[r]]="&"+r+";",t}),{})}function f(e){for(var t=[],r=[],n=0,i=Object.keys(e);n<i.length;n++){var a=i[n];1===a.length?t.push("\\"+a):r.push(a)}t.sort();for(var o=0;o<t.length-1;o++){for(var s=o;s<t.length-1&&t[s].charCodeAt(1)+1===t[s+1].charCodeAt(1);)s+=1;var l=1+s-o;l<3||t.splice(o,l,t[o]+"-"+t[s])}return r.unshift("["+t.join("")+"]"),new RegExp(r.join("|"),"g")}t.encodeHTML=(o=l,s=u,function(e){return e.replace(s,(function(e){return o[e]})).replace(p,h)}),t.encodeNonAsciiHTML=g(l);var p=/(?:[\x80-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/g,d=null!=String.prototype.codePointAt?function(e){return e.codePointAt(0)}:function(e){return 1024*(e.charCodeAt(0)-55296)+e.charCodeAt(1)-56320+65536};function h(e){return"&#x"+(e.length>1?d(e):e.charCodeAt(0)).toString(16).toUpperCase()+";"}var m=new RegExp(a.source+"|"+p.source,"g");function g(e){return function(t){return t.replace(m,(function(t){return e[t]||h(t)}))}}t.escape=function(e){return e.replace(m,h)},t.escapeUTF8=function(e){return e.replace(a,h)}},function(e,t,r){e.exports=s;var n=r(14),i=r(51).Writable,a=r(52).StringDecoder,o=r(24).Buffer;function s(e,t){var r=this._parser=new n(e,t),o=this._decoder=new a;i.call(this,{decodeStrings:!1}),this.once("finish",(function(){r.end(o.end())}))}r(6)(s,i),s.prototype._write=function(e,t,r){e instanceof o&&(e=this._decoder.write(e)),this._parser.write(e),r()}},function(e,t,r){"use strict";(function(e){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var n=r(55),i=r(56),a=r(57);function o(){return l.TYPED_ARRAY_SUPPORT?**********:**********}function s(e,t){if(o()<t)throw new RangeError("Invalid typed array length");return l.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=l.prototype:(null===e&&(e=new l(t)),e.length=t),e}function l(e,t,r){if(!(l.TYPED_ARRAY_SUPPORT||this instanceof l))return new l(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return f(this,e)}return u(this,e,t,r)}function u(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n);l.TYPED_ARRAY_SUPPORT?(e=t).__proto__=l.prototype:e=p(e,t);return e}(e,t,r,n):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!l.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|h(t,r),i=(e=s(e,n)).write(t,r);i!==n&&(e=e.slice(0,i));return e}(e,t,r):function(e,t){if(l.isBuffer(t)){var r=0|d(t.length);return 0===(e=s(e,r)).length||t.copy(e,0,0,r),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(n=t.length)!=n?s(e,0):p(e,t);if("Buffer"===t.type&&a(t.data))return p(e,t.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function c(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function f(e,t){if(c(t),e=s(e,t<0?0:0|d(t)),!l.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function p(e,t){var r=t.length<0?0:0|d(t.length);e=s(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function d(e){if(e>=o())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|e}function h(e,t){if(l.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return U(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return V(e).length;default:if(n)return U(e).length;t=(""+t).toLowerCase(),n=!0}}function m(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return L(this,t,r);case"utf8":case"utf-8":return T(this,t,r);case"ascii":return C(this,t,r);case"latin1":case"binary":return A(this,t,r);case"base64":return k(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return N(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function g(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function y(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=l.from(t,n)),l.isBuffer(t))return 0===t.length?-1:b(e,t,r,n,i);if("number"==typeof t)return t&=255,l.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):b(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function b(e,t,r,n,i){var a,o=1,s=e.length,l=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;o=2,s/=2,l/=2,r/=2}function u(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var c=-1;for(a=r;a<s;a++)if(u(e,a)===u(t,-1===c?0:a-c)){if(-1===c&&(c=a),a-c+1===l)return c*o}else-1!==c&&(a-=a-c),c=-1}else for(r+l>s&&(r=s-l),a=r;a>=0;a--){for(var f=!0,p=0;p<l;p++)if(u(e,a+p)!==u(t,p)){f=!1;break}if(f)return a}return-1}function v(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=t.length;if(a%2!=0)throw new TypeError("Invalid hex string");n>a/2&&(n=a/2);for(var o=0;o<n;++o){var s=parseInt(t.substr(2*o,2),16);if(isNaN(s))return o;e[r+o]=s}return o}function w(e,t,r,n){return z(U(t,e.length-r),e,r,n)}function _(e,t,r,n){return z(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function x(e,t,r,n){return _(e,t,r,n)}function E(e,t,r,n){return z(V(t),e,r,n)}function S(e,t,r,n){return z(function(e,t){for(var r,n,i,a=[],o=0;o<e.length&&!((t-=2)<0);++o)r=e.charCodeAt(o),n=r>>8,i=r%256,a.push(i),a.push(n);return a}(t,e.length-r),e,r,n)}function k(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function T(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var a,o,s,l,u=e[i],c=null,f=u>239?4:u>223?3:u>191?2:1;if(i+f<=r)switch(f){case 1:u<128&&(c=u);break;case 2:128==(192&(a=e[i+1]))&&(l=(31&u)<<6|63&a)>127&&(c=l);break;case 3:a=e[i+1],o=e[i+2],128==(192&a)&&128==(192&o)&&(l=(15&u)<<12|(63&a)<<6|63&o)>2047&&(l<55296||l>57343)&&(c=l);break;case 4:a=e[i+1],o=e[i+2],s=e[i+3],128==(192&a)&&128==(192&o)&&128==(192&s)&&(l=(15&u)<<18|(63&a)<<12|(63&o)<<6|63&s)>65535&&l<1114112&&(c=l)}null===c?(c=65533,f=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=f}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}t.Buffer=l,t.SlowBuffer=function(e){+e!=e&&(e=0);return l.alloc(+e)},t.INSPECT_MAX_BYTES=50,l.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=o(),l.poolSize=8192,l._augment=function(e){return e.__proto__=l.prototype,e},l.from=function(e,t,r){return u(null,e,t,r)},l.TYPED_ARRAY_SUPPORT&&(l.prototype.__proto__=Uint8Array.prototype,l.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&l[Symbol.species]===l&&Object.defineProperty(l,Symbol.species,{value:null,configurable:!0})),l.alloc=function(e,t,r){return function(e,t,r,n){return c(t),t<=0?s(e,t):void 0!==r?"string"==typeof n?s(e,t).fill(r,n):s(e,t).fill(r):s(e,t)}(null,e,t,r)},l.allocUnsafe=function(e){return f(null,e)},l.allocUnsafeSlow=function(e){return f(null,e)},l.isBuffer=function(e){return!(null==e||!e._isBuffer)},l.compare=function(e,t){if(!l.isBuffer(e)||!l.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},l.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},l.concat=function(e,t){if(!a(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return l.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=l.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(!l.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},l.byteLength=h,l.prototype._isBuffer=!0,l.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},l.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},l.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},l.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?T(this,0,e):m.apply(this,arguments)},l.prototype.equals=function(e){if(!l.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===l.compare(this,e)},l.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},l.prototype.compare=function(e,t,r,n,i){if(!l.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var a=(i>>>=0)-(n>>>=0),o=(r>>>=0)-(t>>>=0),s=Math.min(a,o),u=this.slice(n,i),c=e.slice(t,r),f=0;f<s;++f)if(u[f]!==c[f]){a=u[f],o=c[f];break}return a<o?-1:o<a?1:0},l.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},l.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},l.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)},l.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var a=!1;;)switch(n){case"hex":return v(this,e,t,r);case"utf8":case"utf-8":return w(this,e,t,r);case"ascii":return _(this,e,t,r);case"latin1":case"binary":return x(this,e,t,r);case"base64":return E(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,e,t,r);default:if(a)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),a=!0}},l.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function C(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function A(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function L(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",a=t;a<r;++a)i+=B(e[a]);return i}function N(e,t,r){for(var n=e.slice(t,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}function P(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function R(e,t,r,n,i,a){if(!l.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<a)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function D(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,a=Math.min(e.length-r,2);i<a;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function q(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,a=Math.min(e.length-r,4);i<a;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function M(e,t,r,n,i,a){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function O(e,t,r,n,a){return a||M(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function I(e,t,r,n,a){return a||M(e,0,r,8),i.write(e,t,r,n,52,8),r+8}l.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),l.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=l.prototype;else{var i=t-e;r=new l(i,void 0);for(var a=0;a<i;++a)r[a]=this[a+e]}return r},l.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||P(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n},l.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||P(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},l.prototype.readUInt8=function(e,t){return t||P(e,1,this.length),this[e]},l.prototype.readUInt16LE=function(e,t){return t||P(e,2,this.length),this[e]|this[e+1]<<8},l.prototype.readUInt16BE=function(e,t){return t||P(e,2,this.length),this[e]<<8|this[e+1]},l.prototype.readUInt32LE=function(e,t){return t||P(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},l.prototype.readUInt32BE=function(e,t){return t||P(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},l.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||P(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},l.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||P(e,t,this.length);for(var n=t,i=1,a=this[e+--n];n>0&&(i*=256);)a+=this[e+--n]*i;return a>=(i*=128)&&(a-=Math.pow(2,8*t)),a},l.prototype.readInt8=function(e,t){return t||P(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},l.prototype.readInt16LE=function(e,t){t||P(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},l.prototype.readInt16BE=function(e,t){t||P(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},l.prototype.readInt32LE=function(e,t){return t||P(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},l.prototype.readInt32BE=function(e,t){return t||P(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},l.prototype.readFloatLE=function(e,t){return t||P(e,4,this.length),i.read(this,e,!0,23,4)},l.prototype.readFloatBE=function(e,t){return t||P(e,4,this.length),i.read(this,e,!1,23,4)},l.prototype.readDoubleLE=function(e,t){return t||P(e,8,this.length),i.read(this,e,!0,52,8)},l.prototype.readDoubleBE=function(e,t){return t||P(e,8,this.length),i.read(this,e,!1,52,8)},l.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||R(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,a=0;for(this[t]=255&e;++a<r&&(i*=256);)this[t+a]=e/i&255;return t+r},l.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||R(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,a=1;for(this[t+i]=255&e;--i>=0&&(a*=256);)this[t+i]=e/a&255;return t+r},l.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||R(this,e,t,1,255,0),l.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},l.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||R(this,e,t,2,65535,0),l.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):D(this,e,t,!0),t+2},l.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||R(this,e,t,2,65535,0),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):D(this,e,t,!1),t+2},l.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||R(this,e,t,4,4294967295,0),l.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):q(this,e,t,!0),t+4},l.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||R(this,e,t,4,4294967295,0),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):q(this,e,t,!1),t+4},l.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);R(this,e,t,r,i-1,-i)}var a=0,o=1,s=0;for(this[t]=255&e;++a<r&&(o*=256);)e<0&&0===s&&0!==this[t+a-1]&&(s=1),this[t+a]=(e/o>>0)-s&255;return t+r},l.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);R(this,e,t,r,i-1,-i)}var a=r-1,o=1,s=0;for(this[t+a]=255&e;--a>=0&&(o*=256);)e<0&&0===s&&0!==this[t+a+1]&&(s=1),this[t+a]=(e/o>>0)-s&255;return t+r},l.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||R(this,e,t,1,127,-128),l.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},l.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||R(this,e,t,2,32767,-32768),l.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):D(this,e,t,!0),t+2},l.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||R(this,e,t,2,32767,-32768),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):D(this,e,t,!1),t+2},l.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||R(this,e,t,4,**********,-2147483648),l.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):q(this,e,t,!0),t+4},l.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||R(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):q(this,e,t,!1),t+4},l.prototype.writeFloatLE=function(e,t,r){return O(this,e,t,!0,r)},l.prototype.writeFloatBE=function(e,t,r){return O(this,e,t,!1,r)},l.prototype.writeDoubleLE=function(e,t,r){return I(this,e,t,!0,r)},l.prototype.writeDoubleBE=function(e,t,r){return I(this,e,t,!1,r)},l.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,a=n-r;if(this===e&&r<t&&t<n)for(i=a-1;i>=0;--i)e[i+t]=this[i+r];else if(a<1e3||!l.TYPED_ARRAY_SUPPORT)for(i=0;i<a;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+a),t);return a},l.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!l.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var a;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(a=t;a<r;++a)this[a]=e;else{var o=l.isBuffer(e)?e:U(new l(e,n).toString()),s=o.length;for(a=0;a<r-t;++a)this[a+t]=o[a%s]}return this};var H=/[^+\/0-9A-Za-z-_]/g;function B(e){return e<16?"0"+e.toString(16):e.toString(16)}function U(e,t){var r;t=t||1/0;for(var n=e.length,i=null,a=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&a.push(239,191,189);continue}if(o+1===n){(t-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return a}function V(e){return n.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(H,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function z(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}}).call(this,r(54))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e};t.default=function(e,t){var r=n({},(0,i.default)(e),{key:t});"string"==typeof r.style||r.style instanceof String?r.style=(0,a.default)(r.style):delete r.style;return r};var i=o(r(62)),a=o(r(65));function o(e){return e&&e.__esModule?e:{default:e}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){i.hasOwnProperty(e)||(i[e]=n.test(e));return i[e]};var n=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,i={}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return"text"===e.type&&/\r?\n/.test(e.data)&&""===e.data.trim()}},function(e,t,r){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0});var i=r(2),a=u(r(60)),o=u(r(61)),s=u(r(67)),l=u(r(68));function u(e){return e&&e.__esModule?e:{default:e}}function c(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}t.default=(c(n={},i.ElementType.Text,a.default),c(n,i.ElementType.Tag,o.default),c(n,i.ElementType.Style,s.default),c(n,i.ElementType.Directive,l.default),c(n,i.ElementType.Comment,l.default),c(n,i.ElementType.Script,l.default),c(n,i.ElementType.CDATA,l.default),c(n,i.ElementType.Doctype,l.default),n)},function(e,t,r){var n=r(30);e.exports=function(e){if(e>=55296&&e<=57343||e>1114111)return"�";e in n&&(e=n[e]);var t="";e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e);return t+=String.fromCharCode(e)}},function(e){e.exports=JSON.parse('{"0":65533,"128":8364,"130":8218,"131":402,"132":8222,"133":8230,"134":8224,"135":8225,"136":710,"137":8240,"138":352,"139":8249,"140":338,"142":381,"145":8216,"146":8217,"147":8220,"148":8221,"149":8226,"150":8211,"151":8212,"152":732,"153":8482,"154":353,"155":8250,"156":339,"158":382,"159":376}')},function(e){e.exports=JSON.parse('{"Aacute":"Á","aacute":"á","Abreve":"Ă","abreve":"ă","ac":"∾","acd":"∿","acE":"∾̳","Acirc":"Â","acirc":"â","acute":"´","Acy":"А","acy":"а","AElig":"Æ","aelig":"æ","af":"⁡","Afr":"𝔄","afr":"𝔞","Agrave":"À","agrave":"à","alefsym":"ℵ","aleph":"ℵ","Alpha":"Α","alpha":"α","Amacr":"Ā","amacr":"ā","amalg":"⨿","amp":"&","AMP":"&","andand":"⩕","And":"⩓","and":"∧","andd":"⩜","andslope":"⩘","andv":"⩚","ang":"∠","ange":"⦤","angle":"∠","angmsdaa":"⦨","angmsdab":"⦩","angmsdac":"⦪","angmsdad":"⦫","angmsdae":"⦬","angmsdaf":"⦭","angmsdag":"⦮","angmsdah":"⦯","angmsd":"∡","angrt":"∟","angrtvb":"⊾","angrtvbd":"⦝","angsph":"∢","angst":"Å","angzarr":"⍼","Aogon":"Ą","aogon":"ą","Aopf":"𝔸","aopf":"𝕒","apacir":"⩯","ap":"≈","apE":"⩰","ape":"≊","apid":"≋","apos":"\'","ApplyFunction":"⁡","approx":"≈","approxeq":"≊","Aring":"Å","aring":"å","Ascr":"𝒜","ascr":"𝒶","Assign":"≔","ast":"*","asymp":"≈","asympeq":"≍","Atilde":"Ã","atilde":"ã","Auml":"Ä","auml":"ä","awconint":"∳","awint":"⨑","backcong":"≌","backepsilon":"϶","backprime":"‵","backsim":"∽","backsimeq":"⋍","Backslash":"∖","Barv":"⫧","barvee":"⊽","barwed":"⌅","Barwed":"⌆","barwedge":"⌅","bbrk":"⎵","bbrktbrk":"⎶","bcong":"≌","Bcy":"Б","bcy":"б","bdquo":"„","becaus":"∵","because":"∵","Because":"∵","bemptyv":"⦰","bepsi":"϶","bernou":"ℬ","Bernoullis":"ℬ","Beta":"Β","beta":"β","beth":"ℶ","between":"≬","Bfr":"𝔅","bfr":"𝔟","bigcap":"⋂","bigcirc":"◯","bigcup":"⋃","bigodot":"⨀","bigoplus":"⨁","bigotimes":"⨂","bigsqcup":"⨆","bigstar":"★","bigtriangledown":"▽","bigtriangleup":"△","biguplus":"⨄","bigvee":"⋁","bigwedge":"⋀","bkarow":"⤍","blacklozenge":"⧫","blacksquare":"▪","blacktriangle":"▴","blacktriangledown":"▾","blacktriangleleft":"◂","blacktriangleright":"▸","blank":"␣","blk12":"▒","blk14":"░","blk34":"▓","block":"█","bne":"=⃥","bnequiv":"≡⃥","bNot":"⫭","bnot":"⌐","Bopf":"𝔹","bopf":"𝕓","bot":"⊥","bottom":"⊥","bowtie":"⋈","boxbox":"⧉","boxdl":"┐","boxdL":"╕","boxDl":"╖","boxDL":"╗","boxdr":"┌","boxdR":"╒","boxDr":"╓","boxDR":"╔","boxh":"─","boxH":"═","boxhd":"┬","boxHd":"╤","boxhD":"╥","boxHD":"╦","boxhu":"┴","boxHu":"╧","boxhU":"╨","boxHU":"╩","boxminus":"⊟","boxplus":"⊞","boxtimes":"⊠","boxul":"┘","boxuL":"╛","boxUl":"╜","boxUL":"╝","boxur":"└","boxuR":"╘","boxUr":"╙","boxUR":"╚","boxv":"│","boxV":"║","boxvh":"┼","boxvH":"╪","boxVh":"╫","boxVH":"╬","boxvl":"┤","boxvL":"╡","boxVl":"╢","boxVL":"╣","boxvr":"├","boxvR":"╞","boxVr":"╟","boxVR":"╠","bprime":"‵","breve":"˘","Breve":"˘","brvbar":"¦","bscr":"𝒷","Bscr":"ℬ","bsemi":"⁏","bsim":"∽","bsime":"⋍","bsolb":"⧅","bsol":"\\\\","bsolhsub":"⟈","bull":"•","bullet":"•","bump":"≎","bumpE":"⪮","bumpe":"≏","Bumpeq":"≎","bumpeq":"≏","Cacute":"Ć","cacute":"ć","capand":"⩄","capbrcup":"⩉","capcap":"⩋","cap":"∩","Cap":"⋒","capcup":"⩇","capdot":"⩀","CapitalDifferentialD":"ⅅ","caps":"∩︀","caret":"⁁","caron":"ˇ","Cayleys":"ℭ","ccaps":"⩍","Ccaron":"Č","ccaron":"č","Ccedil":"Ç","ccedil":"ç","Ccirc":"Ĉ","ccirc":"ĉ","Cconint":"∰","ccups":"⩌","ccupssm":"⩐","Cdot":"Ċ","cdot":"ċ","cedil":"¸","Cedilla":"¸","cemptyv":"⦲","cent":"¢","centerdot":"·","CenterDot":"·","cfr":"𝔠","Cfr":"ℭ","CHcy":"Ч","chcy":"ч","check":"✓","checkmark":"✓","Chi":"Χ","chi":"χ","circ":"ˆ","circeq":"≗","circlearrowleft":"↺","circlearrowright":"↻","circledast":"⊛","circledcirc":"⊚","circleddash":"⊝","CircleDot":"⊙","circledR":"®","circledS":"Ⓢ","CircleMinus":"⊖","CirclePlus":"⊕","CircleTimes":"⊗","cir":"○","cirE":"⧃","cire":"≗","cirfnint":"⨐","cirmid":"⫯","cirscir":"⧂","ClockwiseContourIntegral":"∲","CloseCurlyDoubleQuote":"”","CloseCurlyQuote":"’","clubs":"♣","clubsuit":"♣","colon":":","Colon":"∷","Colone":"⩴","colone":"≔","coloneq":"≔","comma":",","commat":"@","comp":"∁","compfn":"∘","complement":"∁","complexes":"ℂ","cong":"≅","congdot":"⩭","Congruent":"≡","conint":"∮","Conint":"∯","ContourIntegral":"∮","copf":"𝕔","Copf":"ℂ","coprod":"∐","Coproduct":"∐","copy":"©","COPY":"©","copysr":"℗","CounterClockwiseContourIntegral":"∳","crarr":"↵","cross":"✗","Cross":"⨯","Cscr":"𝒞","cscr":"𝒸","csub":"⫏","csube":"⫑","csup":"⫐","csupe":"⫒","ctdot":"⋯","cudarrl":"⤸","cudarrr":"⤵","cuepr":"⋞","cuesc":"⋟","cularr":"↶","cularrp":"⤽","cupbrcap":"⩈","cupcap":"⩆","CupCap":"≍","cup":"∪","Cup":"⋓","cupcup":"⩊","cupdot":"⊍","cupor":"⩅","cups":"∪︀","curarr":"↷","curarrm":"⤼","curlyeqprec":"⋞","curlyeqsucc":"⋟","curlyvee":"⋎","curlywedge":"⋏","curren":"¤","curvearrowleft":"↶","curvearrowright":"↷","cuvee":"⋎","cuwed":"⋏","cwconint":"∲","cwint":"∱","cylcty":"⌭","dagger":"†","Dagger":"‡","daleth":"ℸ","darr":"↓","Darr":"↡","dArr":"⇓","dash":"‐","Dashv":"⫤","dashv":"⊣","dbkarow":"⤏","dblac":"˝","Dcaron":"Ď","dcaron":"ď","Dcy":"Д","dcy":"д","ddagger":"‡","ddarr":"⇊","DD":"ⅅ","dd":"ⅆ","DDotrahd":"⤑","ddotseq":"⩷","deg":"°","Del":"∇","Delta":"Δ","delta":"δ","demptyv":"⦱","dfisht":"⥿","Dfr":"𝔇","dfr":"𝔡","dHar":"⥥","dharl":"⇃","dharr":"⇂","DiacriticalAcute":"´","DiacriticalDot":"˙","DiacriticalDoubleAcute":"˝","DiacriticalGrave":"`","DiacriticalTilde":"˜","diam":"⋄","diamond":"⋄","Diamond":"⋄","diamondsuit":"♦","diams":"♦","die":"¨","DifferentialD":"ⅆ","digamma":"ϝ","disin":"⋲","div":"÷","divide":"÷","divideontimes":"⋇","divonx":"⋇","DJcy":"Ђ","djcy":"ђ","dlcorn":"⌞","dlcrop":"⌍","dollar":"$","Dopf":"𝔻","dopf":"𝕕","Dot":"¨","dot":"˙","DotDot":"⃜","doteq":"≐","doteqdot":"≑","DotEqual":"≐","dotminus":"∸","dotplus":"∔","dotsquare":"⊡","doublebarwedge":"⌆","DoubleContourIntegral":"∯","DoubleDot":"¨","DoubleDownArrow":"⇓","DoubleLeftArrow":"⇐","DoubleLeftRightArrow":"⇔","DoubleLeftTee":"⫤","DoubleLongLeftArrow":"⟸","DoubleLongLeftRightArrow":"⟺","DoubleLongRightArrow":"⟹","DoubleRightArrow":"⇒","DoubleRightTee":"⊨","DoubleUpArrow":"⇑","DoubleUpDownArrow":"⇕","DoubleVerticalBar":"∥","DownArrowBar":"⤓","downarrow":"↓","DownArrow":"↓","Downarrow":"⇓","DownArrowUpArrow":"⇵","DownBreve":"̑","downdownarrows":"⇊","downharpoonleft":"⇃","downharpoonright":"⇂","DownLeftRightVector":"⥐","DownLeftTeeVector":"⥞","DownLeftVectorBar":"⥖","DownLeftVector":"↽","DownRightTeeVector":"⥟","DownRightVectorBar":"⥗","DownRightVector":"⇁","DownTeeArrow":"↧","DownTee":"⊤","drbkarow":"⤐","drcorn":"⌟","drcrop":"⌌","Dscr":"𝒟","dscr":"𝒹","DScy":"Ѕ","dscy":"ѕ","dsol":"⧶","Dstrok":"Đ","dstrok":"đ","dtdot":"⋱","dtri":"▿","dtrif":"▾","duarr":"⇵","duhar":"⥯","dwangle":"⦦","DZcy":"Џ","dzcy":"џ","dzigrarr":"⟿","Eacute":"É","eacute":"é","easter":"⩮","Ecaron":"Ě","ecaron":"ě","Ecirc":"Ê","ecirc":"ê","ecir":"≖","ecolon":"≕","Ecy":"Э","ecy":"э","eDDot":"⩷","Edot":"Ė","edot":"ė","eDot":"≑","ee":"ⅇ","efDot":"≒","Efr":"𝔈","efr":"𝔢","eg":"⪚","Egrave":"È","egrave":"è","egs":"⪖","egsdot":"⪘","el":"⪙","Element":"∈","elinters":"⏧","ell":"ℓ","els":"⪕","elsdot":"⪗","Emacr":"Ē","emacr":"ē","empty":"∅","emptyset":"∅","EmptySmallSquare":"◻","emptyv":"∅","EmptyVerySmallSquare":"▫","emsp13":" ","emsp14":" ","emsp":" ","ENG":"Ŋ","eng":"ŋ","ensp":" ","Eogon":"Ę","eogon":"ę","Eopf":"𝔼","eopf":"𝕖","epar":"⋕","eparsl":"⧣","eplus":"⩱","epsi":"ε","Epsilon":"Ε","epsilon":"ε","epsiv":"ϵ","eqcirc":"≖","eqcolon":"≕","eqsim":"≂","eqslantgtr":"⪖","eqslantless":"⪕","Equal":"⩵","equals":"=","EqualTilde":"≂","equest":"≟","Equilibrium":"⇌","equiv":"≡","equivDD":"⩸","eqvparsl":"⧥","erarr":"⥱","erDot":"≓","escr":"ℯ","Escr":"ℰ","esdot":"≐","Esim":"⩳","esim":"≂","Eta":"Η","eta":"η","ETH":"Ð","eth":"ð","Euml":"Ë","euml":"ë","euro":"€","excl":"!","exist":"∃","Exists":"∃","expectation":"ℰ","exponentiale":"ⅇ","ExponentialE":"ⅇ","fallingdotseq":"≒","Fcy":"Ф","fcy":"ф","female":"♀","ffilig":"ﬃ","fflig":"ﬀ","ffllig":"ﬄ","Ffr":"𝔉","ffr":"𝔣","filig":"ﬁ","FilledSmallSquare":"◼","FilledVerySmallSquare":"▪","fjlig":"fj","flat":"♭","fllig":"ﬂ","fltns":"▱","fnof":"ƒ","Fopf":"𝔽","fopf":"𝕗","forall":"∀","ForAll":"∀","fork":"⋔","forkv":"⫙","Fouriertrf":"ℱ","fpartint":"⨍","frac12":"½","frac13":"⅓","frac14":"¼","frac15":"⅕","frac16":"⅙","frac18":"⅛","frac23":"⅔","frac25":"⅖","frac34":"¾","frac35":"⅗","frac38":"⅜","frac45":"⅘","frac56":"⅚","frac58":"⅝","frac78":"⅞","frasl":"⁄","frown":"⌢","fscr":"𝒻","Fscr":"ℱ","gacute":"ǵ","Gamma":"Γ","gamma":"γ","Gammad":"Ϝ","gammad":"ϝ","gap":"⪆","Gbreve":"Ğ","gbreve":"ğ","Gcedil":"Ģ","Gcirc":"Ĝ","gcirc":"ĝ","Gcy":"Г","gcy":"г","Gdot":"Ġ","gdot":"ġ","ge":"≥","gE":"≧","gEl":"⪌","gel":"⋛","geq":"≥","geqq":"≧","geqslant":"⩾","gescc":"⪩","ges":"⩾","gesdot":"⪀","gesdoto":"⪂","gesdotol":"⪄","gesl":"⋛︀","gesles":"⪔","Gfr":"𝔊","gfr":"𝔤","gg":"≫","Gg":"⋙","ggg":"⋙","gimel":"ℷ","GJcy":"Ѓ","gjcy":"ѓ","gla":"⪥","gl":"≷","glE":"⪒","glj":"⪤","gnap":"⪊","gnapprox":"⪊","gne":"⪈","gnE":"≩","gneq":"⪈","gneqq":"≩","gnsim":"⋧","Gopf":"𝔾","gopf":"𝕘","grave":"`","GreaterEqual":"≥","GreaterEqualLess":"⋛","GreaterFullEqual":"≧","GreaterGreater":"⪢","GreaterLess":"≷","GreaterSlantEqual":"⩾","GreaterTilde":"≳","Gscr":"𝒢","gscr":"ℊ","gsim":"≳","gsime":"⪎","gsiml":"⪐","gtcc":"⪧","gtcir":"⩺","gt":">","GT":">","Gt":"≫","gtdot":"⋗","gtlPar":"⦕","gtquest":"⩼","gtrapprox":"⪆","gtrarr":"⥸","gtrdot":"⋗","gtreqless":"⋛","gtreqqless":"⪌","gtrless":"≷","gtrsim":"≳","gvertneqq":"≩︀","gvnE":"≩︀","Hacek":"ˇ","hairsp":" ","half":"½","hamilt":"ℋ","HARDcy":"Ъ","hardcy":"ъ","harrcir":"⥈","harr":"↔","hArr":"⇔","harrw":"↭","Hat":"^","hbar":"ℏ","Hcirc":"Ĥ","hcirc":"ĥ","hearts":"♥","heartsuit":"♥","hellip":"…","hercon":"⊹","hfr":"𝔥","Hfr":"ℌ","HilbertSpace":"ℋ","hksearow":"⤥","hkswarow":"⤦","hoarr":"⇿","homtht":"∻","hookleftarrow":"↩","hookrightarrow":"↪","hopf":"𝕙","Hopf":"ℍ","horbar":"―","HorizontalLine":"─","hscr":"𝒽","Hscr":"ℋ","hslash":"ℏ","Hstrok":"Ħ","hstrok":"ħ","HumpDownHump":"≎","HumpEqual":"≏","hybull":"⁃","hyphen":"‐","Iacute":"Í","iacute":"í","ic":"⁣","Icirc":"Î","icirc":"î","Icy":"И","icy":"и","Idot":"İ","IEcy":"Е","iecy":"е","iexcl":"¡","iff":"⇔","ifr":"𝔦","Ifr":"ℑ","Igrave":"Ì","igrave":"ì","ii":"ⅈ","iiiint":"⨌","iiint":"∭","iinfin":"⧜","iiota":"℩","IJlig":"Ĳ","ijlig":"ĳ","Imacr":"Ī","imacr":"ī","image":"ℑ","ImaginaryI":"ⅈ","imagline":"ℐ","imagpart":"ℑ","imath":"ı","Im":"ℑ","imof":"⊷","imped":"Ƶ","Implies":"⇒","incare":"℅","in":"∈","infin":"∞","infintie":"⧝","inodot":"ı","intcal":"⊺","int":"∫","Int":"∬","integers":"ℤ","Integral":"∫","intercal":"⊺","Intersection":"⋂","intlarhk":"⨗","intprod":"⨼","InvisibleComma":"⁣","InvisibleTimes":"⁢","IOcy":"Ё","iocy":"ё","Iogon":"Į","iogon":"į","Iopf":"𝕀","iopf":"𝕚","Iota":"Ι","iota":"ι","iprod":"⨼","iquest":"¿","iscr":"𝒾","Iscr":"ℐ","isin":"∈","isindot":"⋵","isinE":"⋹","isins":"⋴","isinsv":"⋳","isinv":"∈","it":"⁢","Itilde":"Ĩ","itilde":"ĩ","Iukcy":"І","iukcy":"і","Iuml":"Ï","iuml":"ï","Jcirc":"Ĵ","jcirc":"ĵ","Jcy":"Й","jcy":"й","Jfr":"𝔍","jfr":"𝔧","jmath":"ȷ","Jopf":"𝕁","jopf":"𝕛","Jscr":"𝒥","jscr":"𝒿","Jsercy":"Ј","jsercy":"ј","Jukcy":"Є","jukcy":"є","Kappa":"Κ","kappa":"κ","kappav":"ϰ","Kcedil":"Ķ","kcedil":"ķ","Kcy":"К","kcy":"к","Kfr":"𝔎","kfr":"𝔨","kgreen":"ĸ","KHcy":"Х","khcy":"х","KJcy":"Ќ","kjcy":"ќ","Kopf":"𝕂","kopf":"𝕜","Kscr":"𝒦","kscr":"𝓀","lAarr":"⇚","Lacute":"Ĺ","lacute":"ĺ","laemptyv":"⦴","lagran":"ℒ","Lambda":"Λ","lambda":"λ","lang":"⟨","Lang":"⟪","langd":"⦑","langle":"⟨","lap":"⪅","Laplacetrf":"ℒ","laquo":"«","larrb":"⇤","larrbfs":"⤟","larr":"←","Larr":"↞","lArr":"⇐","larrfs":"⤝","larrhk":"↩","larrlp":"↫","larrpl":"⤹","larrsim":"⥳","larrtl":"↢","latail":"⤙","lAtail":"⤛","lat":"⪫","late":"⪭","lates":"⪭︀","lbarr":"⤌","lBarr":"⤎","lbbrk":"❲","lbrace":"{","lbrack":"[","lbrke":"⦋","lbrksld":"⦏","lbrkslu":"⦍","Lcaron":"Ľ","lcaron":"ľ","Lcedil":"Ļ","lcedil":"ļ","lceil":"⌈","lcub":"{","Lcy":"Л","lcy":"л","ldca":"⤶","ldquo":"“","ldquor":"„","ldrdhar":"⥧","ldrushar":"⥋","ldsh":"↲","le":"≤","lE":"≦","LeftAngleBracket":"⟨","LeftArrowBar":"⇤","leftarrow":"←","LeftArrow":"←","Leftarrow":"⇐","LeftArrowRightArrow":"⇆","leftarrowtail":"↢","LeftCeiling":"⌈","LeftDoubleBracket":"⟦","LeftDownTeeVector":"⥡","LeftDownVectorBar":"⥙","LeftDownVector":"⇃","LeftFloor":"⌊","leftharpoondown":"↽","leftharpoonup":"↼","leftleftarrows":"⇇","leftrightarrow":"↔","LeftRightArrow":"↔","Leftrightarrow":"⇔","leftrightarrows":"⇆","leftrightharpoons":"⇋","leftrightsquigarrow":"↭","LeftRightVector":"⥎","LeftTeeArrow":"↤","LeftTee":"⊣","LeftTeeVector":"⥚","leftthreetimes":"⋋","LeftTriangleBar":"⧏","LeftTriangle":"⊲","LeftTriangleEqual":"⊴","LeftUpDownVector":"⥑","LeftUpTeeVector":"⥠","LeftUpVectorBar":"⥘","LeftUpVector":"↿","LeftVectorBar":"⥒","LeftVector":"↼","lEg":"⪋","leg":"⋚","leq":"≤","leqq":"≦","leqslant":"⩽","lescc":"⪨","les":"⩽","lesdot":"⩿","lesdoto":"⪁","lesdotor":"⪃","lesg":"⋚︀","lesges":"⪓","lessapprox":"⪅","lessdot":"⋖","lesseqgtr":"⋚","lesseqqgtr":"⪋","LessEqualGreater":"⋚","LessFullEqual":"≦","LessGreater":"≶","lessgtr":"≶","LessLess":"⪡","lesssim":"≲","LessSlantEqual":"⩽","LessTilde":"≲","lfisht":"⥼","lfloor":"⌊","Lfr":"𝔏","lfr":"𝔩","lg":"≶","lgE":"⪑","lHar":"⥢","lhard":"↽","lharu":"↼","lharul":"⥪","lhblk":"▄","LJcy":"Љ","ljcy":"љ","llarr":"⇇","ll":"≪","Ll":"⋘","llcorner":"⌞","Lleftarrow":"⇚","llhard":"⥫","lltri":"◺","Lmidot":"Ŀ","lmidot":"ŀ","lmoustache":"⎰","lmoust":"⎰","lnap":"⪉","lnapprox":"⪉","lne":"⪇","lnE":"≨","lneq":"⪇","lneqq":"≨","lnsim":"⋦","loang":"⟬","loarr":"⇽","lobrk":"⟦","longleftarrow":"⟵","LongLeftArrow":"⟵","Longleftarrow":"⟸","longleftrightarrow":"⟷","LongLeftRightArrow":"⟷","Longleftrightarrow":"⟺","longmapsto":"⟼","longrightarrow":"⟶","LongRightArrow":"⟶","Longrightarrow":"⟹","looparrowleft":"↫","looparrowright":"↬","lopar":"⦅","Lopf":"𝕃","lopf":"𝕝","loplus":"⨭","lotimes":"⨴","lowast":"∗","lowbar":"_","LowerLeftArrow":"↙","LowerRightArrow":"↘","loz":"◊","lozenge":"◊","lozf":"⧫","lpar":"(","lparlt":"⦓","lrarr":"⇆","lrcorner":"⌟","lrhar":"⇋","lrhard":"⥭","lrm":"‎","lrtri":"⊿","lsaquo":"‹","lscr":"𝓁","Lscr":"ℒ","lsh":"↰","Lsh":"↰","lsim":"≲","lsime":"⪍","lsimg":"⪏","lsqb":"[","lsquo":"‘","lsquor":"‚","Lstrok":"Ł","lstrok":"ł","ltcc":"⪦","ltcir":"⩹","lt":"<","LT":"<","Lt":"≪","ltdot":"⋖","lthree":"⋋","ltimes":"⋉","ltlarr":"⥶","ltquest":"⩻","ltri":"◃","ltrie":"⊴","ltrif":"◂","ltrPar":"⦖","lurdshar":"⥊","luruhar":"⥦","lvertneqq":"≨︀","lvnE":"≨︀","macr":"¯","male":"♂","malt":"✠","maltese":"✠","Map":"⤅","map":"↦","mapsto":"↦","mapstodown":"↧","mapstoleft":"↤","mapstoup":"↥","marker":"▮","mcomma":"⨩","Mcy":"М","mcy":"м","mdash":"—","mDDot":"∺","measuredangle":"∡","MediumSpace":" ","Mellintrf":"ℳ","Mfr":"𝔐","mfr":"𝔪","mho":"℧","micro":"µ","midast":"*","midcir":"⫰","mid":"∣","middot":"·","minusb":"⊟","minus":"−","minusd":"∸","minusdu":"⨪","MinusPlus":"∓","mlcp":"⫛","mldr":"…","mnplus":"∓","models":"⊧","Mopf":"𝕄","mopf":"𝕞","mp":"∓","mscr":"𝓂","Mscr":"ℳ","mstpos":"∾","Mu":"Μ","mu":"μ","multimap":"⊸","mumap":"⊸","nabla":"∇","Nacute":"Ń","nacute":"ń","nang":"∠⃒","nap":"≉","napE":"⩰̸","napid":"≋̸","napos":"ŉ","napprox":"≉","natural":"♮","naturals":"ℕ","natur":"♮","nbsp":" ","nbump":"≎̸","nbumpe":"≏̸","ncap":"⩃","Ncaron":"Ň","ncaron":"ň","Ncedil":"Ņ","ncedil":"ņ","ncong":"≇","ncongdot":"⩭̸","ncup":"⩂","Ncy":"Н","ncy":"н","ndash":"–","nearhk":"⤤","nearr":"↗","neArr":"⇗","nearrow":"↗","ne":"≠","nedot":"≐̸","NegativeMediumSpace":"​","NegativeThickSpace":"​","NegativeThinSpace":"​","NegativeVeryThinSpace":"​","nequiv":"≢","nesear":"⤨","nesim":"≂̸","NestedGreaterGreater":"≫","NestedLessLess":"≪","NewLine":"\\n","nexist":"∄","nexists":"∄","Nfr":"𝔑","nfr":"𝔫","ngE":"≧̸","nge":"≱","ngeq":"≱","ngeqq":"≧̸","ngeqslant":"⩾̸","nges":"⩾̸","nGg":"⋙̸","ngsim":"≵","nGt":"≫⃒","ngt":"≯","ngtr":"≯","nGtv":"≫̸","nharr":"↮","nhArr":"⇎","nhpar":"⫲","ni":"∋","nis":"⋼","nisd":"⋺","niv":"∋","NJcy":"Њ","njcy":"њ","nlarr":"↚","nlArr":"⇍","nldr":"‥","nlE":"≦̸","nle":"≰","nleftarrow":"↚","nLeftarrow":"⇍","nleftrightarrow":"↮","nLeftrightarrow":"⇎","nleq":"≰","nleqq":"≦̸","nleqslant":"⩽̸","nles":"⩽̸","nless":"≮","nLl":"⋘̸","nlsim":"≴","nLt":"≪⃒","nlt":"≮","nltri":"⋪","nltrie":"⋬","nLtv":"≪̸","nmid":"∤","NoBreak":"⁠","NonBreakingSpace":" ","nopf":"𝕟","Nopf":"ℕ","Not":"⫬","not":"¬","NotCongruent":"≢","NotCupCap":"≭","NotDoubleVerticalBar":"∦","NotElement":"∉","NotEqual":"≠","NotEqualTilde":"≂̸","NotExists":"∄","NotGreater":"≯","NotGreaterEqual":"≱","NotGreaterFullEqual":"≧̸","NotGreaterGreater":"≫̸","NotGreaterLess":"≹","NotGreaterSlantEqual":"⩾̸","NotGreaterTilde":"≵","NotHumpDownHump":"≎̸","NotHumpEqual":"≏̸","notin":"∉","notindot":"⋵̸","notinE":"⋹̸","notinva":"∉","notinvb":"⋷","notinvc":"⋶","NotLeftTriangleBar":"⧏̸","NotLeftTriangle":"⋪","NotLeftTriangleEqual":"⋬","NotLess":"≮","NotLessEqual":"≰","NotLessGreater":"≸","NotLessLess":"≪̸","NotLessSlantEqual":"⩽̸","NotLessTilde":"≴","NotNestedGreaterGreater":"⪢̸","NotNestedLessLess":"⪡̸","notni":"∌","notniva":"∌","notnivb":"⋾","notnivc":"⋽","NotPrecedes":"⊀","NotPrecedesEqual":"⪯̸","NotPrecedesSlantEqual":"⋠","NotReverseElement":"∌","NotRightTriangleBar":"⧐̸","NotRightTriangle":"⋫","NotRightTriangleEqual":"⋭","NotSquareSubset":"⊏̸","NotSquareSubsetEqual":"⋢","NotSquareSuperset":"⊐̸","NotSquareSupersetEqual":"⋣","NotSubset":"⊂⃒","NotSubsetEqual":"⊈","NotSucceeds":"⊁","NotSucceedsEqual":"⪰̸","NotSucceedsSlantEqual":"⋡","NotSucceedsTilde":"≿̸","NotSuperset":"⊃⃒","NotSupersetEqual":"⊉","NotTilde":"≁","NotTildeEqual":"≄","NotTildeFullEqual":"≇","NotTildeTilde":"≉","NotVerticalBar":"∤","nparallel":"∦","npar":"∦","nparsl":"⫽⃥","npart":"∂̸","npolint":"⨔","npr":"⊀","nprcue":"⋠","nprec":"⊀","npreceq":"⪯̸","npre":"⪯̸","nrarrc":"⤳̸","nrarr":"↛","nrArr":"⇏","nrarrw":"↝̸","nrightarrow":"↛","nRightarrow":"⇏","nrtri":"⋫","nrtrie":"⋭","nsc":"⊁","nsccue":"⋡","nsce":"⪰̸","Nscr":"𝒩","nscr":"𝓃","nshortmid":"∤","nshortparallel":"∦","nsim":"≁","nsime":"≄","nsimeq":"≄","nsmid":"∤","nspar":"∦","nsqsube":"⋢","nsqsupe":"⋣","nsub":"⊄","nsubE":"⫅̸","nsube":"⊈","nsubset":"⊂⃒","nsubseteq":"⊈","nsubseteqq":"⫅̸","nsucc":"⊁","nsucceq":"⪰̸","nsup":"⊅","nsupE":"⫆̸","nsupe":"⊉","nsupset":"⊃⃒","nsupseteq":"⊉","nsupseteqq":"⫆̸","ntgl":"≹","Ntilde":"Ñ","ntilde":"ñ","ntlg":"≸","ntriangleleft":"⋪","ntrianglelefteq":"⋬","ntriangleright":"⋫","ntrianglerighteq":"⋭","Nu":"Ν","nu":"ν","num":"#","numero":"№","numsp":" ","nvap":"≍⃒","nvdash":"⊬","nvDash":"⊭","nVdash":"⊮","nVDash":"⊯","nvge":"≥⃒","nvgt":">⃒","nvHarr":"⤄","nvinfin":"⧞","nvlArr":"⤂","nvle":"≤⃒","nvlt":"<⃒","nvltrie":"⊴⃒","nvrArr":"⤃","nvrtrie":"⊵⃒","nvsim":"∼⃒","nwarhk":"⤣","nwarr":"↖","nwArr":"⇖","nwarrow":"↖","nwnear":"⤧","Oacute":"Ó","oacute":"ó","oast":"⊛","Ocirc":"Ô","ocirc":"ô","ocir":"⊚","Ocy":"О","ocy":"о","odash":"⊝","Odblac":"Ő","odblac":"ő","odiv":"⨸","odot":"⊙","odsold":"⦼","OElig":"Œ","oelig":"œ","ofcir":"⦿","Ofr":"𝔒","ofr":"𝔬","ogon":"˛","Ograve":"Ò","ograve":"ò","ogt":"⧁","ohbar":"⦵","ohm":"Ω","oint":"∮","olarr":"↺","olcir":"⦾","olcross":"⦻","oline":"‾","olt":"⧀","Omacr":"Ō","omacr":"ō","Omega":"Ω","omega":"ω","Omicron":"Ο","omicron":"ο","omid":"⦶","ominus":"⊖","Oopf":"𝕆","oopf":"𝕠","opar":"⦷","OpenCurlyDoubleQuote":"“","OpenCurlyQuote":"‘","operp":"⦹","oplus":"⊕","orarr":"↻","Or":"⩔","or":"∨","ord":"⩝","order":"ℴ","orderof":"ℴ","ordf":"ª","ordm":"º","origof":"⊶","oror":"⩖","orslope":"⩗","orv":"⩛","oS":"Ⓢ","Oscr":"𝒪","oscr":"ℴ","Oslash":"Ø","oslash":"ø","osol":"⊘","Otilde":"Õ","otilde":"õ","otimesas":"⨶","Otimes":"⨷","otimes":"⊗","Ouml":"Ö","ouml":"ö","ovbar":"⌽","OverBar":"‾","OverBrace":"⏞","OverBracket":"⎴","OverParenthesis":"⏜","para":"¶","parallel":"∥","par":"∥","parsim":"⫳","parsl":"⫽","part":"∂","PartialD":"∂","Pcy":"П","pcy":"п","percnt":"%","period":".","permil":"‰","perp":"⊥","pertenk":"‱","Pfr":"𝔓","pfr":"𝔭","Phi":"Φ","phi":"φ","phiv":"ϕ","phmmat":"ℳ","phone":"☎","Pi":"Π","pi":"π","pitchfork":"⋔","piv":"ϖ","planck":"ℏ","planckh":"ℎ","plankv":"ℏ","plusacir":"⨣","plusb":"⊞","pluscir":"⨢","plus":"+","plusdo":"∔","plusdu":"⨥","pluse":"⩲","PlusMinus":"±","plusmn":"±","plussim":"⨦","plustwo":"⨧","pm":"±","Poincareplane":"ℌ","pointint":"⨕","popf":"𝕡","Popf":"ℙ","pound":"£","prap":"⪷","Pr":"⪻","pr":"≺","prcue":"≼","precapprox":"⪷","prec":"≺","preccurlyeq":"≼","Precedes":"≺","PrecedesEqual":"⪯","PrecedesSlantEqual":"≼","PrecedesTilde":"≾","preceq":"⪯","precnapprox":"⪹","precneqq":"⪵","precnsim":"⋨","pre":"⪯","prE":"⪳","precsim":"≾","prime":"′","Prime":"″","primes":"ℙ","prnap":"⪹","prnE":"⪵","prnsim":"⋨","prod":"∏","Product":"∏","profalar":"⌮","profline":"⌒","profsurf":"⌓","prop":"∝","Proportional":"∝","Proportion":"∷","propto":"∝","prsim":"≾","prurel":"⊰","Pscr":"𝒫","pscr":"𝓅","Psi":"Ψ","psi":"ψ","puncsp":" ","Qfr":"𝔔","qfr":"𝔮","qint":"⨌","qopf":"𝕢","Qopf":"ℚ","qprime":"⁗","Qscr":"𝒬","qscr":"𝓆","quaternions":"ℍ","quatint":"⨖","quest":"?","questeq":"≟","quot":"\\"","QUOT":"\\"","rAarr":"⇛","race":"∽̱","Racute":"Ŕ","racute":"ŕ","radic":"√","raemptyv":"⦳","rang":"⟩","Rang":"⟫","rangd":"⦒","range":"⦥","rangle":"⟩","raquo":"»","rarrap":"⥵","rarrb":"⇥","rarrbfs":"⤠","rarrc":"⤳","rarr":"→","Rarr":"↠","rArr":"⇒","rarrfs":"⤞","rarrhk":"↪","rarrlp":"↬","rarrpl":"⥅","rarrsim":"⥴","Rarrtl":"⤖","rarrtl":"↣","rarrw":"↝","ratail":"⤚","rAtail":"⤜","ratio":"∶","rationals":"ℚ","rbarr":"⤍","rBarr":"⤏","RBarr":"⤐","rbbrk":"❳","rbrace":"}","rbrack":"]","rbrke":"⦌","rbrksld":"⦎","rbrkslu":"⦐","Rcaron":"Ř","rcaron":"ř","Rcedil":"Ŗ","rcedil":"ŗ","rceil":"⌉","rcub":"}","Rcy":"Р","rcy":"р","rdca":"⤷","rdldhar":"⥩","rdquo":"”","rdquor":"”","rdsh":"↳","real":"ℜ","realine":"ℛ","realpart":"ℜ","reals":"ℝ","Re":"ℜ","rect":"▭","reg":"®","REG":"®","ReverseElement":"∋","ReverseEquilibrium":"⇋","ReverseUpEquilibrium":"⥯","rfisht":"⥽","rfloor":"⌋","rfr":"𝔯","Rfr":"ℜ","rHar":"⥤","rhard":"⇁","rharu":"⇀","rharul":"⥬","Rho":"Ρ","rho":"ρ","rhov":"ϱ","RightAngleBracket":"⟩","RightArrowBar":"⇥","rightarrow":"→","RightArrow":"→","Rightarrow":"⇒","RightArrowLeftArrow":"⇄","rightarrowtail":"↣","RightCeiling":"⌉","RightDoubleBracket":"⟧","RightDownTeeVector":"⥝","RightDownVectorBar":"⥕","RightDownVector":"⇂","RightFloor":"⌋","rightharpoondown":"⇁","rightharpoonup":"⇀","rightleftarrows":"⇄","rightleftharpoons":"⇌","rightrightarrows":"⇉","rightsquigarrow":"↝","RightTeeArrow":"↦","RightTee":"⊢","RightTeeVector":"⥛","rightthreetimes":"⋌","RightTriangleBar":"⧐","RightTriangle":"⊳","RightTriangleEqual":"⊵","RightUpDownVector":"⥏","RightUpTeeVector":"⥜","RightUpVectorBar":"⥔","RightUpVector":"↾","RightVectorBar":"⥓","RightVector":"⇀","ring":"˚","risingdotseq":"≓","rlarr":"⇄","rlhar":"⇌","rlm":"‏","rmoustache":"⎱","rmoust":"⎱","rnmid":"⫮","roang":"⟭","roarr":"⇾","robrk":"⟧","ropar":"⦆","ropf":"𝕣","Ropf":"ℝ","roplus":"⨮","rotimes":"⨵","RoundImplies":"⥰","rpar":")","rpargt":"⦔","rppolint":"⨒","rrarr":"⇉","Rrightarrow":"⇛","rsaquo":"›","rscr":"𝓇","Rscr":"ℛ","rsh":"↱","Rsh":"↱","rsqb":"]","rsquo":"’","rsquor":"’","rthree":"⋌","rtimes":"⋊","rtri":"▹","rtrie":"⊵","rtrif":"▸","rtriltri":"⧎","RuleDelayed":"⧴","ruluhar":"⥨","rx":"℞","Sacute":"Ś","sacute":"ś","sbquo":"‚","scap":"⪸","Scaron":"Š","scaron":"š","Sc":"⪼","sc":"≻","sccue":"≽","sce":"⪰","scE":"⪴","Scedil":"Ş","scedil":"ş","Scirc":"Ŝ","scirc":"ŝ","scnap":"⪺","scnE":"⪶","scnsim":"⋩","scpolint":"⨓","scsim":"≿","Scy":"С","scy":"с","sdotb":"⊡","sdot":"⋅","sdote":"⩦","searhk":"⤥","searr":"↘","seArr":"⇘","searrow":"↘","sect":"§","semi":";","seswar":"⤩","setminus":"∖","setmn":"∖","sext":"✶","Sfr":"𝔖","sfr":"𝔰","sfrown":"⌢","sharp":"♯","SHCHcy":"Щ","shchcy":"щ","SHcy":"Ш","shcy":"ш","ShortDownArrow":"↓","ShortLeftArrow":"←","shortmid":"∣","shortparallel":"∥","ShortRightArrow":"→","ShortUpArrow":"↑","shy":"­","Sigma":"Σ","sigma":"σ","sigmaf":"ς","sigmav":"ς","sim":"∼","simdot":"⩪","sime":"≃","simeq":"≃","simg":"⪞","simgE":"⪠","siml":"⪝","simlE":"⪟","simne":"≆","simplus":"⨤","simrarr":"⥲","slarr":"←","SmallCircle":"∘","smallsetminus":"∖","smashp":"⨳","smeparsl":"⧤","smid":"∣","smile":"⌣","smt":"⪪","smte":"⪬","smtes":"⪬︀","SOFTcy":"Ь","softcy":"ь","solbar":"⌿","solb":"⧄","sol":"/","Sopf":"𝕊","sopf":"𝕤","spades":"♠","spadesuit":"♠","spar":"∥","sqcap":"⊓","sqcaps":"⊓︀","sqcup":"⊔","sqcups":"⊔︀","Sqrt":"√","sqsub":"⊏","sqsube":"⊑","sqsubset":"⊏","sqsubseteq":"⊑","sqsup":"⊐","sqsupe":"⊒","sqsupset":"⊐","sqsupseteq":"⊒","square":"□","Square":"□","SquareIntersection":"⊓","SquareSubset":"⊏","SquareSubsetEqual":"⊑","SquareSuperset":"⊐","SquareSupersetEqual":"⊒","SquareUnion":"⊔","squarf":"▪","squ":"□","squf":"▪","srarr":"→","Sscr":"𝒮","sscr":"𝓈","ssetmn":"∖","ssmile":"⌣","sstarf":"⋆","Star":"⋆","star":"☆","starf":"★","straightepsilon":"ϵ","straightphi":"ϕ","strns":"¯","sub":"⊂","Sub":"⋐","subdot":"⪽","subE":"⫅","sube":"⊆","subedot":"⫃","submult":"⫁","subnE":"⫋","subne":"⊊","subplus":"⪿","subrarr":"⥹","subset":"⊂","Subset":"⋐","subseteq":"⊆","subseteqq":"⫅","SubsetEqual":"⊆","subsetneq":"⊊","subsetneqq":"⫋","subsim":"⫇","subsub":"⫕","subsup":"⫓","succapprox":"⪸","succ":"≻","succcurlyeq":"≽","Succeeds":"≻","SucceedsEqual":"⪰","SucceedsSlantEqual":"≽","SucceedsTilde":"≿","succeq":"⪰","succnapprox":"⪺","succneqq":"⪶","succnsim":"⋩","succsim":"≿","SuchThat":"∋","sum":"∑","Sum":"∑","sung":"♪","sup1":"¹","sup2":"²","sup3":"³","sup":"⊃","Sup":"⋑","supdot":"⪾","supdsub":"⫘","supE":"⫆","supe":"⊇","supedot":"⫄","Superset":"⊃","SupersetEqual":"⊇","suphsol":"⟉","suphsub":"⫗","suplarr":"⥻","supmult":"⫂","supnE":"⫌","supne":"⊋","supplus":"⫀","supset":"⊃","Supset":"⋑","supseteq":"⊇","supseteqq":"⫆","supsetneq":"⊋","supsetneqq":"⫌","supsim":"⫈","supsub":"⫔","supsup":"⫖","swarhk":"⤦","swarr":"↙","swArr":"⇙","swarrow":"↙","swnwar":"⤪","szlig":"ß","Tab":"\\t","target":"⌖","Tau":"Τ","tau":"τ","tbrk":"⎴","Tcaron":"Ť","tcaron":"ť","Tcedil":"Ţ","tcedil":"ţ","Tcy":"Т","tcy":"т","tdot":"⃛","telrec":"⌕","Tfr":"𝔗","tfr":"𝔱","there4":"∴","therefore":"∴","Therefore":"∴","Theta":"Θ","theta":"θ","thetasym":"ϑ","thetav":"ϑ","thickapprox":"≈","thicksim":"∼","ThickSpace":"  ","ThinSpace":" ","thinsp":" ","thkap":"≈","thksim":"∼","THORN":"Þ","thorn":"þ","tilde":"˜","Tilde":"∼","TildeEqual":"≃","TildeFullEqual":"≅","TildeTilde":"≈","timesbar":"⨱","timesb":"⊠","times":"×","timesd":"⨰","tint":"∭","toea":"⤨","topbot":"⌶","topcir":"⫱","top":"⊤","Topf":"𝕋","topf":"𝕥","topfork":"⫚","tosa":"⤩","tprime":"‴","trade":"™","TRADE":"™","triangle":"▵","triangledown":"▿","triangleleft":"◃","trianglelefteq":"⊴","triangleq":"≜","triangleright":"▹","trianglerighteq":"⊵","tridot":"◬","trie":"≜","triminus":"⨺","TripleDot":"⃛","triplus":"⨹","trisb":"⧍","tritime":"⨻","trpezium":"⏢","Tscr":"𝒯","tscr":"𝓉","TScy":"Ц","tscy":"ц","TSHcy":"Ћ","tshcy":"ћ","Tstrok":"Ŧ","tstrok":"ŧ","twixt":"≬","twoheadleftarrow":"↞","twoheadrightarrow":"↠","Uacute":"Ú","uacute":"ú","uarr":"↑","Uarr":"↟","uArr":"⇑","Uarrocir":"⥉","Ubrcy":"Ў","ubrcy":"ў","Ubreve":"Ŭ","ubreve":"ŭ","Ucirc":"Û","ucirc":"û","Ucy":"У","ucy":"у","udarr":"⇅","Udblac":"Ű","udblac":"ű","udhar":"⥮","ufisht":"⥾","Ufr":"𝔘","ufr":"𝔲","Ugrave":"Ù","ugrave":"ù","uHar":"⥣","uharl":"↿","uharr":"↾","uhblk":"▀","ulcorn":"⌜","ulcorner":"⌜","ulcrop":"⌏","ultri":"◸","Umacr":"Ū","umacr":"ū","uml":"¨","UnderBar":"_","UnderBrace":"⏟","UnderBracket":"⎵","UnderParenthesis":"⏝","Union":"⋃","UnionPlus":"⊎","Uogon":"Ų","uogon":"ų","Uopf":"𝕌","uopf":"𝕦","UpArrowBar":"⤒","uparrow":"↑","UpArrow":"↑","Uparrow":"⇑","UpArrowDownArrow":"⇅","updownarrow":"↕","UpDownArrow":"↕","Updownarrow":"⇕","UpEquilibrium":"⥮","upharpoonleft":"↿","upharpoonright":"↾","uplus":"⊎","UpperLeftArrow":"↖","UpperRightArrow":"↗","upsi":"υ","Upsi":"ϒ","upsih":"ϒ","Upsilon":"Υ","upsilon":"υ","UpTeeArrow":"↥","UpTee":"⊥","upuparrows":"⇈","urcorn":"⌝","urcorner":"⌝","urcrop":"⌎","Uring":"Ů","uring":"ů","urtri":"◹","Uscr":"𝒰","uscr":"𝓊","utdot":"⋰","Utilde":"Ũ","utilde":"ũ","utri":"▵","utrif":"▴","uuarr":"⇈","Uuml":"Ü","uuml":"ü","uwangle":"⦧","vangrt":"⦜","varepsilon":"ϵ","varkappa":"ϰ","varnothing":"∅","varphi":"ϕ","varpi":"ϖ","varpropto":"∝","varr":"↕","vArr":"⇕","varrho":"ϱ","varsigma":"ς","varsubsetneq":"⊊︀","varsubsetneqq":"⫋︀","varsupsetneq":"⊋︀","varsupsetneqq":"⫌︀","vartheta":"ϑ","vartriangleleft":"⊲","vartriangleright":"⊳","vBar":"⫨","Vbar":"⫫","vBarv":"⫩","Vcy":"В","vcy":"в","vdash":"⊢","vDash":"⊨","Vdash":"⊩","VDash":"⊫","Vdashl":"⫦","veebar":"⊻","vee":"∨","Vee":"⋁","veeeq":"≚","vellip":"⋮","verbar":"|","Verbar":"‖","vert":"|","Vert":"‖","VerticalBar":"∣","VerticalLine":"|","VerticalSeparator":"❘","VerticalTilde":"≀","VeryThinSpace":" ","Vfr":"𝔙","vfr":"𝔳","vltri":"⊲","vnsub":"⊂⃒","vnsup":"⊃⃒","Vopf":"𝕍","vopf":"𝕧","vprop":"∝","vrtri":"⊳","Vscr":"𝒱","vscr":"𝓋","vsubnE":"⫋︀","vsubne":"⊊︀","vsupnE":"⫌︀","vsupne":"⊋︀","Vvdash":"⊪","vzigzag":"⦚","Wcirc":"Ŵ","wcirc":"ŵ","wedbar":"⩟","wedge":"∧","Wedge":"⋀","wedgeq":"≙","weierp":"℘","Wfr":"𝔚","wfr":"𝔴","Wopf":"𝕎","wopf":"𝕨","wp":"℘","wr":"≀","wreath":"≀","Wscr":"𝒲","wscr":"𝓌","xcap":"⋂","xcirc":"◯","xcup":"⋃","xdtri":"▽","Xfr":"𝔛","xfr":"𝔵","xharr":"⟷","xhArr":"⟺","Xi":"Ξ","xi":"ξ","xlarr":"⟵","xlArr":"⟸","xmap":"⟼","xnis":"⋻","xodot":"⨀","Xopf":"𝕏","xopf":"𝕩","xoplus":"⨁","xotime":"⨂","xrarr":"⟶","xrArr":"⟹","Xscr":"𝒳","xscr":"𝓍","xsqcup":"⨆","xuplus":"⨄","xutri":"△","xvee":"⋁","xwedge":"⋀","Yacute":"Ý","yacute":"ý","YAcy":"Я","yacy":"я","Ycirc":"Ŷ","ycirc":"ŷ","Ycy":"Ы","ycy":"ы","yen":"¥","Yfr":"𝔜","yfr":"𝔶","YIcy":"Ї","yicy":"ї","Yopf":"𝕐","yopf":"𝕪","Yscr":"𝒴","yscr":"𝓎","YUcy":"Ю","yucy":"ю","yuml":"ÿ","Yuml":"Ÿ","Zacute":"Ź","zacute":"ź","Zcaron":"Ž","zcaron":"ž","Zcy":"З","zcy":"з","Zdot":"Ż","zdot":"ż","zeetrf":"ℨ","ZeroWidthSpace":"​","Zeta":"Ζ","zeta":"ζ","zfr":"𝔷","Zfr":"ℨ","ZHcy":"Ж","zhcy":"ж","zigrarr":"⇝","zopf":"𝕫","Zopf":"ℤ","Zscr":"𝒵","zscr":"𝓏","zwj":"‍","zwnj":"‌"}')},function(e){e.exports=JSON.parse('{"Aacute":"Á","aacute":"á","Acirc":"Â","acirc":"â","acute":"´","AElig":"Æ","aelig":"æ","Agrave":"À","agrave":"à","amp":"&","AMP":"&","Aring":"Å","aring":"å","Atilde":"Ã","atilde":"ã","Auml":"Ä","auml":"ä","brvbar":"¦","Ccedil":"Ç","ccedil":"ç","cedil":"¸","cent":"¢","copy":"©","COPY":"©","curren":"¤","deg":"°","divide":"÷","Eacute":"É","eacute":"é","Ecirc":"Ê","ecirc":"ê","Egrave":"È","egrave":"è","ETH":"Ð","eth":"ð","Euml":"Ë","euml":"ë","frac12":"½","frac14":"¼","frac34":"¾","gt":">","GT":">","Iacute":"Í","iacute":"í","Icirc":"Î","icirc":"î","iexcl":"¡","Igrave":"Ì","igrave":"ì","iquest":"¿","Iuml":"Ï","iuml":"ï","laquo":"«","lt":"<","LT":"<","macr":"¯","micro":"µ","middot":"·","nbsp":" ","not":"¬","Ntilde":"Ñ","ntilde":"ñ","Oacute":"Ó","oacute":"ó","Ocirc":"Ô","ocirc":"ô","Ograve":"Ò","ograve":"ò","ordf":"ª","ordm":"º","Oslash":"Ø","oslash":"ø","Otilde":"Õ","otilde":"õ","Ouml":"Ö","ouml":"ö","para":"¶","plusmn":"±","pound":"£","quot":"\\"","QUOT":"\\"","raquo":"»","reg":"®","REG":"®","sect":"§","shy":"­","sup1":"¹","sup2":"²","sup3":"³","szlig":"ß","THORN":"Þ","thorn":"þ","times":"×","Uacute":"Ú","uacute":"ú","Ucirc":"Û","ucirc":"û","Ugrave":"Ù","ugrave":"ù","uml":"¨","Uuml":"Ü","uuml":"ü","Yacute":"Ý","yacute":"ý","yen":"¥","yuml":"ÿ"}')},function(e){e.exports=JSON.parse('{"amp":"&","apos":"\'","gt":">","lt":"<","quot":"\\""}')},function(e,t,r){"use strict";var n,i="object"==typeof Reflect?Reflect:null,a=i&&"function"==typeof i.apply?i.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};n=i&&"function"==typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var o=Number.isNaN||function(e){return e!=e};function s(){s.init.call(this)}e.exports=s,e.exports.once=function(e,t){return new Promise((function(r,n){function i(r){e.removeListener(t,a),n(r)}function a(){"function"==typeof e.removeListener&&e.removeListener("error",i),r([].slice.call(arguments))}y(e,t,a,{once:!0}),"error"!==t&&function(e,t,r){"function"==typeof e.on&&y(e,"error",t,r)}(e,i,{once:!0})}))},s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var l=10;function u(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function c(e){return void 0===e._maxListeners?s.defaultMaxListeners:e._maxListeners}function f(e,t,r,n){var i,a,o,s;if(u(r),void 0===(a=e._events)?(a=e._events=Object.create(null),e._eventsCount=0):(void 0!==a.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),a=e._events),o=a[t]),void 0===o)o=a[t]=r,++e._eventsCount;else if("function"==typeof o?o=a[t]=n?[r,o]:[o,r]:n?o.unshift(r):o.push(r),(i=c(e))>0&&o.length>i&&!o.warned){o.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=o.length,s=l,console&&console.warn&&console.warn(s)}return e}function p(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=p.bind(n);return i.listener=r,n.wrapFn=i,i}function h(e,t,r){var n=e._events;if(void 0===n)return[];var i=n[t];return void 0===i?[]:"function"==typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):g(i,i.length)}function m(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function g(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}function y(e,t,r,n){if("function"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function i(a){n.once&&e.removeEventListener(t,i),r(a)}))}}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return l},set:function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");l=e}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},s.prototype.getMaxListeners=function(){return c(this)},s.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var n="error"===e,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){var o;if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var s=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var l=i[e];if(void 0===l)return!1;if("function"==typeof l)a(l,this,t);else{var u=l.length,c=g(l,u);for(r=0;r<u;++r)a(c[r],this,t)}return!0},s.prototype.addListener=function(e,t){return f(this,e,t,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(e,t){return f(this,e,t,!0)},s.prototype.once=function(e,t){return u(t),this.on(e,d(this,e,t)),this},s.prototype.prependOnceListener=function(e,t){return u(t),this.prependListener(e,d(this,e,t)),this},s.prototype.removeListener=function(e,t){var r,n,i,a,o;if(u(t),void 0===(n=this._events))return this;if(void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(i=-1,a=r.length-1;a>=0;a--)if(r[a]===t||r[a].listener===t){o=r[a].listener,i=a;break}if(i<0)return this;0===i?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,i),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",e,o||t)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(e){var t,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var i,a=Object.keys(r);for(n=0;n<a.length;++n)"removeListener"!==(i=a[n])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},s.prototype.listeners=function(e){return h(this,e,!0)},s.prototype.rawListeners=function(e){return h(this,e,!1)},s.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):m.call(e,t)},s.prototype.listenerCount=m,s.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]}},function(e,t,r){var n=r(17),i=e.exports=Object.create(n),a={tagName:"name"};Object.keys(a).forEach((function(e){var t=a[e];Object.defineProperty(i,e,{get:function(){return this[t]||null},set:function(e){return this[t]=e,e}})}))},function(e,t,r){var n=r(16),i=r(18);function a(e,t){this.init(e,t)}function o(e,t){return i.getElementsByTagName(e,t,!0)}function s(e,t){return i.getElementsByTagName(e,t,!0,1)[0]}function l(e,t,r){return i.getText(i.getElementsByTagName(e,t,r,1)).trim()}function u(e,t,r,n,i){var a=l(r,n,i);a&&(e[t]=a)}r(6)(a,n),a.prototype.init=n;var c=function(e){return"rss"===e||"feed"===e||"rdf:RDF"===e};a.prototype.onend=function(){var e,t,r={},i=s(c,this.dom);i&&("feed"===i.name?(t=i.children,r.type="atom",u(r,"id","id",t),u(r,"title","title",t),(e=s("link",t))&&(e=e.attribs)&&(e=e.href)&&(r.link=e),u(r,"description","subtitle",t),(e=l("updated",t))&&(r.updated=new Date(e)),u(r,"author","email",t,!0),r.items=o("entry",t).map((function(e){var t,r={};return u(r,"id","id",e=e.children),u(r,"title","title",e),(t=s("link",e))&&(t=t.attribs)&&(t=t.href)&&(r.link=t),(t=l("summary",e)||l("content",e))&&(r.description=t),(t=l("updated",e))&&(r.pubDate=new Date(t)),r}))):(t=s("channel",i.children).children,r.type=i.name.substr(0,3),r.id="",u(r,"title","title",t),u(r,"link","link",t),u(r,"description","description",t),(e=l("lastBuildDate",t))&&(r.updated=new Date(e)),u(r,"author","managingEditor",t,!0),r.items=o("item",i.children).map((function(e){var t,r={};return u(r,"id","guid",e=e.children),u(r,"title","title",e),u(r,"link","link",e),u(r,"description","description",e),(t=l("pubDate",e))&&(r.pubDate=new Date(t)),r})))),this.dom=r,n.prototype._handleCallback.call(this,i?null:Error("couldn't find root of feed"))},e.exports=a},function(e,t,r){var n=r(5),i=r(38),a=n.isTag;e.exports={getInnerHTML:function(e,t){return e.children?e.children.map((function(e){return i(e,t)})).join(""):""},getOuterHTML:i,getText:function e(t){return Array.isArray(t)?t.map(e).join(""):a(t)?"br"===t.name?"\n":e(t.children):t.type===n.CDATA?e(t.children):t.type===n.Text?t.data:""}}},function(e,t,r){var n=r(39),i=r(40),a=r(44);a.elementNames.__proto__=null,a.attributeNames.__proto__=null;var o={__proto__:null,style:!0,script:!0,xmp:!0,iframe:!0,noembed:!0,noframes:!0,plaintext:!0,noscript:!0};var s={__proto__:null,area:!0,base:!0,basefont:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,isindex:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},l=e.exports=function(e,t){Array.isArray(e)||e.cheerio||(e=[e]),t=t||{};for(var r="",i=0;i<e.length;i++){var a=e[i];"root"===a.type?r+=l(a.children,t):n.isTag(a)?r+=c(a,t):a.type===n.Directive?r+=f(a):a.type===n.Comment?r+=h(a):a.type===n.CDATA?r+=d(a):r+=p(a,t)}return r},u=["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"];function c(e,t){"foreign"===t.xmlMode&&(e.name=a.elementNames[e.name]||e.name,e.parent&&u.indexOf(e.parent.name)>=0&&(t=Object.assign({},t,{xmlMode:!1}))),!t.xmlMode&&["svg","math"].indexOf(e.name)>=0&&(t=Object.assign({},t,{xmlMode:"foreign"}));var r="<"+e.name,n=function(e,t){if(e){var r,n="";for(var o in e)r=e[o],n&&(n+=" "),"foreign"===t.xmlMode&&(o=a.attributeNames[o]||o),n+=o,(null!==r&&""!==r||t.xmlMode)&&(n+='="'+(t.decodeEntities?i.encodeXML(r):r.replace(/\"/g,"&quot;"))+'"');return n}}(e.attribs,t);return n&&(r+=" "+n),!t.xmlMode||e.children&&0!==e.children.length?(r+=">",e.children&&(r+=l(e.children,t)),s[e.name]&&!t.xmlMode||(r+="</"+e.name+">")):r+="/>",r}function f(e){return"<"+e.data+">"}function p(e,t){var r=e.data||"";return!t.decodeEntities||e.parent&&e.parent.name in o||(r=i.encodeXML(r)),r}function d(e){return"<![CDATA["+e.children[0].data+"]]>"}function h(e){return"\x3c!--"+e.data+"--\x3e"}},function(e,t,r){"use strict";var n;function i(e){return e.type===n.Tag||e.type===n.Script||e.type===n.Style}r.r(t),r.d(t,"ElementType",(function(){return n})),r.d(t,"isTag",(function(){return i})),r.d(t,"Root",(function(){return a})),r.d(t,"Text",(function(){return o})),r.d(t,"Directive",(function(){return s})),r.d(t,"Comment",(function(){return l})),r.d(t,"Script",(function(){return u})),r.d(t,"Style",(function(){return c})),r.d(t,"Tag",(function(){return f})),r.d(t,"CDATA",(function(){return p})),r.d(t,"Doctype",(function(){return d})),function(e){e.Root="root",e.Text="text",e.Directive="directive",e.Comment="comment",e.Script="script",e.Style="style",e.Tag="tag",e.CDATA="cdata",e.Doctype="doctype"}(n||(n={}));const a=n.Root,o=n.Text,s=n.Directive,l=n.Comment,u=n.Script,c=n.Style,f=n.Tag,p=n.CDATA,d=n.Doctype},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decodeXMLStrict=t.decodeHTML5Strict=t.decodeHTML4Strict=t.decodeHTML5=t.decodeHTML4=t.decodeHTMLStrict=t.decodeHTML=t.decodeXML=t.encodeHTML5=t.encodeHTML4=t.escapeUTF8=t.escape=t.encodeNonAsciiHTML=t.encodeHTML=t.encodeXML=t.encode=t.decodeStrict=t.decode=void 0;var n=r(19),i=r(22);t.decode=function(e,t){return(!t||t<=0?n.decodeXML:n.decodeHTML)(e)},t.decodeStrict=function(e,t){return(!t||t<=0?n.decodeXML:n.decodeHTMLStrict)(e)},t.encode=function(e,t){return(!t||t<=0?i.encodeXML:i.encodeHTML)(e)};var a=r(22);Object.defineProperty(t,"encodeXML",{enumerable:!0,get:function(){return a.encodeXML}}),Object.defineProperty(t,"encodeHTML",{enumerable:!0,get:function(){return a.encodeHTML}}),Object.defineProperty(t,"encodeNonAsciiHTML",{enumerable:!0,get:function(){return a.encodeNonAsciiHTML}}),Object.defineProperty(t,"escape",{enumerable:!0,get:function(){return a.escape}}),Object.defineProperty(t,"escapeUTF8",{enumerable:!0,get:function(){return a.escapeUTF8}}),Object.defineProperty(t,"encodeHTML4",{enumerable:!0,get:function(){return a.encodeHTML}}),Object.defineProperty(t,"encodeHTML5",{enumerable:!0,get:function(){return a.encodeHTML}});var o=r(19);Object.defineProperty(t,"decodeXML",{enumerable:!0,get:function(){return o.decodeXML}}),Object.defineProperty(t,"decodeHTML",{enumerable:!0,get:function(){return o.decodeHTML}}),Object.defineProperty(t,"decodeHTMLStrict",{enumerable:!0,get:function(){return o.decodeHTMLStrict}}),Object.defineProperty(t,"decodeHTML4",{enumerable:!0,get:function(){return o.decodeHTML}}),Object.defineProperty(t,"decodeHTML5",{enumerable:!0,get:function(){return o.decodeHTML}}),Object.defineProperty(t,"decodeHTML4Strict",{enumerable:!0,get:function(){return o.decodeHTMLStrict}}),Object.defineProperty(t,"decodeHTML5Strict",{enumerable:!0,get:function(){return o.decodeHTMLStrict}}),Object.defineProperty(t,"decodeXMLStrict",{enumerable:!0,get:function(){return o.decodeXML}})},function(e){e.exports=JSON.parse('{"Aacute":"Á","aacute":"á","Acirc":"Â","acirc":"â","acute":"´","AElig":"Æ","aelig":"æ","Agrave":"À","agrave":"à","amp":"&","AMP":"&","Aring":"Å","aring":"å","Atilde":"Ã","atilde":"ã","Auml":"Ä","auml":"ä","brvbar":"¦","Ccedil":"Ç","ccedil":"ç","cedil":"¸","cent":"¢","copy":"©","COPY":"©","curren":"¤","deg":"°","divide":"÷","Eacute":"É","eacute":"é","Ecirc":"Ê","ecirc":"ê","Egrave":"È","egrave":"è","ETH":"Ð","eth":"ð","Euml":"Ë","euml":"ë","frac12":"½","frac14":"¼","frac34":"¾","gt":">","GT":">","Iacute":"Í","iacute":"í","Icirc":"Î","icirc":"î","iexcl":"¡","Igrave":"Ì","igrave":"ì","iquest":"¿","Iuml":"Ï","iuml":"ï","laquo":"«","lt":"<","LT":"<","macr":"¯","micro":"µ","middot":"·","nbsp":" ","not":"¬","Ntilde":"Ñ","ntilde":"ñ","Oacute":"Ó","oacute":"ó","Ocirc":"Ô","ocirc":"ô","Ograve":"Ò","ograve":"ò","ordf":"ª","ordm":"º","Oslash":"Ø","oslash":"ø","Otilde":"Õ","otilde":"õ","Ouml":"Ö","ouml":"ö","para":"¶","plusmn":"±","pound":"£","quot":"\\"","QUOT":"\\"","raquo":"»","reg":"®","REG":"®","sect":"§","shy":"­","sup1":"¹","sup2":"²","sup3":"³","szlig":"ß","THORN":"Þ","thorn":"þ","times":"×","Uacute":"Ú","uacute":"ú","Ucirc":"Û","ucirc":"û","Ugrave":"Ù","ugrave":"ù","uml":"¨","Uuml":"Ü","uuml":"ü","Yacute":"Ý","yacute":"ý","yen":"¥","yuml":"ÿ"}')},function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var i=n(r(43)),a=String.fromCodePoint||function(e){var t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e)};t.default=function(e){return e>=55296&&e<=57343||e>1114111?"�":(e in i.default&&(e=i.default[e]),a(e))}},function(e){e.exports=JSON.parse('{"0":65533,"128":8364,"130":8218,"131":402,"132":8222,"133":8230,"134":8224,"135":8225,"136":710,"137":8240,"138":352,"139":8249,"140":338,"142":381,"145":8216,"146":8217,"147":8220,"148":8221,"149":8226,"150":8211,"151":8212,"152":732,"153":8482,"154":353,"155":8250,"156":339,"158":382,"159":376}')},function(e){e.exports=JSON.parse('{"elementNames":{"altglyph":"altGlyph","altglyphdef":"altGlyphDef","altglyphitem":"altGlyphItem","animatecolor":"animateColor","animatemotion":"animateMotion","animatetransform":"animateTransform","clippath":"clipPath","feblend":"feBlend","fecolormatrix":"feColorMatrix","fecomponenttransfer":"feComponentTransfer","fecomposite":"feComposite","feconvolvematrix":"feConvolveMatrix","fediffuselighting":"feDiffuseLighting","fedisplacementmap":"feDisplacementMap","fedistantlight":"feDistantLight","fedropshadow":"feDropShadow","feflood":"feFlood","fefunca":"feFuncA","fefuncb":"feFuncB","fefuncg":"feFuncG","fefuncr":"feFuncR","fegaussianblur":"feGaussianBlur","feimage":"feImage","femerge":"feMerge","femergenode":"feMergeNode","femorphology":"feMorphology","feoffset":"feOffset","fepointlight":"fePointLight","fespecularlighting":"feSpecularLighting","fespotlight":"feSpotLight","fetile":"feTile","feturbulence":"feTurbulence","foreignobject":"foreignObject","glyphref":"glyphRef","lineargradient":"linearGradient","radialgradient":"radialGradient","textpath":"textPath"},"attributeNames":{"definitionurl":"definitionURL","attributename":"attributeName","attributetype":"attributeType","basefrequency":"baseFrequency","baseprofile":"baseProfile","calcmode":"calcMode","clippathunits":"clipPathUnits","diffuseconstant":"diffuseConstant","edgemode":"edgeMode","filterunits":"filterUnits","glyphref":"glyphRef","gradienttransform":"gradientTransform","gradientunits":"gradientUnits","kernelmatrix":"kernelMatrix","kernelunitlength":"kernelUnitLength","keypoints":"keyPoints","keysplines":"keySplines","keytimes":"keyTimes","lengthadjust":"lengthAdjust","limitingconeangle":"limitingConeAngle","markerheight":"markerHeight","markerunits":"markerUnits","markerwidth":"markerWidth","maskcontentunits":"maskContentUnits","maskunits":"maskUnits","numoctaves":"numOctaves","pathlength":"pathLength","patterncontentunits":"patternContentUnits","patterntransform":"patternTransform","patternunits":"patternUnits","pointsatx":"pointsAtX","pointsaty":"pointsAtY","pointsatz":"pointsAtZ","preservealpha":"preserveAlpha","preserveaspectratio":"preserveAspectRatio","primitiveunits":"primitiveUnits","refx":"refX","refy":"refY","repeatcount":"repeatCount","repeatdur":"repeatDur","requiredextensions":"requiredExtensions","requiredfeatures":"requiredFeatures","specularconstant":"specularConstant","specularexponent":"specularExponent","spreadmethod":"spreadMethod","startoffset":"startOffset","stddeviation":"stdDeviation","stitchtiles":"stitchTiles","surfacescale":"surfaceScale","systemlanguage":"systemLanguage","tablevalues":"tableValues","targetx":"targetX","targety":"targetY","textlength":"textLength","viewbox":"viewBox","viewtarget":"viewTarget","xchannelselector":"xChannelSelector","ychannelselector":"yChannelSelector","zoomandpan":"zoomAndPan"}}')},function(e,t){var r=t.getChildren=function(e){return e.children},n=t.getParent=function(e){return e.parent};t.getSiblings=function(e){var t=n(e);return t?r(t):[e]},t.getAttributeValue=function(e,t){return e.attribs&&e.attribs[t]},t.hasAttrib=function(e,t){return!!e.attribs&&hasOwnProperty.call(e.attribs,t)},t.getName=function(e){return e.name}},function(e,t){t.removeElement=function(e){if(e.prev&&(e.prev.next=e.next),e.next&&(e.next.prev=e.prev),e.parent){var t=e.parent.children;t.splice(t.lastIndexOf(e),1)}},t.replaceElement=function(e,t){var r=t.prev=e.prev;r&&(r.next=t);var n=t.next=e.next;n&&(n.prev=t);var i=t.parent=e.parent;if(i){var a=i.children;a[a.lastIndexOf(e)]=t}},t.appendChild=function(e,t){if(t.parent=e,1!==e.children.push(t)){var r=e.children[e.children.length-2];r.next=t,t.prev=r,t.next=null}},t.append=function(e,t){var r=e.parent,n=e.next;if(t.next=n,t.prev=e,e.next=t,t.parent=r,n){if(n.prev=t,r){var i=r.children;i.splice(i.lastIndexOf(n),0,t)}}else r&&r.children.push(t)},t.prepend=function(e,t){var r=e.parent;if(r){var n=r.children;n.splice(n.lastIndexOf(e),0,t)}e.prev&&(e.prev.next=t),t.parent=r,t.prev=e.prev,t.next=e,e.prev=t}},function(e,t,r){var n=r(5).isTag;function i(e,t,r,n){for(var a,o=[],s=0,l=t.length;s<l&&!(e(t[s])&&(o.push(t[s]),--n<=0))&&(a=t[s].children,!(r&&a&&a.length>0&&(a=i(e,a,r,n),o=o.concat(a),(n-=a.length)<=0)));s++);return o}e.exports={filter:function(e,t,r,n){Array.isArray(t)||(t=[t]);"number"==typeof n&&isFinite(n)||(n=1/0);return i(e,t,!1!==r,n)},find:i,findOneChild:function(e,t){for(var r=0,n=t.length;r<n;r++)if(e(t[r]))return t[r];return null},findOne:function e(t,r){for(var i=null,a=0,o=r.length;a<o&&!i;a++)n(r[a])&&(t(r[a])?i=r[a]:r[a].children.length>0&&(i=e(t,r[a].children)));return i},existsOne:function e(t,r){for(var i=0,a=r.length;i<a;i++)if(n(r[i])&&(t(r[i])||r[i].children.length>0&&e(t,r[i].children)))return!0;return!1},findAll:function(e,t){var r=[],i=t.slice();for(;i.length;){var a=i.shift();n(a)&&(a.children&&a.children.length>0&&i.unshift.apply(i,a.children),e(a)&&r.push(a))}return r}}},function(e,t,r){var n=r(5),i=t.isTag=n.isTag;t.testElement=function(e,t){for(var r in e)if(e.hasOwnProperty(r)){if("tag_name"===r){if(!i(t)||!e.tag_name(t.name))return!1}else if("tag_type"===r){if(!e.tag_type(t.type))return!1}else if("tag_contains"===r){if(i(t)||!e.tag_contains(t.data))return!1}else if(!t.attribs||!e[r](t.attribs[r]))return!1}else;return!0};var a={tag_name:function(e){return"function"==typeof e?function(t){return i(t)&&e(t.name)}:"*"===e?i:function(t){return i(t)&&t.name===e}},tag_type:function(e){return"function"==typeof e?function(t){return e(t.type)}:function(t){return t.type===e}},tag_contains:function(e){return"function"==typeof e?function(t){return!i(t)&&e(t.data)}:function(t){return!i(t)&&t.data===e}}};function o(e,t){return"function"==typeof t?function(r){return r.attribs&&t(r.attribs[e])}:function(r){return r.attribs&&r.attribs[e]===t}}function s(e,t){return function(r){return e(r)||t(r)}}t.getElements=function(e,t,r,n){var i=Object.keys(e).map((function(t){var r=e[t];return t in a?a[t](r):o(t,r)}));return 0===i.length?[]:this.filter(i.reduce(s),t,r,n)},t.getElementById=function(e,t,r){return Array.isArray(t)||(t=[t]),this.findOne(o("id",e),t,!1!==r)},t.getElementsByTagName=function(e,t,r,n){return this.filter(a.tag_name(e),t,r,n)},t.getElementsByTagType=function(e,t,r,n){return this.filter(a.tag_type(e),t,r,n)}},function(e,t){t.removeSubsets=function(e){for(var t,r,n,i=e.length;--i>-1;){for(t=r=e[i],e[i]=null,n=!0;r;){if(e.indexOf(r)>-1){n=!1,e.splice(i,1);break}r=r.parent}n&&(e[i]=t)}return e};var r=1,n=2,i=4,a=8,o=16,s=t.compareDocumentPosition=function(e,t){var s,l,u,c,f,p,d=[],h=[];if(e===t)return 0;for(s=e;s;)d.unshift(s),s=s.parent;for(s=t;s;)h.unshift(s),s=s.parent;for(p=0;d[p]===h[p];)p++;return 0===p?r:(u=(l=d[p-1]).children,c=d[p],f=h[p],u.indexOf(c)>u.indexOf(f)?l===t?i|o:i:l===e?n|a:n)};t.uniqueSort=function(e){var t,r,a=e.length;for(e=e.slice();--a>-1;)t=e[a],(r=e.indexOf(t))>-1&&r<a&&e.splice(a,1);return e.sort((function(e,t){var r=s(e,t);return r&n?-1:r&i?1:0})),e}},function(e,t,r){e.exports=i;var n=r(23);function i(e){n.call(this,new a(this),e)}function a(e){this.scope=e}r(6)(i,n),i.prototype.readable=!0;var o=r(2).EVENTS;Object.keys(o).forEach((function(e){if(0===o[e])a.prototype["on"+e]=function(){this.scope.emit(e)};else if(1===o[e])a.prototype["on"+e]=function(t){this.scope.emit(e,t)};else{if(2!==o[e])throw Error("wrong number of arguments!");a.prototype["on"+e]=function(t,r){this.scope.emit(e,t,r)}}}))},function(e,t){},function(e,t,r){"use strict";var n=r(53).Buffer,i=n.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function a(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(n.isEncoding===i||!i(e)))throw new Error("Unknown encoding: "+e);return t||e}(e),this.encoding){case"utf16le":this.text=l,this.end=u,t=4;break;case"utf8":this.fillLast=s,t=4;break;case"base64":this.text=c,this.end=f,t=3;break;default:return this.write=p,void(this.end=d)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function o(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function s(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function l(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function u(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function c(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function f(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function p(e){return e.toString(this.encoding)}function d(e){return e&&e.length?this.write(e):""}t.StringDecoder=a,a.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},a.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},a.prototype.text=function(e,t){var r=function(e,t,r){var n=t.length-1;if(n<r)return 0;var i=o(t[n]);if(i>=0)return i>0&&(e.lastNeed=i-1),i;if(--n<r||-2===i)return 0;if((i=o(t[n]))>=0)return i>0&&(e.lastNeed=i-2),i;if(--n<r||-2===i)return 0;if((i=o(t[n]))>=0)return i>0&&(2===i?i=0:e.lastNeed=i-3),i;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)},a.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},function(e,t,r){
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
var n=r(24),i=n.Buffer;function a(e,t){for(var r in e)t[r]=e[r]}function o(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=n:(a(n,t),t.Buffer=o),o.prototype=Object.create(i.prototype),a(i,o),o.from=function(e,t,r){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,t,r)},o.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},o.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}},function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r},function(e,t,r){"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return 3*(r+n)/4-n},t.toByteArray=function(e){var t,r,n=u(e),o=n[0],s=n[1],l=new a(function(e,t,r){return 3*(t+r)/4-r}(0,o,s)),c=0,f=s>0?o-4:o;for(r=0;r<f;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],l[c++]=t>>16&255,l[c++]=t>>8&255,l[c++]=255&t;2===s&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,l[c++]=255&t);1===s&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,l[c++]=t>>8&255,l[c++]=255&t);return l},t.fromByteArray=function(e){for(var t,r=e.length,i=r%3,a=[],o=0,s=r-i;o<s;o+=16383)a.push(c(e,o,o+16383>s?s:o+16383));1===i?(t=e[r-1],a.push(n[t>>2]+n[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],a.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"="));return a.join("")};for(var n=[],i=[],a="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/",s=0,l=o.length;s<l;++s)n[s]=o[s],i[o.charCodeAt(s)]=s;function u(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function c(e,t,r){for(var i,a,o=[],s=t;s<r;s+=3)i=(e[s]<<16&16711680)+(e[s+1]<<8&65280)+(255&e[s+2]),o.push(n[(a=i)>>18&63]+n[a>>12&63]+n[a>>6&63]+n[63&a]);return o.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},function(e,t){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
t.read=function(e,t,r,n,i){var a,o,s=8*i-n-1,l=(1<<s)-1,u=l>>1,c=-7,f=r?i-1:0,p=r?-1:1,d=e[t+f];for(f+=p,a=d&(1<<-c)-1,d>>=-c,c+=s;c>0;a=256*a+e[t+f],f+=p,c-=8);for(o=a&(1<<-c)-1,a>>=-c,c+=n;c>0;o=256*o+e[t+f],f+=p,c-=8);if(0===a)a=1-u;else{if(a===l)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,n),a-=u}return(d?-1:1)*o*Math.pow(2,a-n)},t.write=function(e,t,r,n,i,a){var o,s,l,u=8*a-i-1,c=(1<<u)-1,f=c>>1,p=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:a-1,h=n?1:-1,m=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,o=c):(o=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-o))<1&&(o--,l*=2),(t+=o+f>=1?p/l:p*Math.pow(2,1-f))*l>=2&&(o++,l/=2),o+f>=c?(s=0,o=c):o+f>=1?(s=(t*l-1)*Math.pow(2,i),o+=f):(s=t*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;e[r+d]=255&s,d+=h,s/=256,i-=8);for(o=o<<i|s,u+=i;u>0;e[r+d]=255&o,d+=h,o/=256,u-=8);e[r+d-h]|=128*m}},function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},function(e,t,r){function n(e){this._cbs=e||{}}e.exports=n;var i=r(2).EVENTS;Object.keys(i).forEach((function(e){if(0===i[e])e="on"+e,n.prototype[e]=function(){this._cbs[e]&&this._cbs[e]()};else if(1===i[e])e="on"+e,n.prototype[e]=function(t){this._cbs[e]&&this._cbs[e](t)};else{if(2!==i[e])throw Error("wrong number of arguments");e="on"+e,n.prototype[e]=function(t,r){this._cbs[e]&&this._cbs[e](t,r)}}}))},function(e,t,r){function n(e){this._cbs=e||{},this.events=[]}e.exports=n;var i=r(2).EVENTS;Object.keys(i).forEach((function(e){if(0===i[e])e="on"+e,n.prototype[e]=function(){this.events.push([e]),this._cbs[e]&&this._cbs[e]()};else if(1===i[e])e="on"+e,n.prototype[e]=function(t){this.events.push([e,t]),this._cbs[e]&&this._cbs[e](t)};else{if(2!==i[e])throw Error("wrong number of arguments");e="on"+e,n.prototype[e]=function(t,r){this.events.push([e,t,r]),this._cbs[e]&&this._cbs[e](t,r)}}})),n.prototype.onreset=function(){this.events=[],this._cbs.onreset&&this._cbs.onreset()},n.prototype.restart=function(){this._cbs.onreset&&this._cbs.onreset();for(var e=0,t=this.events.length;e<t;e++)if(this._cbs[this.events[e][0]]){var r=this.events[e].length;1===r?this._cbs[this.events[e][0]]():2===r?this._cbs[this.events[e][0]](this.events[e][1]):this._cbs[this.events[e][0]](this.events[e][1],this.events[e][2])}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return e.data}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){var l=e.name;if(!(0,s.default)(l))return null;var u=(0,a.default)(e.attribs,t),c=null;-1===o.default.indexOf(l)&&(c=(0,i.default)(e.children,r));return n.default.createElement(l,u,c)};var n=l(r(0)),i=l(r(12)),a=l(r(25)),o=l(r(66)),s=l(r(26));function l(e){return e&&e.__esModule?e:{default:e}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return Object.keys(e).filter((function(e){return(0,a.default)(e)})).reduce((function(t,r){var a=r.toLowerCase(),o=i.default[a]||a;return t[o]=function(e,t){n.default.map((function(e){return e.toLowerCase()})).indexOf(e.toLowerCase())>=0&&(t=e);return t}(o,e[r]),t}),{})};var n=o(r(63)),i=o(r(64)),a=o(r(26));function o(e){return e&&e.__esModule?e:{default:e}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=["allowfullScreen","async","autoplay","capture","checked","controls","default","defer","disabled","formnovalidate","hidden","loop","multiple","muted","novalidate","open","playsinline","readonly","required","reversed","scoped","seamless","selected","itemscope"]},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={accept:"accept","accept-charset":"acceptCharset",accesskey:"accessKey",action:"action",allowfullscreen:"allowFullScreen",allowtransparency:"allowTransparency",alt:"alt",as:"as",async:"async",autocomplete:"autoComplete",autoplay:"autoPlay",capture:"capture",cellpadding:"cellPadding",cellspacing:"cellSpacing",charset:"charSet",challenge:"challenge",checked:"checked",cite:"cite",classid:"classID",class:"className",cols:"cols",colspan:"colSpan",content:"content",contenteditable:"contentEditable",contextmenu:"contextMenu",controls:"controls",controlsList:"controlsList",coords:"coords",crossorigin:"crossOrigin",data:"data",datetime:"dateTime",default:"default",defer:"defer",dir:"dir",disabled:"disabled",download:"download",draggable:"draggable",enctype:"encType",form:"form",formaction:"formAction",formenctype:"formEncType",formmethod:"formMethod",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder",headers:"headers",height:"height",hidden:"hidden",high:"high",href:"href",hreflang:"hrefLang",for:"htmlFor","http-equiv":"httpEquiv",icon:"icon",id:"id",inputmode:"inputMode",integrity:"integrity",is:"is",keyparams:"keyParams",keytype:"keyType",kind:"kind",label:"label",lang:"lang",list:"list",loop:"loop",low:"low",manifest:"manifest",marginheight:"marginHeight",marginwidth:"marginWidth",max:"max",maxlength:"maxLength",media:"media",mediagroup:"mediaGroup",method:"method",min:"min",minlength:"minLength",multiple:"multiple",muted:"muted",name:"name",nonce:"nonce",novalidate:"noValidate",open:"open",optimum:"optimum",pattern:"pattern",placeholder:"placeholder",playsinline:"playsInline",poster:"poster",preload:"preload",profile:"profile",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",rel:"rel",required:"required",reversed:"reversed",role:"role",rows:"rows",rowspan:"rowSpan",sandbox:"sandbox",scope:"scope",scoped:"scoped",scrolling:"scrolling",seamless:"seamless",selected:"selected",shape:"shape",size:"size",sizes:"sizes",slot:"slot",span:"span",spellcheck:"spellCheck",src:"src",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",start:"start",step:"step",style:"style",summary:"summary",tabindex:"tabIndex",target:"target",title:"title",type:"type",usemap:"useMap",value:"value",width:"width",wmode:"wmode",wrap:"wrap",about:"about",datatype:"datatype",inlist:"inlist",prefix:"prefix",property:"property",resource:"resource",typeof:"typeof",vocab:"vocab",autocapitalize:"autoCapitalize",autocorrect:"autoCorrect",autosave:"autoSave",color:"color",itemprop:"itemProp",itemscope:"itemScope",itemtype:"itemType",itemid:"itemID",itemref:"itemRef",results:"results",security:"security",unselectable:"unselectable"}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{!n&&s.return&&s.return()}finally{if(i)throw a}}return r}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")};t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(""===e)return{};return e.split(";").reduce((function(e,t){var r=t.split(/^([^:]+):/).filter((function(e,t){return t>0})).map((function(e){return e.trim().toLowerCase()})),i=n(r,2),a=i[0],o=i[1];return void 0===o||(e[a=a.replace(/^-ms-/,"ms-").replace(/-(.)/g,(function(e,t){return t.toUpperCase()}))]=o),e}),{})}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=void 0;e.children.length>0&&(r=e.children[0].data);var a=(0,i.default)(e.attribs,t);return n.default.createElement("style",a,r)};var n=a(r(0)),i=a(r(25));function a(e){return e&&e.__esModule?e:{default:e}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return null}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.decodeEntities,a=void 0===r||r,o=t.transform,s=t.preprocessNodes,l=void 0===s?function(e){return e}:s,u=l(n.default.parseDOM(e,{decodeEntities:a}));return(0,i.default)(u,o)};var n=a(r(2)),i=a(r(12));function a(e){return e&&e.__esModule?e:{default:e}}},,,,function(e,t,r){"use strict";r.r(t);var n=r(0),i=r.n(n),a=r(3),o=r.n(a);const{__:s}=wp.i18n,l=[{slug:"templatespare",description:s("TemplateSpare - No-Code Website Builder - 1-Click Import/Export and Full Site Migration Tool. Get ready-to-use Starter Sites instantly and build modern websites in minutes.","morenews")},{slug:"wp-post-author",description:s("It has different widgets for post's author, selective author by id and custom author. It also provides shortcodes for the author profile.","morenews")},{slug:"af-companion",description:s("Use AF Companion to import live demo content, widgets, and settings swiftly. This plugin gives fundamental layout to build your website and accelerate the development process.","morenews")},{slug:"elespare",description:s("DESIGN PERFECT BLOG, NEWS, OR MAGAZINE WEBSITES Elespare is a simple yet effective WordPress plugin that lets you use Elementor to create fantastic Header, Footer and Contents of websites with highly customization options for FREE. ","morenews")},{slug:"blockspare",description:s("You may design complicated layouts for your website in just a few minutes with our adaptable and inventive blocks.","morenews")},{slug:"latest-posts-block-lite",description:s("A beautiful collection of latest posts Gutenberg blocks for WordPress, which helps you to design posts grid, posts list, full posts layout, advanced express posts design and tile layouts of your posts.","morenews")},{slug:"magic-content-box-lite",description:s("We build a beautiful series of page section Gutenberg blocks for WordPress content to help you quickly create the website you've always desired.","morenews")},{slug:"wp-live-chat-support",description:s("This free plugin offers a free live chat solution for your website visitors to contact you via live chat or a live call - all for free! Engage with your customers and increase sales.","morenews")}],u=[{slug:"templatespare",title:s("TemplateSpare -   No-Code Website Builder - 1-Click Import/Export and Full Site Migration Tool","morenews"),description:s("Get over 1000 ready-to-import Starter Sites instantly and build modern websites in minutes, not in weeks.","morenews")}];var c=r(1);const{__:f}=wp.i18n,p=c.a.replaceAll("[theme]",afDashboardData.themeName);var d=function(){return React.createElement("div",{className:"aft-dashboard-content"},React.createElement("div",{className:"aft-dashbord-header"},React.createElement("div",{className:"aft-dashbord-container"},React.createElement("figure",null,React.createElement("img",{src:afDashboardData.aflogoUrl+c.b,alt:""})),React.createElement("div",{className:"aft-dashbord-header-link"},c.c&&c.c.map(e=>React.createElement("a",{href:e.textUrl,target:"_blank"},e.linkeText))))),React.createElement("div",{className:"aft-dashbord-container"},React.createElement("div",{className:"aft-hero-section"},React.createElement("div",{className:"aft-theme-img"},React.createElement("img",{src:afDashboardData.themeUrl+"/screenshot.png"})),React.createElement("div",{className:"aft-theme-info"},React.createElement("div",{className:"afte-greeting-note"},React.createElement("h4",null,f("Hello ","morenews")+afDashboardData.currentUser+","),React.createElement("h2",null,f("Welcome to ","morenews")+afDashboardData.themeName,React.createElement("span",{className:"aft-badge aft-badge-success aft-theme-version"},f("Free","morenews")))),React.createElement("p",null,p),React.createElement("div",{className:"aft-theme-info-btn"},React.createElement("a",{href:afDashboardData.starter_sites_url,className:"button button-primary"},f("Start with Demo Sites","morenews")),React.createElement("a",{href:afDashboardData.customizer_url,target:"_blank",className:"button"},f("Start Customizing","morenews")),React.createElement("a",{href:c.e,target:"_blank",className:"button"},c.d))))))};const{__:h}=wp.i18n,m=[{icon:"<svg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 23.73 20.2' > <g id='ic-layout-header'> <path d='M2.81,2.32H21.19a2.15,2.15,0,0,1,2.3,1.95V19.83a2.14,2.14,0,0,1-2.3,2H2.81a2.14,2.14,0,0,1-2.3-2V4.27A2.15,2.15,0,0,1,2.81,2.32Z' transform='translate(-0.14 -1.95)' fill='none' stroke='#2271b1' strokeLinecap='round' strokeLinejoin='round' strokeWidth='0.75' /> <line x1='0.38' y1='5.24' x2='23.35' y2='5.24' fill='none' stroke='#2271b1' strokeLinecap='round' strokeLinejoin='round' strokeWidth='0.75' /> </g> </svg>",title:h("Header Options","morenews"),description:h("Set the theme header type, set the colors, spacing, alignment and more.","morenews"),section:"header_options_settings",control:"[section]"},{icon:"<svg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 438.69'><path d='M485.08,36.65H26.92A27,27,0,0,0,0,63.57V448.43a27,27,0,0,0,26.92,26.92H485.08A27,27,0,0,0,512,448.43V63.57A27,27,0,0,0,485.08,36.65ZM127.14,53.83h331V73.31h-331Zm-36.65,0H110V73.31H90.49Zm-36.66,0H73.31V73.31H53.83ZM17.18,63.57a9.75,9.75,0,0,1,9.74-9.74h9.73V73.31H17.18ZM494.82,448.43a9.75,9.75,0,0,1-9.74,9.74H26.92a9.75,9.75,0,0,1-9.74-9.74V90.49H494.82Zm0-375.12H475.35V53.83h9.73a9.75,9.75,0,0,1,9.74,9.74v9.74Z' transform='translate(0 -36.65)'/><path d='M466.76,110H45.24a8.59,8.59,0,1,0,0,17.18H466.76a8.59,8.59,0,1,0,0-17.18Z' transform='translate(0 -36.65)'/><path d='M329.31,164.94H311a17.78,17.78,0,0,0-16.91,12.35c-73.69,43-153.53,50.3-176.5,51.51a17.72,17.72,0,0,0-16.77,17.72V283.8a17.71,17.71,0,0,0,16.77,17.72c4.58.24,11.42.73,20,1.69l25.81,94.9a17.79,17.79,0,0,0,17.14,13.09h38.81a8.59,8.59,0,0,0,0-17.18h-2.6l-22-80.51c30.32,7.56,65.45,19.77,99.28,39.52A17.78,17.78,0,0,0,311,365.38h18.33a17.77,17.77,0,0,0,17.75-17.75V182.69A17.77,17.77,0,0,0,329.31,164.94Zm-191.86,121c-8.08-.87-14.55-1.32-19-1.55a.53.53,0,0,1-.49-.57V246.52a.52.52,0,0,1,.49-.56c4.43-.23,10.9-.69,19-1.55ZM198.93,394h-18.4a.57.57,0,0,1-.55-.42l-23.91-87.92c6.13,1,12.74,2.13,19.74,3.55Zm131-46.39a.58.58,0,0,1-.57.58H311a.57.57,0,0,1-.57-.58V246.84a8.59,8.59,0,0,0-17.18,0v86C242.36,305.3,190,293.31,154.63,288.1V242.22C190,237,242.36,225,293.23,197.47v12.72a8.59,8.59,0,0,0,17.18,0v-27.5a.57.57,0,0,1,.57-.57h18.33a.58.58,0,0,1,.57.57V347.63Z' transform='translate(0 -36.65)'/><path d='M430.1,256.57h-55a8.6,8.6,0,0,0,0,17.19h55a8.6,8.6,0,0,0,0-17.19Z' transform='translate(0 -36.65)'/><path d='M419.46,206.34a8.6,8.6,0,0,0-11.53-3.84l-36.65,18.33a8.59,8.59,0,0,0,3.85,16.27,8.48,8.48,0,0,0,3.83-.91l36.66-18.32A8.6,8.6,0,0,0,419.46,206.34Z' transform='translate(0 -36.65)'/><path d='M415.62,312.46,379,294.13a8.59,8.59,0,0,0-7.69,15.37l36.66,18.33a8.59,8.59,0,0,0,7.68-15.37Z' transform='translate(0 -36.65)'/></svg>",title:h("Banner Advertisement","morenews"),description:h("You can easily place your promotional banners within your header section.","morenews"),section:"frontpage_advertisement_settings",control:"[section]"},{icon:"<svg id='Capa_1' data-name='Capa 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 285 229.96' ><path d='M275.14,179a5,5,0,0,0-2.27.51c-5.4,2.54-10.14,2.61-15.76,0a5,5,0,0,0-4.38,9l.16.07a28.09,28.09,0,0,0,24.24,0,5,5,0,0,0-2-9.55ZM265,196.29c-10,0-20,8.41-20,21.88,0,9.17,5.21,14.79,9,18.54s5.92,5.6,6,11.17a5,5,0,0,0,3.15,5h0l.08,0a5,5,0,0,0,6-2c3.54-3.67,5.79-6.82,7-9.79a20.38,20.38,0,0,0,1.29-8,9.67,9.67,0,0,1,.58-4.24c.6-1.49,2-3.77,5.46-7.22a5,5,0,0,0,1.46-3.54c0-13.47-10-21.88-20-21.88Zm0,10c4.66,0,9.26,2.54,9.88,10.17a30.61,30.61,0,0,0-6.08,8.76,20.39,20.39,0,0,0-1.3,7.95,10.34,10.34,0,0,1-.49,3.9,36.18,36.18,0,0,0-6-7.44c-3.75-3.75-6-5.63-6-11.46,0-9,5-11.88,10-11.88ZM265,31.8A5,5,0,0,0,260,36.41l-10,130a5,5,0,0,0,2.87,4.92,28.09,28.09,0,0,0,24.24,0,5,5,0,0,0,2.85-4.91l-10-130a5,5,0,0,0-5-4.61ZM265,102l4.73,61.44a16,16,0,0,1-9.45,0L265,102Zm-120,75.5a25,25,0,1,0,25,25A25.07,25.07,0,0,0,145,177.48Zm0,10a15,15,0,1,1-15,15A14.93,14.93,0,0,1,145,187.48Zm-70-20a25,25,0,1,0,25,25A25.07,25.07,0,0,0,75,167.48Zm0,10a15,15,0,1,1-15,15A14.93,14.93,0,0,1,75,177.48Zm-30-60a25,25,0,1,0,25,25A25.07,25.07,0,0,0,45,117.48Zm0,10a15,15,0,1,1-15,15A14.93,14.93,0,0,1,45,127.48Zm40-60a25,25,0,1,0,25,25A25.07,25.07,0,0,0,85,67.48Zm0,10a15,15,0,1,1-15,15A14.93,14.93,0,0,1,85,77.48Zm60-10a25,25,0,1,0,25,25A25.07,25.07,0,0,0,145,67.48Zm0,10a15,15,0,1,1-15,15A14.93,14.93,0,0,1,145,77.48Zm-29.21-50a117.38,117.38,0,0,0-43.25,8.09A115,115,0,1,0,198.7,221.35a5,5,0,0,0,.52-6.2h0c-24.14-36.37-16.55-61.35-6.57-84.27,5-11.46,10.82-22.14,13.48-33.18s1.55-22.91-7.22-33.83C180,40.31,148.14,27.54,115.79,27.53Zm-.11,10.19c29.63.15,58.91,11.83,75.44,32.43,7,8.69,7.5,16.08,5.29,25.22s-7.72,19.6-12.92,31.53c-10.07,23.14-17.93,52.87,5.22,90.26a105.09,105.09,0,1,1-73-179.44Z' transform='translate(0 -27.52)' /></svg>",title:h("Color Options","morenews"),description:h("Change the color setting provide with the Theme.","morenews"),section:"colors",control:"[section]"},{icon:"<svg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 438.7'><path d='M485.08,0H26.92A27,27,0,0,0,0,26.92V411.78A27,27,0,0,0,26.92,438.7H485.08A27,27,0,0,0,512,411.78V26.92A27,27,0,0,0,485.08,0ZM127.14,17.18h331V36.66h-331Zm-36.65,0H110V36.66H90.49Zm-36.66,0H73.31V36.66H53.83ZM17.18,26.92a9.75,9.75,0,0,1,9.74-9.74h9.73V36.66H17.18ZM494.82,411.78a9.75,9.75,0,0,1-9.74,9.74H26.92a9.75,9.75,0,0,1-9.74-9.74V53.84H494.82Zm0-375.12H475.35V17.18h9.73a9.75,9.75,0,0,1,9.74,9.74ZM421,147.43H302.65c-1.34,0-2.42,3.7-2.42,8.26s1.08,8.25,2.42,8.25H421c1.33,0,2.41-3.7,2.41-8.25S422.3,147.43,421,147.43Zm-19.62,33.25h-99.1c-1.11,0-2,3.69-2,8.25s.91,8.26,2,8.26h99.1c1.12,0,2-3.7,2-8.26S402.47,180.68,401.35,180.68ZM199.57,305.77H81.25c-1.33,0-2.41,3.7-2.41,8.26s1.08,8.25,2.41,8.25H199.57c1.34,0,2.42-3.69,2.42-8.25S200.91,305.77,199.57,305.77ZM180,339H80.86c-1.11,0-2,3.69-2,8.25s.91,8.26,2,8.26H180c1.12,0,2-3.7,2-8.26S181.08,339,180,339Zm61.15,53.81H58.39a8.5,8.5,0,0,1-8.5-8.5V91a8.5,8.5,0,0,1,8.5-8.5H241.11a8.5,8.5,0,0,1,8.5,8.5v293.3A8.5,8.5,0,0,1,241.11,392.83Zm-174.22-17H232.61V99.53H66.89Zm386.72-146H278a8.5,8.5,0,0,1-8.5-8.5V91a8.49,8.49,0,0,1,8.5-8.5h175.6a8.5,8.5,0,0,1,8.5,8.5V221.36A8.51,8.51,0,0,1,453.61,229.86Zm-167.1-17h158.6V99.53H286.51ZM421,310.4H302.65c-1.34,0-2.42,3.7-2.42,8.26s1.08,8.25,2.42,8.25H421c1.33,0,2.41-3.69,2.41-8.25S422.3,310.4,421,310.4Zm-19.62,33.25h-99.1c-1.11,0-2,3.69-2,8.25s.91,8.26,2,8.26h99.1c1.12,0,2-3.7,2-8.26S402.47,343.65,401.35,343.65Zm52.26,49.18H278a8.49,8.49,0,0,1-8.5-8.5V254a8.5,8.5,0,0,1,8.5-8.5h175.6a8.51,8.51,0,0,1,8.5,8.5V384.33A8.5,8.5,0,0,1,453.61,392.83Zm-167.1-17h158.6V262.5H286.51Z'/></svg>",title:h("Main Banner Options","morenews"),description:h("Selecting different categories can be done for different section.","morenews"),section:"frontpage_main_banner_section_settings",control:"[section]"},{icon:"<svg id='Capa_1' data-name='Capa 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 351.53 362.25' > <path d='M333.31,0H81.46A23.59,23.59,0,0,0,57.89,23.57V81.39H28.94A23.6,23.6,0,0,0,5.36,105V338.68a23.6,23.6,0,0,0,23.58,23.57H333.31a23.6,23.6,0,0,0,23.58-23.57V23.57A23.6,23.6,0,0,0,333.31,0ZM21.68,338.67V105a7.27,7.27,0,0,1,7.26-7.26h29v241a7.27,7.27,0,0,1-7.26,7.26H28.94A7.28,7.28,0,0,1,21.68,338.67Zm318.9,0a7.27,7.27,0,0,1-7.27,7.26H73.07c.17-.54.26-1.11.4-1.68s.22-.83.3-1.26a23.7,23.7,0,0,0,.44-4.32V23.57a7.26,7.26,0,0,1,7.25-7.25H333.31a7.26,7.26,0,0,1,7.26,7.25v315.1Z' transform='translate(-5.36)' /> <path d='M303.56,69.71H115A8.16,8.16,0,1,0,115,86H303.56a8.16,8.16,0,1,0,0-16.31Z' transform='translate(-5.36)' /> <path d='M303.56,113.22H115a8.16,8.16,0,0,0,0,16.32H303.56a8.16,8.16,0,1,0,0-16.32Z' transform='translate(-5.36)' /> <path d='M303.56,156.73H115a8.16,8.16,0,1,0,0,16.32H303.56a8.16,8.16,0,1,0,0-16.32Z' transform='translate(-5.36)' /> <path d='M303.56,200.24H115a8.16,8.16,0,0,0,0,16.32H303.56a8.16,8.16,0,1,0,0-16.32Z' transform='translate(-5.36)' /> <path d='M303.56,243.76H115a8.16,8.16,0,0,0,0,16.32H303.56a8.16,8.16,0,1,0,0-16.32Z' transform='translate(-5.36)' /> <path d='M303.56,287.27H115a8.16,8.16,0,0,0,0,16.32H303.56a8.16,8.16,0,0,0,0-16.32Z' transform='translate(-5.36)' /> </svg>",title:h("Frontpage Option","morenews"),description:h("Frontpage layout with Popular Tag, Breaking News, Feature News can be set.","morenews"),section:"frontpage_option_panel",control:"[panel]"},{icon:"<svg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 438.7'><path d='M485.08,0H26.92A27,27,0,0,0,0,26.92V411.78A27,27,0,0,0,26.92,438.7H485.08A27,27,0,0,0,512,411.78V26.92A27,27,0,0,0,485.08,0ZM127.14,17.18h331V36.66h-331Zm-36.65,0H110V36.66H90.49Zm-36.66,0H73.31V36.66H53.83ZM17.18,26.92a9.75,9.75,0,0,1,9.74-9.74h9.73V36.66H17.18ZM494.82,411.78a9.75,9.75,0,0,1-9.74,9.74H26.92a9.75,9.75,0,0,1-9.74-9.74V53.84H494.82Zm0-375.12H475.35V17.18h9.73a9.75,9.75,0,0,1,9.74,9.74ZM455.06,210.45H56.94A14.93,14.93,0,0,1,42,195.54V91.73a14.93,14.93,0,0,1,14.91-14.9H455.06A14.93,14.93,0,0,1,470,91.73V195.54A14.93,14.93,0,0,1,455.06,210.45Zm-398-15H455V91.83H57ZM223.48,398.53H58.85A14.92,14.92,0,0,1,44,383.63V256.33a14.92,14.92,0,0,1,14.9-14.91H223.48a14.93,14.93,0,0,1,14.9,14.91v127.3A14.92,14.92,0,0,1,223.48,398.53ZM59,383.53H223.38V256.42H59Zm143.94-56H79.44a7.5,7.5,0,0,1,0-15H202.89a7.5,7.5,0,0,1,0,15Zm0,33.83H79.44a7.5,7.5,0,0,1,0-15H202.89a7.5,7.5,0,1,1,0,15ZM452,398.53H287.37a14.92,14.92,0,0,1-14.9-14.9V256.33a14.93,14.93,0,0,1,14.9-14.91H452a14.92,14.92,0,0,1,14.9,14.91v127.3A14.92,14.92,0,0,1,452,398.53Zm-164.53-15H451.9V256.42H287.47Zm144-56H308a7.5,7.5,0,0,1,0-15H431.42a7.5,7.5,0,1,1,0,15Zm0,33.83H308a7.5,7.5,0,0,1,0-15H431.42a7.5,7.5,0,0,1,0,15Z'/></svg>",title:h("Static Front Page","morenews"),description:h("Set the Homepage as a static page to create own homepage using widgets.","morenews"),section:"static_front_page",control:"[section]"},{icon:"<svg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 304' > <path d='M360,48H40A40.12,40.12,0,0,0,0,88V312a40.12,40.12,0,0,0,40,40H360a40.12,40.12,0,0,0,40-40V88A40.12,40.12,0,0,0,360,48ZM256,336H40a24.07,24.07,0,0,1-24-24V88A24.07,24.07,0,0,1,40,64H256Zm128-24a24.07,24.07,0,0,1-24,24H272V64h88a24.07,24.07,0,0,1,24,24Z' transform='translate(0 -48)' /> <path d='M48,108h8a8,8,0,0,0,0-16H48a8,8,0,0,0,0,16Z' transform='translate(0 -48)' /> <path d='M84,108h8a8,8,0,0,0,0-16H84a8,8,0,0,0,0,16Z' transform='translate(0 -48)' /> <path d='M120,108h8a8,8,0,0,0,0-16h-8a8,8,0,0,0,0,16Z' transform='translate(0 -48)' /> <path d='M340,116H312a8,8,0,0,0,0,16h28a8,8,0,0,0,0-16Z' transform='translate(0 -48)' /> <path d='M340,156H312a8,8,0,0,0,0,16h28a8,8,0,0,0,0-16Z' transform='translate(0 -48)' /> <path d='M340,196H312a8,8,0,0,0,0,16h28a8,8,0,0,0,0-16Z' transform='translate(0 -48)' /> </svg>",title:h("Widgets Area","morenews"),description:h("Widgets can be added on all the section provide by the Theme.","morenews"),section:"widgets",control:"[panel]"},{icon:"<svg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' > <path d='M93.24,221H265.7a8.09,8.09,0,0,0,0-16.17H93.24a8.09,8.09,0,1,0,0,16.17Z' /> <path d='M93.24,177.85h97a8.09,8.09,0,0,0,0-16.17h-97a8.09,8.09,0,1,0,0,16.17Z' /> <path d='M114.8,398.28H395.05a29.68,29.68,0,0,0,29.64-29.64V277.56a29.59,29.59,0,0,0-4-14.82,8.08,8.08,0,1,0-14,8.09,13.5,13.5,0,0,1,1.79,6.73v91.08a13.49,13.49,0,0,1-13.47,13.48H114.8a13.5,13.5,0,0,1-13.48-13.48V277.56a13.49,13.49,0,0,1,13.48-13.47H265.7a8.09,8.09,0,0,0,0-16.17H114.8a29.68,29.68,0,0,0-29.65,29.64v91.08A29.68,29.68,0,0,0,114.8,398.28Z' /> <path d='M477.07,412.24a51.15,51.15,0,0,0,2.59-16.11V159a51.25,51.25,0,0,0-51.2-51.2H382.65V29.64A29.67,29.67,0,0,0,353,0H331.45a29.68,29.68,0,0,0-29.64,29.64v78.15H83.54A51.26,51.26,0,0,0,32.34,159V396.13a51.15,51.15,0,0,0,2.59,16.11A51.21,51.21,0,0,0,51.2,512H460.8a51.21,51.21,0,0,0,16.27-99.76ZM318,29.64a13.48,13.48,0,0,1,13.47-13.47H353a13.49,13.49,0,0,1,13.48,13.47V43.12H318V29.64Zm48.5,29.64V247.92H350.32V59.28Zm-48.5,0h16.17V247.92H318Zm0,204.8h48.5v20.66a2.75,2.75,0,0,1-.53,1.62L344.39,315.1a2.7,2.7,0,0,1-4.32,0l-21.55-28.74a2.7,2.7,0,0,1-.54-1.62V264.08ZM48.51,159a35.06,35.06,0,0,1,35-35H301.81v37.72H244.14a8.09,8.09,0,0,0,0,16.17h57.67V284.74a19,19,0,0,0,3.77,11.32l21.56,28.74a18.86,18.86,0,0,0,30.18,0l21.56-28.74a18.93,18.93,0,0,0,3.78-11.32V221h33.95a8.09,8.09,0,0,0,0-16.17H382.66v-27h33.95a8.09,8.09,0,0,0,0-16.17H382.66V124h45.81a35.07,35.07,0,0,1,35,35V396.13a35.08,35.08,0,0,1-35,35H83.54a35.07,35.07,0,0,1-35-35ZM460.8,495.83H51.2a35,35,0,0,1-8.61-69,51.18,51.18,0,0,0,41,20.49H428.46a51.18,51.18,0,0,0,41-20.49,35,35,0,0,1-8.61,69Z' /> <path d='M235.52,463.49h-1.08a8.09,8.09,0,0,0,0,16.17h1.08a8.09,8.09,0,0,0,0-16.17Z' /> <path d='M433.85,463.49H272.17a8.09,8.09,0,1,0,0,16.17H433.85a8.09,8.09,0,1,0,0-16.17Z' /> <path d='M202.1,463.49H201a8.09,8.09,0,1,0,0,16.17h1.07a8.09,8.09,0,1,0,0-16.17Z' /> <path d='M164.38,463.49H78.15a8.09,8.09,0,1,0,0,16.17h86.23a8.09,8.09,0,0,0,0-16.17Z' /> </svg>",title:h("Archive Options","morenews"),description:h("Customize the blog layout desgin.","morenews"),section:"site_archive_settings",control:"[section]"},{icon:"<svg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 511.96'><path d='M475.69,0a36.2,36.2,0,0,0-33.45,22.25l-32.64,78c-9.73-9.15-22.42-14.93-36.27-14.93h-320A53.41,53.41,0,0,0,0,138.65v320A53.41,53.41,0,0,0,53.33,512h320a53.41,53.41,0,0,0,53.34-53.33V225.15L508.59,52.33a37.05,37.05,0,0,0,3.41-16A36.34,36.34,0,0,0,475.69,0ZM405.33,458.64a32,32,0,0,1-32,32h-320a32,32,0,0,1-32-32v-320a32,32,0,0,1,32-32h320a31.75,31.75,0,0,1,27.07,15.58l-37.73,90.19V160A10.67,10.67,0,0,0,352,149.31H74.67A10.67,10.67,0,0,0,64,160v85.33A10.67,10.67,0,0,0,74.67,256H344.43l-8.94,21.33H245.33A10.67,10.67,0,0,0,234.67,288V416a7.81,7.81,0,0,0,.15.79c-8.77,7.78-14.15,10.39-14.53,10.54a10.66,10.66,0,0,0-3.84,17.55c15.44,15.45,36.75,21.34,57.6,21.34,26.64,0,52.48-9.65,64.12-21.34,16.77-16.77,29.06-50,19.78-74.77l47.38-99.93V458.64ZM270.64,397.52c2.94-9.68,5.73-18.85,14.22-27.34,15.09-15.09,33.84-15.09,48.92,0,13.51,13.5,3.2,45.71-10.67,59.58-11.56,11.54-52.67,22.68-80.25,8.26a138.23,138.23,0,0,0,12.67-11.33C264.45,417.75,267.61,407.47,270.64,397.52ZM256,375.34V298.67h70.57l-16.36,39.08c-14.25-.26-28.57,5.48-40.45,17.39A66.08,66.08,0,0,0,256,375.34Zm75.26-32.6L360,274.11,376.11,282l-32.56,68.65A60.75,60.75,0,0,0,331.26,342.74Zm10.07-172.09v64h-256v-64Zm148-127.41L385.22,262.76l-17-8.34,54.08-129.24,39.62-94.67a14.88,14.88,0,0,1,13.8-9.15c8.24,0,14.94,6.69,14.94,15.65A14.9,14.9,0,0,1,489.28,43.24Z' transform='translate(0 -0.02)'/><path d='M181.33,277.31H74.67A10.67,10.67,0,0,0,64,288V437.31A10.67,10.67,0,0,0,74.67,448H181.33A10.67,10.67,0,0,0,192,437.31V288A10.67,10.67,0,0,0,181.33,277.31ZM170.67,426.64H85.33v-128h85.34v128Z' transform='translate(0 -0.02)'/></svg>",title:h("Theme Option","morenews"),description:h("Set the Global Layout, Breadcrumb, Sidebar, Single Post, You may have missed and more.","morenews"),section:"theme_option_panel",control:"[panel]"},{icon:"<svg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20.75 16.76' > <path d='M18.37,18.38h-16a1.84,1.84,0,0,1-2-1.61V4a1.83,1.83,0,0,1,2-1.6h16a1.83,1.83,0,0,1,2,1.6v12.8A1.84,1.84,0,0,1,18.37,18.38Z' transform='translate(0 -1.99)' fill='none' stroke='#2271b1' strokeMiterlimit='10' strokeWidth='0.75' /> <line x1='20.38' y1='12.38' x2='0.38' y2='12.38' fill='none' stroke='#2271b1' strokeMiterlimit='10' strokeWidth='0.75' /> </svg>",title:h("Footer Options","morenews"),description:h("Set the footer type, number of columns, spacing and colors.","morenews"),section:"site_footer_settings",control:"[section]"}],{__:g}=wp.i18n;var y=()=>React.createElement("ul",{className:"aft-first-steps aft-dashboard-grid-container"},m&&m.map(e=>{e.icon;return React.createElement("li",null,React.createElement("div",{className:"aft-header"},React.createElement("div",{dangerouslySetInnerHTML:{__html:e.icon}}),React.createElement("h4",null,e.title)),React.createElement("p",null,e.description),React.createElement("a",{href:`${afDashboardData.customizer_url}${encodeURI(`${e.control}=${e.section}`)}`,target:"_blank",className:"aft-button"},g("Go to option","morenews")))}));const{__:b}=wp.i18n;var v=function({changelog:e}){return React.createElement("div",{className:"aft-change-log-wrapper"},e.split(/\r?\n/).map((function(e,t){let r=e.replaceAll("\n",""),n="",i="";return r.match(/[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/)?(i="h2",n="version"):r.includes("Fixed")||e.includes("fixes")?(i="span",n="fixed_release"):r.includes("Update")?(i="span",n="update_release"):(i="span",n="new_release"),React.createElement(React.Fragment,null,r&&React.createElement(i,{className:n,key:t},r))})))};const{__:w}=wp.i18n,_=[{features:w("Starter Sites Import","morenews"),free:"yes",pro:"yes"},{features:w("Live Editing in Customizer","morenews"),free:"yes",pro:"yes"},{features:w("Dark/Light Site Mode","morenews"),free:"yes",pro:"yes"},{features:w("Dark/Light Frontend Switch","morenews"),free:"no-alt",pro:"yes"},{features:w("Responsive Layout","morenews"),free:"yes",pro:"yes"},{features:w("Disable WordPress Emojis (GDPR Friendly)","morenews"),free:"yes",pro:"yes"},{features:w("Basic Typography Style","morenews"),free:"yes",pro:"yes"},{features:w("Advanced Typography Style (Google & System Fonts - GDPR Friendly)","morenews"),free:"no-alt",pro:"yes"},{features:w("Basic Color Controls","morenews"),free:"yes",pro:"yes"},{features:w("Advanced Color Controls (Global Options)","morenews"),free:"no-alt",pro:"yes"},{features:w("Category Color Options","morenews"),free:"no-alt",pro:"yes"},{features:w("Hide Theme Credit Link","morenews"),free:"no-alt",pro:"yes"},{features:w("Custom CSS Output Minified","morenews"),free:"yes",pro:"yes"},{features:w("Custom CSS Output Optimized with Mobile Spacing","morenews"),free:"no-alt",pro:"yes"},{features:w("Improved Font Loading Partial","morenews"),free:"yes",pro:"yes"},{features:w("Improved Font Loading System/Google Switch + Preconnect","morenews"),free:"no-alt",pro:"yes"},{features:w("Top Header Menu","morenews"),free:"no-alt",pro:"yes"},{features:w("Multiple Header Layouts (10+ including Centered & Compressed)","morenews"),free:"no-alt",pro:"yes"},{features:w("Sticky Header Option Basic","morenews"),free:"yes",pro:"yes"},{features:w("Sticky Header Option Advanced Behavior & Layout Support","morenews"),free:"no-alt",pro:"yes"},{features:w("Header Advertisement Widget Area","morenews"),free:"no-alt",pro:"yes"},{features:w("Frontpage Banner Layouts Basic (5+ Layouts)","morenews"),free:"yes",pro:"yes"},{features:w("Frontpage Banner Layouts Advanced (30+ Layouts)","morenews"),free:"no-alt",pro:"yes"},{features:w("Banner Post Filter: Category & Tag","morenews"),free:"yes",pro:"yes"},{features:w("Banner Post Filter: Views & Comments","morenews"),free:"no-alt",pro:"yes"},{features:w("Above/Below Banner Widget Areas","morenews"),free:"no-alt",pro:"yes"},{features:w("Featured Videos Section (Custom Field + Responsive)","morenews"),free:"no-alt",pro:"yes"},{features:w("Express / 3 Column Posts Section","morenews"),free:"no-alt",pro:"yes"},{features:w("Posts Carousel Section","morenews"),free:"no-alt",pro:"yes"},{features:w("Reading Progress Bar","morenews"),free:"no-alt",pro:"yes"},{features:w("Post Subtitle Field","morenews"),free:"no-alt",pro:"yes"},{features:w("Source & Sponsor Custom Field","morenews"),free:"no-alt",pro:"yes"},{features:w("Post Badge Taxonomy (Breaking, Premium)","morenews"),free:"no-alt",pro:"yes"},{features:w("Post Reaction Feature (Animated, Localized, Color Custom)","morenews"),free:"no-alt",pro:"yes"},{features:w("Author Box Basic","morenews"),free:"yes",pro:"yes"},{features:w("Author Box with Donate Link, Website & Author Posts","morenews"),free:"no-alt",pro:"yes"},{features:w("Trending Posts in Single Post","morenews"),free:"no-alt",pro:"yes"},{features:w("Jetpack Share Buttons Placement","morenews"),free:"no-alt",pro:"yes"},{features:w("Mailchimp Subscribe Box","morenews"),free:"no-alt",pro:"yes"},{features:w("Archive Post Layouts List & Full","morenews"),free:"yes",pro:"yes"},{features:w("Archive Post Layouts Grid, Tile, Masonry, List Alt, Right, Mixed","morenews"),free:"no-alt",pro:"yes"},{features:w("Category-wise Post Layout (Global Only)","morenews"),free:"yes",pro:"yes"},{features:w("Category-wise Post Layout (Category Specific)","morenews"),free:"no-alt",pro:"yes"},{features:w("Pagination Numeric","morenews"),free:"yes",pro:"yes"},{features:w("Pagination Ajax Load More & Infinite Scroll","morenews"),free:"no-alt",pro:"yes"},{features:w("Basic Custom Widgets (5+ Layouts)","morenews"),free:"yes",pro:"yes"},{features:w("Advanced Custom Widgets Count (15+ Layouts)","morenews"),free:"no-alt",pro:"yes"},{features:w("Section Title Designs (Multiple Styles)","morenews"),free:"no-alt",pro:"yes"},{features:w("Trending Posts Carousel (Tag, Views)","morenews"),free:"no-alt",pro:"yes"},{features:w("YouTube Video Slider Widget","morenews"),free:"no-alt",pro:"yes"},{features:w("Post Express / Grid / Tabbed Widgets","morenews"),free:"no-alt",pro:"yes"},{features:w("Centralized Advertisement Support","morenews"),free:"no-alt",pro:"yes"},{features:w("Rotating Ads with Split Option","morenews"),free:"no-alt",pro:"yes"},{features:w("Ad Placement Areas (Header, Content, Footer, Archive)","morenews"),free:"no-alt",pro:"yes"},{features:w("Centralized Ads Management Panel","morenews"),free:"no-alt",pro:"yes"},{features:w("Widget Areas Header, Frontpage, Sidebar, Footer","morenews"),free:"yes",pro:"yes"},{features:w("Widget Areas Banner, Featured, Inline","morenews"),free:"no-alt",pro:"yes"},{features:w("Advanced Widgets (Carousel, Express, Tabs)","morenews"),free:"no-alt",pro:"yes"},{features:w("Gutenberg & Page Builders Compatibility","morenews"),free:"yes",pro:"yes"},{features:w("Multilingual & RTL Support","morenews"),free:"yes",pro:"yes"},{features:w("WooCommerce Support","morenews"),free:"yes",pro:"yes"},{features:w("SEO Ready Basic","morenews"),free:"yes",pro:"yes"},{features:w("SEO Ready with Open Graph Enhancements","morenews"),free:"no-alt",pro:"yes"},{features:w("Standard Support","morenews"),free:"yes",pro:"yes"},{features:w("Priority Support","morenews"),free:"no-alt",pro:"yes"}],x=w("Upgrade to Pro","morenews"),{__:E}=wp.i18n;var S=()=>React.createElement("section",{className:"af-comparison-table"},React.createElement("ul",null,React.createElement("li",{className:"af-table-heading"},React.createElement("span",null,E("Features","morenews")),React.createElement("span",null,E("Free","morenews")),React.createElement("span",null,E("Pro","morenews"))),_.map((e,t)=>React.createElement("li",null,React.createElement("span",null,e.features),React.createElement("span",{className:"dashicons dashicons-"+e.free}),React.createElement("span",{className:"dashicons dashicons-"+e.pro})))),React.createElement("div",{className:"upgrade-pro-button"},React.createElement("a",{target:"_blank",href:"https://afthemes.com/products/morenews-pro/",className:"button button-primary"},x))),k=r(7),T=r.n(k);r(75);function C(){return i.a.createElement("div",{className:"spinner-container"},i.a.createElement("div",{className:"loading-spinner"}))}var A=function({plugins:e,isLoading:t}){return React.createElement("div",{className:"aft-dashboard-grid-container"},t?e.map(e=>React.createElement(React.Fragment,null,T()(e))):React.createElement(C,null))};const{__:L}=wp.i18n,{apiFetch:N}=wp;var P=function(){const[e,t]=Object(n.useState)([]),[r,i]=Object(n.useState)(!1);Object(n.useEffect)(()=>{a()},[]);const a=async()=>{let{plugins:e}=await N({path:"aft-useful-plugins/v1/get-useful-plugins?plug="+JSON.stringify(u),methods:"GET"});t(e),i(!0)};return React.createElement(React.Fragment,null,r?e.map(e=>React.createElement(React.Fragment,null,T()(e))):"Loading...")};const{__:R}=wp.i18n,D=[{title:R("Documentation","morenews"),linkText:R("View","morenews"),textUrl:"https://docs.afthemes.com/morenews/",shortdesc:R("Please check our full documentation for detailed information on how to setup and customize the theme.","morenews")},{title:R("Support","morenews"),linkText:R("Support","morenews"),textUrl:"https://afthemes.com/supports/",shortdesc:R("Got theme support question or found bug or got some feedbacks? Best place to ask your query is the dedicated Support forum for the theme..","morenews")},{title:R("Video  Tutorials","morenews"),linkText:R("Video","morenews"),textUrl:"https://www.youtube.com/watch?v=Z5MdDCRhxz4&list=PL8nUD79gscmgfXEtuVz1as47b6RcSXPr6",shortdesc:R("Watch out the videos for an easy learning on Customizing the Theme .","morenews")}];var q=function(){return React.createElement("div",{className:"aft-sidebar-content"},D&&D.map(e=>React.createElement("div",{className:"aft-sidebar-content-item"},React.createElement("h2",null,e.title),React.createElement("span",null,e.shortdesc),React.createElement("a",{href:e.textUrl,target:"_blank"},e.linkText))))};const{__:M}=wp.i18n,{apiFetch:O}=wp;var I=()=>{const[e,t]=Object(n.useState)("home"),[r,i,a,o]=Object(n.useState)([]),[s,u]=Object(n.useState)(!1),c=e=>{sessionStorage.setItem("nav",e),t(e)};Object(n.useEffect)(()=>{f()},[]);const f=async()=>{let{plugins:e}=await O({path:"aft-useful-plugins/v1/get-useful-plugins?plug="+JSON.stringify(l),methods:"GET"});i(e),u(!0)};return React.createElement("div",{className:"aft-dashboard-wrapper"},React.createElement(d,null),React.createElement("div",{className:"aft-dashboard-main-section aft-dashbord-container"},React.createElement("div",{className:"settings-tab-content-wrapper"},React.createElement("ul",{className:"settings-nav"},React.createElement("li",{className:"navbar-item settings "+("home"==e?"active":"inactive"),onClick:e=>c("home")},React.createElement("a",null,M("Get started","morenews"))),React.createElement("li",{className:"navbar-item  "+("useful-plugins"==e?"active":"inactive"),onClick:e=>c("useful-plugins")},React.createElement("a",null,M("Useful Plugins","morenews"))),React.createElement("li",{className:"navbar-item  "+("free-pro"==e?"active":"inactive"),onClick:e=>c("free-pro")},React.createElement("a",null,M("Free vs Pro","morenews"))),React.createElement("li",{className:"navbar-item  "+("change-log"==e?"active":"inactive"),onClick:e=>c("change-log")},React.createElement("a",null,M("Change Log","morenews")))),"home"==e&&React.createElement(y,null),"change-log"==e&&React.createElement(v,{changelog:afDashboardData.changelog}),"free-pro"==e&&React.createElement(S,null),"useful-plugins"==e&&React.createElement(A,{plugins:r,isLoading:s})),React.createElement("div",{className:"aft-dashboard-sidebar"},React.createElement(P,null),React.createElement(q,null))))};document.addEventListener("DOMContentLoaded",()=>{var e="af-theme-dashboard";void 0!==document.getElementById(e)&&null!==document.getElementById(e)&&o.a.render(i.a.createElement(I,null),document.getElementById(e))})},,function(e,t){}]);