/**
 * Theme JavaScript functionality
 * Dakoii Provincial Government Theme
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        
        // Mobile menu functionality
        initMobileMenu();
        
        // Smooth scrolling for anchor links
        initSmoothScrolling();
        
        // Back to top button
        initBackToTop();
        
        // Animation delays for elements
        initAnimationDelays();
        
        // Search form enhancements
        initSearchEnhancements();
        
        // Comment form enhancements
        initCommentFormEnhancements();
        
        // Cultural pattern animations
        initCulturalAnimations();
    });

    /**
     * Mobile menu functionality
     */
    function initMobileMenu() {
        const menuToggle = document.querySelector('.menu-toggle');
        const navMenu = document.querySelector('.nav-menu');
        const body = document.body;
        
        if (menuToggle && navMenu) {
            menuToggle.addEventListener('click', function() {
                const expanded = this.getAttribute('aria-expanded') === 'true' || false;
                
                // Toggle aria-expanded
                this.setAttribute('aria-expanded', !expanded);
                
                // Toggle menu visibility
                navMenu.classList.toggle('is-open');
                body.classList.toggle('menu-open');
                
                // Toggle menu icon
                const menuIcon = this.querySelector('.menu-icon');
                if (menuIcon) {
                    menuIcon.textContent = expanded ? '☰' : '✕';
                }
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!menuToggle.contains(e.target) && !navMenu.contains(e.target)) {
                    menuToggle.setAttribute('aria-expanded', 'false');
                    navMenu.classList.remove('is-open');
                    body.classList.remove('menu-open');
                    
                    const menuIcon = menuToggle.querySelector('.menu-icon');
                    if (menuIcon) {
                        menuIcon.textContent = '☰';
                    }
                }
            });
            
            // Close menu on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && navMenu.classList.contains('is-open')) {
                    menuToggle.setAttribute('aria-expanded', 'false');
                    navMenu.classList.remove('is-open');
                    body.classList.remove('menu-open');
                    menuToggle.focus();
                }
            });
        }
    }

    /**
     * Smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        
        anchorLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                const targetId = this.getAttribute('href');

                // Skip if targetId is empty, just '#', or invalid
                if (!targetId || targetId === '#' || targetId.length <= 1) {
                    return;
                }

                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    e.preventDefault();

                    const headerElement = document.querySelector('.site-header');
                    const navElement = document.querySelector('.main-nav');

                    const headerHeight = headerElement ? headerElement.offsetHeight : 0;
                    const navHeight = navElement ? navElement.offsetHeight : 0;
                    const offset = headerHeight + navHeight + 20;

                    const targetPosition = targetElement.offsetTop - offset;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // Update focus for accessibility
                    targetElement.focus();
                }
            });
        });
    }

    /**
     * Back to top button functionality
     */
    function initBackToTop() {
        const backToTop = document.getElementById('back-to-top');
        
        if (backToTop) {
            // Show/hide based on scroll position
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTop.style.display = 'block';
                    setTimeout(function() {
                        backToTop.style.opacity = '1';
                    }, 10);
                } else {
                    backToTop.style.opacity = '0';
                    setTimeout(function() {
                        if (window.pageYOffset <= 300) {
                            backToTop.style.display = 'none';
                        }
                    }, 300);
                }
            });
            
            // Smooth scroll to top
            backToTop.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    }

    /**
     * Animation delays for staggered entrance effects
     */
    function initAnimationDelays() {
        const animatedElements = document.querySelectorAll('.post, .widget, article, .related-post');
        
        animatedElements.forEach(function(element, index) {
            element.style.animationDelay = (index * 0.1) + 's';
        });
    }

    /**
     * Search form enhancements
     */
    function initSearchEnhancements() {
        const searchForms = document.querySelectorAll('.search-form');
        
        searchForms.forEach(function(form) {
            const searchInput = form.querySelector('.search-field');
            const searchSubmit = form.querySelector('.search-submit');
            
            if (searchInput && searchSubmit) {
                // Add loading state on form submission
                form.addEventListener('submit', function() {
                    searchSubmit.disabled = true;
                    searchSubmit.value = 'Searching...';
                });
                
                // Clear button functionality
                if (searchInput.value) {
                    addClearButton(searchInput);
                }
                
                searchInput.addEventListener('input', function() {
                    if (this.value) {
                        addClearButton(this);
                    } else {
                        removeClearButton(this);
                    }
                });
            }
        });
    }

    /**
     * Add clear button to search input
     */
    function addClearButton(input) {
        if (input.parentNode.querySelector('.search-clear')) {
            return;
        }
        
        const clearButton = document.createElement('button');
        clearButton.type = 'button';
        clearButton.className = 'search-clear';
        clearButton.innerHTML = '✕';
        clearButton.title = 'Clear search';
        
        clearButton.addEventListener('click', function() {
            input.value = '';
            input.focus();
            removeClearButton(input);
        });
        
        input.parentNode.appendChild(clearButton);
    }

    /**
     * Remove clear button from search input
     */
    function removeClearButton(input) {
        const clearButton = input.parentNode.querySelector('.search-clear');
        if (clearButton) {
            clearButton.remove();
        }
    }

    /**
     * Comment form enhancements
     */
    function initCommentFormEnhancements() {
        const commentForm = document.getElementById('commentform');
        
        if (commentForm) {
            const requiredFields = commentForm.querySelectorAll('[required]');
            
            // Add visual indicators for required fields
            requiredFields.forEach(function(field) {
                const label = commentForm.querySelector('label[for="' + field.id + '"]');
                if (label && !label.querySelector('.required-indicator')) {
                    const indicator = document.createElement('span');
                    indicator.className = 'required-indicator';
                    indicator.innerHTML = ' *';
                    indicator.style.color = 'var(--png-red)';
                    label.appendChild(indicator);
                }
            });
            
            // Form validation feedback
            commentForm.addEventListener('submit', function(e) {
                let isValid = true;
                
                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        field.style.borderColor = 'var(--png-red)';
                        isValid = false;
                    } else {
                        field.style.borderColor = '';
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    const firstInvalidField = commentForm.querySelector('[style*="border-color"]');
                    if (firstInvalidField) {
                        firstInvalidField.focus();
                    }
                }
            });
        }
    }

    /**
     * Cultural pattern animations and effects
     */
    function initCulturalAnimations() {
        // Add hover effects to cultural patterns
        const culturalPatterns = document.querySelectorAll('.cultural-pattern');
        
        culturalPatterns.forEach(function(pattern) {
            pattern.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.transition = 'transform 0.3s ease';
            });
            
            pattern.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
        
        // Animate logo icon on scroll
        const logoIcon = document.querySelector('.logo-icon');
        if (logoIcon) {
            let lastScrollY = window.pageYOffset;
            
            window.addEventListener('scroll', function() {
                const currentScrollY = window.pageYOffset;
                
                if (currentScrollY > lastScrollY) {
                    // Scrolling down
                    logoIcon.style.transform = 'rotate(5deg)';
                } else {
                    // Scrolling up
                    logoIcon.style.transform = 'rotate(-5deg)';
                }
                
                setTimeout(function() {
                    logoIcon.style.transform = 'rotate(0deg)';
                }, 200);
                
                lastScrollY = currentScrollY;
            });
        }
    }

    /**
     * Intersection Observer for fade-in animations
     */
    function initIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });
            
            const elementsToObserve = document.querySelectorAll('.widget, .post, article');
            elementsToObserve.forEach(function(element) {
                observer.observe(element);
            });
        }
    }

    // Initialize intersection observer
    initIntersectionObserver();

})();
