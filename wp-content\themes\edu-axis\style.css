/*!
Theme Name: Edu Axis
Theme URI: https://refreshthemes.com/downloads/edu-axis
Author: Refresh Themes
Author URI: https://refreshthemes.com
Description: Edu Axis is WordPress Education themes by Refresh Themes.
Version: 1.0.3
License: GNU General Public License v2 or later
License URI: https://www.gnu.org/licenses/license-list.html#GPLv2
Text Domain: edu-axis
Tested up to: 5.4.2
Requires PHP: 5.2.4
Tags: blog, e-commerce, portfolio, flexible-header, translation-ready, custom-menu, custom-logo, featured-images, footer-widgets, custom-background, grid-Layout, two-columns, left-sidebar, right-sidebar, custom-colors, full-width-template, post-formats, theme-options, threaded-comments

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned with others.

Edu Axis is based on Underscores https://underscores.me/, (C) 2012-2017 Automattic, Inc.
Underscores is distributed under the terms of the GNU GPL v2 or later.

Normalizing styles have been helped along thanks to the fine work of
<PERSON> and <PERSON> https://necolas.github.io/normalize.css/
*/
/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Normalize
# Typography
# Elements
# Forms
# Navigation
	## Links
	## Menus
# Accessibility
# Alignments
# Clearings
# Loader
# Widgets
# Content
	## Posts and pages
	## Comments
# Infinite scroll
# Media
	## Captions
	## Galleries
# Layout
# Sections
	## Header image
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Normalize
--------------------------------------------------------------*/
html {
  font-family: sans-serif;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  margin: 0;
  overflow-x: hidden;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
menu,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

[hidden],
template {
  display: none;
}

a {
  background-color: transparent;
}

a:active,
a:hover {
  outline: 0;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b,
strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

h1 {
  font-size: 32px;
  font-size: 2rem;
  margin: 0.67em 0;
}

mark {
  background: #ff0;
  color: #000;
}

small {
  font-size: 80%;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

img {
  border: 0;
}

svg:not(:root) {
  overflow: hidden;
}

figure {
  margin: 1em 40px;
}

hr {
  box-sizing: content-box;
  height: 0;
}

pre {
  overflow: auto;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled],
html input[disabled] {
  cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
}

input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 10px 25px;
}

tr {
  border: 1px solid #e2e2e2;
  text-align: left;
}

/*--------------------------------------------------------------
# Typography
--------------------------------------------------------------*/
body,
button,
input,
select,
optgroup,
textarea {
  color: #656565;
  font-family: "Montserrat", sans-serif;
  font-size: 15px;
  font-size: 0.9375rem;
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  clear: both;
  color: #1abc9c;
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
  line-height: 1.55;
  margin: 0 0 20px;
}

h1 {
  font-size: 28px;
}

h2 {
  font-size: 24px;
}

h3 {
  font-size: 20px;
}

h4 {
  font-size: 18px;
}

h5 {
  font-size: 16px;
}

h6 {
  font-size: 14px;
}

p {
  margin-bottom: 1.75em;
}

dfn, cite, em, i {
  font-style: italic;
}

blockquote {
  margin: 0 1.5em;
}

address {
  margin: 0 0 1.5em;
}

pre {
  background: #eee;
  font-family: "Courier 10 Pitch", Courier, monospace;
  font-size: 15px;
  font-size: 0.9375rem;
  line-height: 1.6;
  margin-bottom: 1.6em;
  max-width: 100%;
  overflow: auto;
  padding: 1.6em;
}

code, kbd, tt, var {
  font-family: Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
  font-size: 15px;
  font-size: 0.9375rem;
}

abbr, acronym {
  border-bottom: 1px dotted #666;
  cursor: help;
}

mark, ins {
  background: #fff9c0;
  text-decoration: none;
}

big {
  font-size: 125%;
}

/*--------------------------------------------------------------
# Elements
--------------------------------------------------------------*/
html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

body {
  background: #fff;
  /* Fallback background color. */
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
}

hr {
  border: 0;
  height: 1px;
  margin-bottom: 1.5em;
}

ul, ol {
  margin: 0;
  padding-left: 1.5em;
}

ul {
  list-style: disc;
}

ol {
  list-style: decimal;
}

li > ul,
li > ol {
  margin-bottom: 0;
  margin-left: 0.5em;
}

dt {
  font-weight: bold;
}

dd {
  margin: 0 1.5em 1.5em;
}

img {
  height: auto;
  /* Make sure images are scaled correctly. */
  max-width: 100%;
  vertical-align: middle;
}

figure {
  margin: 1em 0;
  /* Extra wide images within figure tags don't overflow the content area. */
}

table {
  margin: 0 0 1.5em;
  width: 100%;
}

/*--------------------------------------------------------------
# Forms
--------------------------------------------------------------*/
button,
input[type="button"],
input[type="reset"],
input[type="submit"],
.btn {
  display: inline-block;
  padding: 8px 25px;
  border: 1px solid;
  background-color: #1abc9c;
  color: #fff;
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
  font-size: 15px;
  text-transform: capitalize;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  text-align: center;
}
button:hover, button:focus,
input[type="button"]:hover,
input[type="button"]:focus,
input[type="reset"]:hover,
input[type="reset"]:focus,
input[type="submit"]:hover,
input[type="submit"]:focus,
.btn:hover,
.btn:focus {
  background-color: #2d3e50;
  letter-spacing: 1.1px;
}
button:hover, button:focus, button:active,
input[type="button"]:hover,
input[type="button"]:focus,
input[type="button"]:active,
input[type="reset"]:hover,
input[type="reset"]:focus,
input[type="reset"]:active,
input[type="submit"]:hover,
input[type="submit"]:focus,
input[type="submit"]:active,
.btn:hover,
.btn:focus,
.btn:active {
  color: #fff;
}

.btn-transparent {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}
.btn-transparent:hover, .btn-transparent:focus {
  background-color: #1abc9c;
  border-color: #1abc9c;
  color: #fff;
}

.btn-default {
  border-color: rgba(0, 0, 0, 0.3);
  background-color: #1abc9c;
  color: #2e2e2e;
}
.btn-default:hover, .btn-default:focus {
  color: #fff;
}

.btn-alt {
  background-color: #2d3e50;
}
.btn-alt:hover, .btn-alt:focus, .btn-alt:active {
  background: #1abc9c;
}

span.read-more {
  display: table;
}

input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
input[type="range"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime"],
input[type="datetime-local"],
input[type="color"],
.select2-selection.select2-selection--single,
textarea {
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #656565;
  border-radius: 0;
  padding: 10px;
  height: 38px;
  margin-top: 10px;
  outline: none;
  width: 100%;
  font-family: "Montserrat", sans-serif;
}
input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="range"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="week"]:focus,
input[type="time"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="color"]:focus,
.select2-selection.select2-selection--single:focus,
textarea:focus {
  color: #656565;
  border-color: #1abc9c;
  outline: auto;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 15px;
  padding: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 11px;
  width: 35px;
  height: 35px;
}

input[type="search"] {
  height: 45px;
}

label {
  color: #656565;
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
}

select {
  border: 1px solid rgba(0, 0, 0, 0.1);
}

textarea {
  width: 100%;
  height: 250px;
}

.wpcf7 p {
  margin-top: 0;
  margin-bottom: 20px;
}

.wpcf7 input[type="submit"] {
  width: 100%;
}

/*--------------------------------------------------------------
# Accessibility
--------------------------------------------------------------*/
/* Text meant only for screen readers. */
.screen-reader-text {
  border: 0;
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute !important;
  width: 1px;
  word-wrap: normal !important;
  /* Many screen reader and browser combinations announce broken words as they would appear visually. */
}
.screen-reader-text:focus {
  background-color: #e4e4e4;
  border-radius: 3px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  clip: auto !important;
  clip-path: none;
  color: #f00;
  display: block;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: bold;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000;
  /* Above WP toolbar. */
}

/* Do not show the outline on the skip link target. */
#content[tabindex="-1"]:focus {
  outline: 0;
}

/*--------------------------------------------------------------
# Alignments
--------------------------------------------------------------*/
.alignleft {
  display: inline;
  float: left;
  margin-right: 1.5em;
}

.alignright {
  display: inline;
  float: right;
  margin-left: 1.5em;
}

.aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/*--------------------------------------------------------------
# Clearings
--------------------------------------------------------------*/
.clearfix:before, .clearfix:after,
.clear:before,
.clear:after,
.rt-wrapper:before,
.rt-wrapper:after,
.entry-content:before,
.entry-content:after,
.comment-content:before,
.comment-content:after,
.site-header:before,
.site-header:after,
.site-content:before,
.site-content:after,
.site-footer:before,
.site-footer:after,
.section-content:before,
.section-content:after,
.widget_popular_posts ul li:before,
.widget_popular_posts ul li:after,
.widget_latest_posts ul li:before,
.widget_latest_posts ul li:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
}

.hidden {
  display: none;
}

/*--------------------------------------------------------------
# Loader
--------------------------------------------------------------*/
/*
========================================================
                    RT PRELOADER
========================================================
*/
.rt-preloader-wrapper {
  background: #1abc9c none repeat scroll 0 0;
  height: 100%;
  position: fixed;
  width: 100%;
  z-index: 9999;
}
.rt-preloader-wrapper .rt-cube-grid {
  height: 40px;
  left: 50%;
  margin: -20px 0 0 -20px;
  position: absolute;
  top: 50%;
  width: 40px;
  -webkit-transition: .5s;
  transition: .5s;
}
.rt-preloader-wrapper .rt-cube-grid .rt-cube {
  width: 33%;
  height: 33%;
  padding-right: 1px;
  padding-bottom: 1px;
  float: left;
  -webkit-animation: gridScaleDelay 1.3s infinite ease-in-out;
  -moz-animation: gridScaleDelay 1.3s infinite ease-in-out;
  -ms-animation: gridScaleDelay 1.3s infinite ease-in-out;
  -o-animation: gridScaleDelay 1.3s infinite ease-in-out;
  animation: gridScaleDelay 1.3s infinite ease-in-out;
}
.rt-preloader-wrapper .rt-cube-grid .rt-cube:before {
  content: '';
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: block;
}
.rt-preloader-wrapper .rt-cube-grid .rt-cube.rt-cube7 {
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}
.rt-preloader-wrapper .rt-cube-grid .rt-cube.rt-cube4, .rt-preloader-wrapper .rt-cube-grid .rt-cube.rt-cube8 {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}
.rt-preloader-wrapper .rt-cube-grid .rt-cube.rt-cube1, .rt-preloader-wrapper .rt-cube-grid .rt-cube.rt-cube5, .rt-preloader-wrapper .rt-cube-grid .rt-cube.rt-cube9 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}
.rt-preloader-wrapper .rt-cube-grid .rt-cube.rt-cube2, .rt-preloader-wrapper .rt-cube-grid .rt-cube.rt-cube6 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
.rt-preloader-wrapper .rt-cube-grid .rt-cube.rt-cube3 {
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

@-webkit-keyframes gridScaleDelay {
  0%, 70%, 100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}
@-moz-keyframes gridScaleDelay {
  0%, 70%, 100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}
@-ms-keyframes gridScaleDelay {
  0%, 70%, 100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}
@-o-keyframes gridScaleDelay {
  0%, 70%, 100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}
@keyframes gridScaleDelay {
  0%, 70%, 100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}
/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/
#top-bar {
  background: #2d3e50;
  position: relative;
  z-index: 4000;
  min-height: 50px;
}
#top-bar:before {
  background: #1abc9c none repeat scroll 0 0;
  content: "";
  height: 48px;
  position: absolute;
  right: 0;
  top: 10px;
  width: 40%;
}
#top-bar:after {
  border-color: transparent #1abc9c #1abc9c transparent;
  -o-border-image: none;
  border-image: none;
  border-style: solid;
  border-width: 24px 10px;
  content: "";
  height: 5px;
  position: absolute;
  right: 40%;
  top: 10px;
  width: 0;
}
#top-bar .top-left-menu-container ul,
#top-bar .top-right-menu-container ul {
  padding-left: 0px;
}
#top-bar .top-left-menu-container ul li,
#top-bar .top-right-menu-container ul li {
  list-style-type: none;
  float: left;
}
#top-bar .top-left-menu-container ul lili.menu-item-has-children > a:after,
#top-bar .top-right-menu-container ul lili.menu-item-has-children > a:after {
  display: none;
}
#top-bar .top-left-menu-container ul li a,
#top-bar .top-right-menu-container ul li a {
  font-size: 13px;
  margin-left: 15px;
  margin-right: 15px;
  padding: 15px 0;
  display: block;
  color: #fff;
}
#top-bar .top-left-menu-container ul li a:hover, #top-bar .top-left-menu-container ul li a:focus,
#top-bar .top-right-menu-container ul li a:hover,
#top-bar .top-right-menu-container ul li a:focus {
  color: #1abc9c;
}
#top-bar .top-left-menu-container ul li a:first-child,
#top-bar .top-right-menu-container ul li a:first-child {
  margin-left: 0;
}
#top-bar .address-block-container {
  float: left;
}
#top-bar .address-block-container ul li a:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  position: relative;
  left: 0;
  top: 0;
  padding-right: 5px;
}
#top-bar .address-block-container ul li a[href^="mailto:"]:before {
  content: "\f0e0";
}
#top-bar .address-block-container ul li a[href^="tel:"]:before {
  content: "\f3cd";
}
#top-bar .top-right-menu-wrapper {
  text-align: right;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container {
  display: inline-block;
  margin-top: 10px;
  line-height: 1;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul {
  margin-top: 10px;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li {
  position: relative;
  z-index: 5000;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li > a {
  padding: 7px 2px;
  font-weight: 500;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li > a:hover, #top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li > a:focus {
  color: #2d3e50;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li.menu-item-has-children > a {
  padding-right: 15px;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li button.dropdown-toggle {
  top: 2px;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li button.dropdown-toggle svg {
  fill: #fff;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li ul {
  display: none;
  width: auto;
  position: absolute;
  top: 100%;
  right: 0;
  -webkit-transition: all 0.5s 0.3s ease-in-out;
  -moz-transition: all 0.5s 0.3s ease-in-out;
  -ms-transition: all 0.5s 0.3s ease-in-out;
  -o-transition: all 0.5s 0.3s ease-in-out;
  transition: all 0.5s 0.3s ease-in-out;
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li ul a {
  background: #fff;
  color: #2d3e50;
  margin: 0;
  padding: 10px 15px;
  text-align: left;
  width: 200px;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li ul a:active, #top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li ul a:hover, #top-bar .top-right-menu-wrapper .top-right-menu-container > ul > li ul a:focus {
  color: #1abc9c;
}
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul li:hover > ul,
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul li:active > ul,
#top-bar .top-right-menu-wrapper .top-right-menu-container > ul li:focus > ul {
  display: block;
}
@media (min-width: 1600px) {
  #top-bar:before {
    width: 42%;
  }
  #top-bar:after {
    right: 42%;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  #top-bar:before {
    width: 45%;
  }
  #top-bar:after {
    right: 45%;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  #top-bar:before {
    width: 50%;
  }
  #top-bar:after {
    right: 50%;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  #top-bar .address-block-container {
    display: none;
  }
  #top-bar:before {
    width: 90%;
  }
  #top-bar:after {
    right: 90%;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  #top-bar .address-block-container {
    display: none;
  }
  #top-bar:before {
    width: 90%;
  }
  #top-bar:after {
    right: 90%;
  }
}
@media (max-width: 360.98px) {
  #top-bar .address-block-container {
    display: none;
  }
  #top-bar:before {
    width: 90%;
  }
  #top-bar:after {
    right: 90%;
  }
}

.topbar-toggle {
  position: absolute;
  right: 15px;
  top: 15px;
  margin: auto;
  text-align: center;
  background-color: #35b05e;
  color: #fff;
  box-shadow: none;
  border: none;
  outline: none;
  z-index: 30;
  width: 35px;
  height: 35px;
  border-radius: 5px;
  font-size: 15px;
  text-align: center;
  padding: 0;
  display: none;
}
.topbar-toggle:hover, .topbar-toggle:focus {
  box-shadow: none;
}

#masthead {
  position: relative;
  z-index: 3000;
  padding: 15px 0;
  width: 100%;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
#masthead #rt-header {
  width: 100%;
}
#masthead #rt-header .rt-wrapper {
  display: flex;
}
#masthead #rt-header .rt-wrapper .site-header-menu {
  margin: auto 0;
  position: relative;
}

body.stick-at #masthead {
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
}

.absolute-header #masthead {
  position: absolute;
  left: 0;
  width: 100%;
}

.sticky-menu.stick-at #masthead {
  padding: 0;
  position: fixed;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .sticky-menu.stick-at #masthead {
    position: relative;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .sticky-menu.stick-at #masthead {
    position: relative;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  .sticky-menu.stick-at #masthead {
    position: relative;
  }
}
@media (max-width: 360.98px) {
  .sticky-menu.stick-at #masthead {
    position: relative;
  }
}

.sticky-menu.stick-at #masthead,
.relative-header #masthead {
  background: #fff;
}

@media screen and (min-width: 960px) {
  .admin-bar:not(.page-layout-framed).absolute-header #masthead {
    top: 67px;
  }
  .admin-bar:not(.page-layout-framed).stick-at.sticky-menu #masthead {
    top: 32px;
  }

  body:not(.admin-bar).stick-at.sticky-menu.absolute-header #masthead {
    top: 32px;
  }
  body:not(.admin-bar).stick-at.sticky-menu.stick-at.sticky-menu #masthead {
    top: 0px;
  }
}
.rt-header-search {
  float: right;
}
.rt-header-search li.search-menu {
  list-style: none;
  color: #fff;
  cursor: pointer;
  margin-top: 4px;
}
.rt-header-search li.search-menu button.rt-top-search {
  background: #1abc9c;
  border-radius: 0;
  color: #fff;
  min-width: 40px;
  padding: 5px 10px;
}
.rt-header-search li.search-menu button.rt-top-search:focus, .rt-header-search li.search-menu button.rt-top-search:active, .rt-header-search li.search-menu button.rt-top-search:hover {
  outline: none;
  background: #2d3e50;
  letter-spacing: initial;
}
.rt-header-search li.search-menu .fa-times {
  margin-left: 2px;
}

.top-search-form {
  position: absolute;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  height: 45px;
  overflow: hidden;
  right: 0;
}
.top-search-form .search-form button.search-submit {
  background: transparent;
  border: none;
  padding: 10px 15px;
  top: 3px;
}
.top-search-form .search-form button.search-submit svg {
  fill: #1abc9c;
}
.top-search-form.hidden {
  height: 0;
}

@media (max-width: 991.98px) {
  .rt-header-search {
    display: none;
  }
}
/*--------------------------------------------------------------
# Navigation
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Links
--------------------------------------------------------------*/
a {
  color: #1abc9c;
  text-decoration: none;
  outline: none;
}
a:hover, a:focus, a:active {
  color: #2d3e50;
}
a:focus {
  outline: auto;
}

/*--------------------------------------------------------------
## Site branding
--------------------------------------------------------------*/
.site-branding {
  flex: 1;
  min-width: 220px;
  padding: 10px 0;
}
.site-branding .site-logo img {
  max-height: 70px;
  width: auto;
  margin-right: 15px;
}
.site-branding .site-branding-text .site-title {
  margin: 0;
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  font-size: 28px;
  line-height: 1.26;
  color: #1abc9c;
}
.site-branding .site-branding-text .site-title a {
  color: #1abc9c;
}
.site-branding .site-branding-text .site-description {
  margin: 5px 0 0;
  line-height: 1;
  color: #2d3e50;
}

@media (max-width: 360.98px) {
  .site-branding {
    padding-top: 10px 0;
    float: none;
    text-align: center;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  .site-branding {
    padding-top: 10px 0;
    float: none;
    text-align: center;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .site-branding {
    padding-top: 10px 0;
    float: none;
    text-align: center;
  }
}
/*--------------------------------------------------------------
## Menus
--------------------------------------------------------------*/
.menu-label {
  display: none;
}

button.dropdown-toggle {
  background-color: transparent;
  border: none;
  padding: 0;
  float: right;
  position: absolute;
  top: 9px;
  right: 6px;
  width: 14px;
  height: 14px;
  line-height: 1;
  margin-top: 6px;
}
button.dropdown-toggle svg {
  width: 14px;
  height: 14px;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

/* Small menu. */
.menu-toggle,
.main-navigation.toggled ul {
  display: block;
}

.menu-toggle {
  position: relative;
  width: 60px;
  height: 60px;
  border: none;
  margin-right: 0px;
  margin-top: 0;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
  padding: 0;
  border: none;
  background-color: transparent;
  outline: none;
}
.menu-toggle:hover, .menu-toggle:focus {
  border-color: transparent;
  outline: auto;
}

.icon {
  background-color: #1abc9c;
  width: 22px;
  height: 2px;
  position: absolute;
  display: inline-block;
  left: 50%;
  top: 50%;
  bottom: auto;
  right: auto;
  transform: translateX(-50%) translateY(-50%) translateZ(0) rotate(0deg) scale(1);
  -o-transform: translateX(-50%) translateY(-50%) translateZ(0) rotate(0deg) scale(1);
  -ms-transform: translateX(-50%) translateY(-50%) translateZ(0) rotate(0deg) scale(1);
  -moz-transform: translateX(-50%) translateY(-50%) translateZ(0) rotate(0deg) scale(1);
  -webkit-transform: translateX(-50%) translateY(-50%) translateZ(0) rotate(0deg) scale(1);
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
}

.icon::after,
.icon::before {
  content: '';
  top: 0;
  right: 0;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
}

.icon,
.icon::after,
.icon::before {
  width: 22px;
  height: 2px;
  position: absolute;
}

.icon::before {
  transform: translateX(0) translateY(-6px) translateZ(0) rotate(0deg) scale(1);
  -o-transform: translateX(0) translateY(-6px) translateZ(0) rotate(0deg) scale(1);
  -ms-transform: translateX(0) translateY(-6px) translateZ(0) rotate(0deg) scale(1);
  -moz-transform: translateX(0) translateY(-6px) translateZ(0) rotate(0deg) scale(1);
  -webkit-transform: translateX(0) translateY(-6px) translateZ(0) rotate(0deg) scale(1);
  background-color: #1abc9c;
}

.icon::after {
  transform: translateX(0) translateY(6px) translateZ(0) rotate(0deg) scale(1);
  -o-transform: translateX(0) translateY(6px) translateZ(0) rotate(0deg) scale(1);
  -ms-transform: translateX(0) translateY(6px) translateZ(0) rotate(0deg) scale(1);
  -moz-transform: translateX(0) translateY(6px) translateZ(0) rotate(0deg) scale(1);
  -webkit-transform: translateX(0) translateY(6px) translateZ(0) rotate(0deg) scale(1);
  background-color: #1abc9c;
}

.menu-toggle.active .icon,
.absolute-header #masthead .menu-toggle.active .icon {
  background-color: transparent;
}

.menu-toggle.active .icon::before {
  transform: translateX(0) translateY(0) translateZ(0) rotate(-135deg) scale(1);
  -o-transform: translateX(0) translateY(0) translateZ(0) rotate(-135deg) scale(1);
  -ms-transform: translateX(0) translateY(0) translateZ(0) rotate(-135deg) scale(1);
  -moz-transform: translateX(0) translateY(0) translateZ(0) rotate(-135deg) scale(1);
  -webkit-transform: translateX(0) translateY(0) translateZ(0) rotate(-135deg) scale(1);
}

.menu-toggle.active .icon::after {
  transform: translateX(0) translateY(0) translateZ(0) rotate(138deg) scale(1);
  -o-transform: translateX(0) translateY(0) translateZ(0) rotate(138deg) scale(1);
  -ms-transform: translateX(0) translateY(0) translateZ(0) rotate(138deg) scale(1);
  -moz-transform: translateX(0) translateY(0) translateZ(0) rotate(138deg) scale(1);
  -webkit-transform: translateX(0) translateY(0) translateZ(0) rotate(138deg) scale(1);
}

#site-header-menu {
  float: right;
}
#site-header-menu .menu-container {
  display: inline-block;
}
#site-header-menu .main-navigation {
  clear: both;
  display: block;
  float: left;
  width: 100%;
}
#site-header-menu .main-navigation ul {
  display: none;
  list-style: none;
  margin: 0;
  padding-left: 0;
  position: absolute;
  top: 100%;
  z-index: 30000;
  right: 0;
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
#site-header-menu .main-navigation ul li {
  position: relative;
  color: #2d3e50;
}
#site-header-menu .main-navigation ul li a {
  display: block;
  text-decoration: none;
  text-transform: uppercase;
  color: #2d3e50;
  padding: 10px 20px;
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
}
#site-header-menu .main-navigation ul li a:hover, #site-header-menu .main-navigation ul li a:focus {
  color: #1abc9c;
}
#site-header-menu .main-navigation ul li svg {
  fill: #2d3e50;
}
#site-header-menu .main-navigation ul li svg:hover, #site-header-menu .main-navigation ul li svg:focus {
  fill: #1abc9c;
}
#site-header-menu .main-navigation ul ul {
  display: none;
  border-bottom-color: transparent;
  position: absolute;
  top: 100%;
  left: auto;
  -webkit-transition: all 0.5s 0.3s ease-in-out;
  -moz-transition: all 0.5s 0.3s ease-in-out;
  -ms-transition: all 0.5s 0.3s ease-in-out;
  -o-transition: all 0.5s 0.3s ease-in-out;
  transition: all 0.5s 0.3s ease-in-out;
}
#site-header-menu .main-navigation ul ul a {
  background: #1abc9c;
  color: #fff;
}
#site-header-menu .main-navigation ul ul a:hover, #site-header-menu .main-navigation ul ul a:active, #site-header-menu .main-navigation ul ul a:focus {
  color: #2d3e50;
}
#site-header-menu .main-navigation ul ul li ul {
  right: 100%;
  top: -1px;
  border: 1px solid rgba(0, 0, 0, 0.2);
}
#site-header-menu .main-navigation ul ul:last-child li:last-child {
  border-bottom: none;
}
#site-header-menu .main-navigation ul li:hover > ul,
#site-header-menu .main-navigation ul li.focus > ul {
  left: auto;
}

.classic-menu .main-navigation ul ul button.dropdown-toggle {
  margin-top: 5px;
}

.classic-menu .main-navigation ul ul svg {
  transform: rotate(90deg);
  position: absolute;
  right: 0px;
  top: 0px;
}
.classic-menu .main-navigation ul ul svg a {
  padding-left: 25px;
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}

.classic-menu #site-header-menu .main-navigation ul#primary-menu > li > ul:after {
  content: "";
  position: absolute;
  top: -18px;
  right: 4px;
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
  border-top: 9px solid transparent;
  border-bottom: 9px solid #1abc9c;
}

ul#primary-menu > li.menu-item-has-children > a {
  padding-right: 34px;
}
ul#primary-menu button.dropdown-toggle:after {
  display: none;
}

@media screen and (min-width: 37.5em) {
  .main-navigation ul {
    display: block;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .classic-menu #site-header-menu .main-navigation ul {
    width: auto;
    max-width: none;
  }
  .classic-menu #site-header-menu .main-navigation ul li.menu-item-has-children {
    position: relative;
  }
  .classic-menu #site-header-menu .main-navigation ul li.menu-item-has-children:hover > ul {
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    display: block !important;
  }
  .classic-menu #site-header-menu .main-navigation ul li:hover > ul {
    left: auto;
    float: left;
    position: absolute;
    top: auto;
    display: inline-block;
  }
  .classic-menu #site-header-menu .main-navigation ul li > ul > li:hover > ul,
  .classic-menu #site-header-menu .main-navigation ul li > ul > li:focus > ul {
    top: -1px;
    border: 1px solid rgba(0, 0, 0, 0.2);
  }
  .classic-menu #site-header-menu .main-navigation ul li ul {
    width: auto;
  }
  .classic-menu #site-header-menu .main-navigation ul li ul a {
    width: 250px;
  }

  .classic-menu #site-header-menu .menu-toggle {
    display: none;
  }
  .classic-menu #site-header-menu .main-navigation ul#primary-menu {
    display: block !important;
    max-width: 100%;
    position: relative;
    border: none;
  }
  .classic-menu #site-header-menu .main-navigation ul#primary-menu li {
    float: left;
    border-bottom: none;
  }
}
@media (min-width: 1200px) and (max-width: 1599.98px) {
  .main-navigation ul a svg {
    display: inline-block;
    vertical-align: middle;
    margin-left: 5px;
    fill: #fff;
    padding-bottom: 2px;
  }

  .classic-menu #site-header-menu .main-navigation ul {
    width: auto;
    max-width: none;
  }
  .classic-menu #site-header-menu .main-navigation ul li.menu-item-has-children {
    position: relative;
  }
  .classic-menu #site-header-menu .main-navigation ul li.menu-item-has-children:hover > ul {
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    display: block !important;
  }
  .classic-menu #site-header-menu .main-navigation ul li ul {
    width: auto;
  }
  .classic-menu #site-header-menu .main-navigation ul li ul a {
    width: 250px;
  }

  .classic-menu #site-header-menu .menu-toggle {
    display: none;
  }
  .classic-menu #site-header-menu .main-navigation ul#primary-menu {
    display: block !important;
    max-width: 100%;
    position: relative;
    border: none;
  }
  .classic-menu #site-header-menu .main-navigation ul#primary-menu li {
    float: left;
  }
}
@media (min-width: 1600px) {
  .main-navigation ul a svg {
    display: inline-block;
    vertical-align: middle;
    margin-left: 5px;
    fill: #fff;
    padding-bottom: 2px;
  }

  .main-navigation ul ul a svg,
  .classic-menu #site-header-menu .main-navigation ul {
    width: auto;
    max-width: none;
  }
  .main-navigation ul ul a svg li.menu-item-has-children,
  .classic-menu #site-header-menu .main-navigation ul li.menu-item-has-children {
    position: relative;
  }
  .main-navigation ul ul a svg li.menu-item-has-children:hover > ul,
  .classic-menu #site-header-menu .main-navigation ul li.menu-item-has-children:hover > ul {
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    display: block !important;
  }
  .main-navigation ul ul a svg li:hover > ul,
  .main-navigation ul ul a svg li:active > ul,
  .main-navigation ul ul a svg li:focus > ul,
  .classic-menu #site-header-menu .main-navigation ul li:hover > ul,
  .classic-menu #site-header-menu .main-navigation ul li:active > ul,
  .classic-menu #site-header-menu .main-navigation ul li:focus > ul {
    display: inline-block;
  }
  .main-navigation ul ul a svg li ul,
  .classic-menu #site-header-menu .main-navigation ul li ul {
    width: auto;
  }
  .main-navigation ul ul a svg li ul a,
  .classic-menu #site-header-menu .main-navigation ul li ul a {
    width: 250px;
  }

  .classic-menu #site-header-menu .menu-toggle {
    display: none;
  }
  .classic-menu #site-header-menu .main-navigation ul#primary-menu {
    display: block !important;
    max-width: 100%;
    position: relative;
    border: none;
  }
  .classic-menu #site-header-menu .main-navigation ul#primary-menu li {
    float: left;
    border-bottom: none;
  }
}
@media (max-width: 991.98px) {
  #site-header-menu .main-navigation ul {
    width: 250px;
  }
  #site-header-menu .main-navigation ul li a {
    border-bottom: 1px solid rgba(255, 255, 255, 0.5);
    background: #1abc9c;
    color: #fff;
  }
  #site-header-menu .main-navigation ul li a:hover, #site-header-menu .main-navigation ul li a:focus {
    color: #2d3e50;
  }
  #site-header-menu .main-navigation ul ul {
    position: relative;
    right: auto !important;
    border: none !important;
  }
  #site-header-menu .main-navigation ul ul:after {
    border: none !important;
  }
  #site-header-menu .main-navigation ul ul li a:hover,
  #site-header-menu .main-navigation ul ul li a:focus {
    background-color: #1abc9c;
  }
  #site-header-menu .main-navigation ul ul ul {
    top: 0 !important;
    border-top: 1px solid #3f5771 !important;
  }
  #site-header-menu .main-navigation ul ul a {
    padding-left: 30px;
  }
  #site-header-menu .main-navigation ul ul ul a {
    padding-left: 60px;
  }
  #site-header-menu .main-navigation ul ul ul ul a {
    padding-left: 90px;
  }
  #site-header-menu .main-navigation ul ul ul ul ul a {
    padding-left: 120px;
  }
  #site-header-menu .main-navigation > ul:after {
    content: "";
    position: absolute;
    top: -18px;
    right: 20px;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-top: 9px solid transparent;
    border-bottom: 9px solid #1abc9c;
  }
}
/*--------------------------------------------------------------
## Menus
--------------------------------------------------------------*/
.social-menu-container ul {
  list-style: none;
  padding-left: 0;
}
.social-menu-container ul li {
  display: inline-block;
  vertical-align: top;
  padding: 5px 0px;
}
.social-menu-container ul li a {
  font-family: "Font Awesome 5 Brands";
  font-weight: 400;
  display: inline-block;
  text-align: center;
  color: #fff;
  margin-left: 7px;
  margin-right: 7px;
  position: relative;
  font-size: 15px;
}
.social-menu-container ul li a:hover, .social-menu-container ul li a:focus {
  color: #2d3e50;
}
.social-menu-container ul li a[href$="wp-admin/nav-menus.php"]:before {
  content: "\f055";
  font-weight: 900;
  font-family: "Font Awesome 5 Free";
}
.social-menu-container ul li a:hover, .social-menu-container ul li a:focus {
  color: #2d3e50;
}
.social-menu-container ul li a[href*="facebook.com"]:before,
.social-menu-container ul li a[href*="fb.com"]:before {
  content: "\f09a";
}
.social-menu-container ul li a[href*="twitter.com"]:before {
  content: "\f099";
}
.social-menu-container ul li a[href*="linkedin.com"]:before {
  content: "\f0e1";
}
.social-menu-container ul li a[href*="instagram.com"]:before {
  content: "\f16d";
}
.social-menu-container ul li a[href*="youtube.com"]:before {
  content: "\f167";
}
.social-menu-container ul li a[href*="pinterest.com"]:before {
  content: "\f0d2";
}
.social-menu-container ul li a[href*="plus.google.com"]:before {
  content: "\f0d5";
}
.social-menu-container ul li a:hover[href*="facebook.com"], .social-menu-container ul li a:focus[href*="facebook.com"],
.social-menu-container ul li a:hover[href*="fb.com"], .social-menu-container ul li a:focus[href*="fb.com"] {
  color: #3C579E !important;
}
.social-menu-container ul li a:hover[href*="twitter.com"], .social-menu-container ul li a:focus[href*="twitter.com"] {
  color: #01BBF6 !important;
}
.social-menu-container ul li a:hover[href*="linkedin.com"], .social-menu-container ul li a:focus[href*="linkedin.com"] {
  color: #1484C3 !important;
}
.social-menu-container ul li a:hover[href*="instagram.com"], .social-menu-container ul li a:focus[href*="instagram.com"] {
  color: #2D6A93 !important;
}
.social-menu-container ul li a:hover[href*="youtube.com"], .social-menu-container ul li a:focus[href*="youtube.com"] {
  color: #D62424 !important;
}
.social-menu-container ul li a:hover[href*="pinterest.com"], .social-menu-container ul li a:focus[href*="pinterest.com"] {
  color: #BD2126 !important;
}
.social-menu-container ul li a:hover[href*="plus.google.com"] a:focus[href*="plus.google.com"] {
  color: #D73D32 !important;
}

/*--------------------------------------------------------------
# Widgets
--------------------------------------------------------------*/
#secondary {
  margin-top: 50px;
}
#secondary .widget {
  padding: 30px;
  background: #fff;
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
}
#secondary .widgettitle {
  color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 28px;
  text-transform: none;
  font-size: 22px;
  letter-spacing: 0;
}
#secondary .widget-title {
  text-transform: none;
  font-size: 22px;
  letter-spacing: 0;
}
#secondary ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
#secondary ul li {
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  margin-bottom: 10px;
  position: relative;
  padding-left: 25px;
}
#secondary ul li:hover {
  padding-left: 35px;
}
#secondary ul li a:before {
  position: absolute;
  font-family: "Font Awesome 5 Free";
  content: "\f0c6";
  font-weight: 900;
  font-size: 13px;
  color: #b2b2b2;
  left: 2px;
  top: 3px;
}
#secondary ul li a[aria-current=page] {
  color: #1abc9c;
}
#secondary ul li a[aria-current=page]::before {
  color: #1abc9c;
}
#secondary ul li ul.children {
  margin-left: -25px;
}
#secondary ul li.has-post-thumbnail .post-details {
  padding-right: 100px;
}
#secondary ul a {
  color: #191919;
}
#secondary ul a:hover, #secondary ul a:focus {
  color: #1abc9c;
}
#secondary section.widget_recent_comments ul li a:before {
  content: "\f075";
  font-weight: 100;
}
#secondary section.widget_recent_entries ul li a:before {
  content: "\f02b";
}
#secondary section.widget_archive ul li a:before {
  content: "\f07b";
  font-weight: 100;
}
#secondary section.widget_categories ul li a:before {
  content: "\f07c";
  font-weight: 100;
}
#secondary .textwidget {
  text-align: center;
}
#secondary .textwidget img {
  border-radius: 50%;
  margin: 15px 0 25px;
}

.widget:not(:last-child) {
  margin: 0 0 42px;
  /* Make sure select elements fit in widgets. */
}
.widget:not(:last-child) select {
  max-width: 100%;
}

.widget-title {
  font-size: 18px;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.1em;
}

svg.icon-search {
  width: 18px;
  height: 18px;
  fill: #ffff;
}

#subscribe-email input {
  margin-top: 0;
  border: none;
}

.search-form {
  position: relative;
}
.search-form input.search-field {
  margin-top: 0;
  border: 1px solid #ccc;
  padding-right: 70px;
}
.search-form input.search-field:focus {
  outline: auto;
}
.search-form button.search-submit {
  padding-left: 20px;
  padding-right: 20px;
  position: absolute;
  top: 1px;
  right: 1px;
  border-radius: 0;
  padding-bottom: 11px;
  padding-top: 10px;
  line-height: 1;
}
.search-form button.search-submit:focus {
  outline: 2px solid #000c;
}

#secondary .widget-title,
#secondary .widgettitle {
  background: #1abc9c;
  border-bottom: 0.0625rem solid rgba(140, 140, 140, 0.2);
  padding: 15px 30px;
  margin: -30px -30px 25px;
  color: #fff;
  position: relative;
}
#secondary .widget-title:after,
#secondary .widgettitle:after {
  content: " ";
  position: absolute;
  bottom: -4px;
  width: 100%;
  height: 0.125rem;
  background-color: #ccc;
  left: 24px;
  margin-left: -1.5rem;
}

#colophon ul li.has-post-thumbnail .post-details {
  padding-right: 100px;
}

.post-details {
  display: table;
  float: left;
}
.post-details .posted-on {
  margin-bottom: 10px;
  display: inline-block;
  font-weight: 600;
  font-size: 14px;
}
.post-details .post-title {
  line-height: 1.5;
  margin: 0;
  font-weight: 600;
}

.post-image {
  position: absolute;
  top: 0;
  right: 0;
}

.widget_popular_posts ul li,
.widget_latest_posts ul li {
  position: relative;
  margin-bottom: 45px;
}
.widget_popular_posts ul li span.posted-on a,
.widget_latest_posts ul li span.posted-on a {
  color: #929292;
}
.widget_popular_posts ul li span.posted-on a:hover, .widget_popular_posts ul li span.posted-on a:focus,
.widget_latest_posts ul li span.posted-on a:hover,
.widget_latest_posts ul li span.posted-on a:focus {
  color: #1abc9c;
}

#wp-calendar {
  margin-bottom: 0;
}
#wp-calendar th, #wp-calendar td {
  padding: 10px;
  text-align: center;
}
#wp-calendar caption {
  caption-side: top;
  text-align: right;
  padding: 0.5rem;
}
#wp-calendar #today {
  background: #1abc9c;
}
#wp-calendar #today a {
  color: #fff;
}
#wp-calendar #today a:before {
  background: #fff;
}
#wp-calendar tbody a {
  position: relative;
  font-weight: 500;
}
#wp-calendar tbody a::before {
  content: "";
  display: block;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #1abc9c;
  position: absolute;
  right: -7px;
}
#wp-calendar td#prev {
  text-align: left;
}
#wp-calendar td#next {
  text-align: right;
}

/*--------------------------------------------------------------
# Content
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Posts and pages
--------------------------------------------------------------*/
.sticky {
  display: block;
}

.hentry {
  margin: 0 0 1.5em;
}

.updated:not(.published) {
  display: none;
}

.page-content,
.entry-content,
.entry-summary {
  margin: 1.5em 0 0;
}

.page .site-main .entry-content {
  margin-top: 0;
}

.page-links {
  clear: both;
  margin: 0 0 1.5em;
}

#page-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}
#page-header .page-header {
  padding: 35px 0;
}
#page-header .page-header .page-title {
  margin: 0;
  font-size: 32px;
}

#inner-content-wrapper {
  padding: 35px 0;
}

.entry-meta {
  margin-bottom: 2px;
  padding: 0;
}

.entry-meta > span {
  font-weight: 500;
  color: #b2b2b2;
}
.entry-meta > span:before {
  font-family: "Font Awesome 5 Free";
  padding-right: 5px;
  color: #b2b2b2;
}

.entry-meta > span a {
  color: #b2b2b2;
}
.entry-meta > span a:hover, .entry-meta > span a:focus {
  color: #1abc9c;
}

.entry-meta > span:not(:last-child):after,
.author-meta > span:not(:last-child):after,
.entry-footer > span:not(:last-child):after {
  color: #cbcbcb;
  content: "|";
  padding: 0 5px;
}

.posted-on a,
.tags-links a,
.cat-links a,
.comment-meta a {
  font-size: 13px;
  font-family: "Poppins", sans-serif;
}
.posted-on:before,
.tags-links:before,
.cat-links:before,
.comment-meta:before {
  font-family: "Font Awesome 5 Free";
  padding-right: 5px;
  color: #b2b2b2;
}

.posted-on:before {
  content: "\f017";
}

.tags-links:before {
  content: "\f02c";
  font-weight: 900;
}

.cat-links:before {
  content: "\f07c";
}

span.comment-meta:before {
  content: "\f075";
}

.byline {
  font-size: 13px;
  text-transform: capitalize;
}
.byline .author img {
  border-radius: 50%;
  margin-right: 10px;
  -webkit-filter: grayscale(80%);
  -moz-filter: grayscale(80%);
  -ms-filter: grayscale(80%);
  -o-filter: grayscale(80%);
  filter: grayscale(80%);
  display: inline-block;
  width: auto !important;
}
.byline .author:hover img {
  -webkit-filter: grayscale(0%);
  -moz-filter: grayscale(0%);
  -ms-filter: grayscale(0%);
  -o-filter: grayscale(0%);
  filter: grayscale(0%);
}
.byline .author a {
  color: #b2b2b2;
}
.byline .author a:hover, .byline .author a:focus {
  color: #1abc9c;
}

.rt-featured-image {
  position: relative;
  background: #f1f1f1;
}
.rt-featured-image .post-thumbnail {
  z-index: 1;
}
.rt-featured-image:before {
  font-family: "Font Awesome 5 Free";
  content: '\f03e';
  position: absolute;
  top: calc( 50% - 45px );
  left: calc( 50% - 25px );
  font-size: 50px;
  z-index: 0;
  color: #ccc;
}
.rt-featured-image:after {
  content: '16:9';
  position: absolute;
  color: #ccc;
  z-index: 0;
  text-align: center;
  top: 60%;
  display: block;
  width: 100%;
}

.blog .rt-featured-image:after {
  content: '1:1';
}

.comment-meta a {
  color: #b2b2b2;
}
.comment-meta a:hover, .comment-meta a:focus {
  color: #1abc9c;
}

.sticky-post-wrapper article {
  margin-bottom: 49px;
}
.sticky-post-wrapper article .featured-post-image {
  width: 100%;
}
.sticky-post-wrapper article .featured-post-image .rt-featured-image {
  padding: 200px 0;
  background-size: cover;
  background-position: 50%;
  margin-bottom: 25px;
}
.sticky-post-wrapper article .entry-container .entry-header .entry-title {
  font-size: 22px;
  line-height: 1.5;
}
.sticky-post-wrapper article .entry-container .entry-header .entry-title a {
  color: #2e2e2e;
  display: block;
}
.sticky-post-wrapper article .entry-container .entry-header .entry-title a:hover, .sticky-post-wrapper article .entry-container .entry-header .entry-title a:focus {
  color: #1abc9c;
}
.sticky-post-wrapper article .entry-container .entry-content {
  margin-bottom: 1.5em;
}
.sticky-post-wrapper article .entry-container .read-more-link a {
  color: #1abc9c;
  text-decoration: underline;
  text-transform: uppercase;
  letter-spacing: 0.01em;
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
}
.sticky-post-wrapper article .author-meta {
  clear: both;
  margin-bottom: 21px;
}
.sticky-post-wrapper article .author-meta .author {
  display: flex;
  align-items: center;
}
.sticky-post-wrapper article .author-meta .author img {
  border-radius: 50%;
  margin-right: 21px;
}
.sticky-post-wrapper article .author-meta .author a {
  color: #2e2e2e;
  letter-spacing: 0.01em;
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
}
.sticky-post-wrapper article .author-meta .author a:hover, .sticky-post-wrapper article .author-meta .author a:focus {
  color: #1abc9c;
}

.entry-title {
  text-transform: uppercase;
}
.entry-title a {
  text-transform: uppercase;
}

body.single .site-main article,
body.page .site-main article {
  padding: 25px;
}

.site-main article {
  padding: 10px;
  margin-bottom: 40px;
  text-align: left;
  background: transparent;
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
  overflow: hidden;
  background-color: #fff;
  display: flex;
}
.site-main article:hover .rt-overlay, .site-main article:focus .rt-overlay {
  opacity: 0.9;
  visibility: visible;
  width: 100%;
  height: 100%;
}
.site-main article:hover .read-more, .site-main article:focus .read-more {
  visibility: visible;
  opacity: 1;
}
.site-main article .featured-post-image .rt-featured-image {
  overflow: hidden;
  min-height: 226px;
}
.site-main article .featured-post-image .rt-featured-image .post-thumbnail {
  position: relative;
  display: block;
  overflow: hidden;
}
.site-main article .featured-post-image .rt-featured-image .post-thumbnail img {
  width: 100%;
  transition: transform .2s;
}
.site-main article .featured-post-image .rt-featured-image .rt-overlay {
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.3);
  width: 10%;
  height: 10%;
  margin: auto;
  right: 0px;
  bottom: 0px;
  top: 0px;
  left: 0px;
  visibility: hidden;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.site-main article .featured-post-image .rt-featured-image .read-more {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  visibility: hidden;
  opacity: 0;
  z-index: 1111;
}
.site-main article .featured-post-image .rt-featured-image .read-more .btn {
  margin-top: 0;
}
.site-main article .entry-container {
  padding: 10px 0;
}
.site-main article .entry-container .entry-meta {
  padding: 0 20px;
}
.site-main article .entry-container .entry-header {
  padding: 0 20px;
  margin-bottom: 0px;
}
.site-main article .entry-container .entry-header .entry-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0px;
}
.site-main article .entry-container .entry-header .entry-title a {
  color: #1abc9c;
}
.site-main article .entry-container .entry-header .entry-title a:hover, .site-main article .entry-container .entry-header .entry-title a:focus {
  color: #2d3e50;
}
.site-main article .entry-container .entry-content {
  margin-top: 0;
  padding: 0 20px;
}
.site-main article .entry-container .entry-content p {
  margin-bottom: 20px;
}
.site-main article .entry-container .entry-content .read-more {
  display: none;
}
.site-main article .entry-container .author-meta {
  padding: 0 20px;
}
.site-main article .entry-footer {
  padding: 10px 20px;
  border-top: 1px solid #eee;
}
.site-main article .entry-footer .byline img {
  width: 18px;
  height: 18px;
  margin-right: 5px;
}
@media (max-width: 360.98px) {
  .site-main article {
    flex-direction: column;
  }
  .site-main article .featured-post-image {
    flex: 1;
  }
  .site-main article .featured-post-image .rt-featured-image {
    width: auto;
  }
  .site-main article .entry-container {
    flex: 1;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  .site-main article {
    flex-direction: column;
  }
  .site-main article .featured-post-image {
    flex: 1;
  }
  .site-main article .featured-post-image .rt-featured-image {
    width: auto;
  }
  .site-main article .entry-container {
    flex: 1;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .site-main article {
    flex-direction: row;
  }
  .site-main article .featured-post-image .rt-featured-image {
    width: 230px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .site-main article {
    flex-direction: row;
  }
  .site-main article .featured-post-image .rt-featured-image {
    width: 230px;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .site-main article {
    flex-direction: row;
  }
  .site-main article .featured-post-image .rt-featured-image {
    width: 230px;
  }
}
@media (min-width: 1200px) and (max-width: 1599.98px) {
  .site-main article {
    flex-direction: row;
  }
  .site-main article .featured-post-image .rt-featured-image {
    width: 230px;
  }
}
@media (min-width: 1600px) {
  .site-main article {
    flex-direction: row;
  }
  .site-main article .featured-post-image .rt-featured-image {
    width: 230px;
  }
}
.site-main article:hover {
  background: #fafafa;
  -webkit-box-shadow: -3px 0px 15px #ccc;
  -moz-box-shadow: -3px 0px 15px #ccc;
  box-shadow: -3px 0px 15px #ccc;
}
.site-main article:last-child {
  margin-bottom: 0;
}

body.single article,
body.page:not(.home) article {
  flex-direction: column;
}
body.single article .featured-post-image .rt-featured-image,
body.page:not(.home) article .featured-post-image .rt-featured-image {
  width: auto;
  margin: 5px;
}
body.single article .entry-container .entry-header,
body.single article .entry-container .entry-meta,
body.single article .entry-container .entry-content,
body.single article .entry-container .author-meta,
body.page:not(.home) article .entry-container .entry-header,
body.page:not(.home) article .entry-container .entry-meta,
body.page:not(.home) article .entry-container .entry-content,
body.page:not(.home) article .entry-container .author-meta {
  padding: 0 5px;
}
body.single article .entry-container .entry-header .entry-title,
body.page:not(.home) article .entry-container .entry-header .entry-title {
  font-size: 20px;
  margin: 10px 0;
}
body.single article .entry-container .author-meta,
body.page:not(.home) article .entry-container .author-meta {
  text-align: right;
}

/*--------------------------------------------------------------
## Comments
--------------------------------------------------------------*/
.comment-content a {
  word-wrap: break-word;
}

.bypostauthor {
  display: block;
}

span.says {
  display: none;
}

#comments {
  margin-top: 54px;
}
#comments .comments-title {
  margin-bottom: 20px;
  font-size: 24px;
}
#comments ol {
  padding-left: 0;
  margin-left: 0;
  list-style: none;
}
#comments ol.comment-list {
  margin-bottom: 50px;
}
#comments ol.comment-list li {
  border-left: 3px solid #1abc9c;
  padding-left: 15px;
  background: #fff;
}
#comments ol.comment-list li article {
  margin-bottom: 20px;
  position: relative;
}
#comments ol.comment-list li li {
  padding-top: 0;
  padding-bottom: 0;
}
#comments ol.comment-list > li:first-child {
  padding-top: 0;
}
#comments ol.comment-list .comment-meta .comment-author img {
  float: left;
  margin-right: 15px;
  border-radius: 50%;
}
#comments ol.comment-list ol.children {
  padding: 0 0 0em 1.5em;
}
#comments ol.comment-list b.fn {
  font-size: 16px;
  font-weight: 400;
  display: table;
  line-height: 1;
  margin-bottom: 0px;
  text-transform: capitalize;
}
#comments ol.comment-list .comment-content p {
  margin-bottom: 0px;
}
#comments ol.comment-list .comment-metadata {
  display: table;
}
#comments ol.comment-list .comment-metadata a {
  font-weight: 500;
}
#comments ol.comment-list .reply a {
  text-decoration: none;
  text-transform: capitalize;
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
  font-size: 12px;
  position: absolute;
  top: 15px;
  right: 15px;
  line-height: 1;
  text-transform: uppercase;
  padding: 5px 10px;
  background: #2d3e50;
  color: #fff;
  letter-spacing: 0.5px;
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
}
#comments ol.comment-list .reply a:hover, #comments ol.comment-list .reply a:focus {
  text-decoration: none;
  background: #1abc9c;
}
#comments #respond #reply-title {
  font-size: 24px;
  color: #1abc9c;
  margin-bottom: 10px;
}
#comments #respond form {
  background-color: #f5f5f5;
  padding: 20px 30px;
}
#comments #respond form input,
#comments #respond form textarea {
  border: none;
}
#comments #respond form .form-submit {
  text-align: right;
}

#comments .fn a:hover, #comments .fn a:focus,
#comments .comment-metadata a:hover,
#comments .comment-metadata a:focus {
  color: #1abc9c;
}

/*--------------------------------------------------------------
# Infinite scroll
--------------------------------------------------------------*/
/* Globally hidden elements when Infinite Scroll is supported and in use. */
.infinite-scroll .posts-navigation,
.infinite-scroll.neverending .site-footer {
  /* Theme Footer (when set to scrolling) */
  display: none;
}

/* When Infinite Scroll has reached its end we need to re-display elements that were hidden (via .neverending) before. */
.infinity-end.neverending .site-footer {
  display: block;
}

/*--------------------------------------------------------------
# Media
--------------------------------------------------------------*/
.page-content .wp-smiley,
.entry-content .wp-smiley,
.comment-content .wp-smiley {
  border: none;
  margin-bottom: 0;
  margin-top: 0;
  padding: 0;
}

/* Make sure embeds and iframes fit their containers. */
embed,
iframe,
object {
  max-width: 100%;
}

/* Make sure logo link wraps around logo image. */
.custom-logo-link {
  display: inline-block;
}

/*--------------------------------------------------------------
## Captions
--------------------------------------------------------------*/
.wp-caption {
  margin-bottom: 1.5em;
  max-width: 100%;
}
.wp-caption img[class*="wp-image-"] {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.wp-caption .wp-caption-text {
  margin: 0.8075em 0;
}

.wp-caption-text {
  text-align: center;
}

/*--------------------------------------------------------------
## Galleries
--------------------------------------------------------------*/
.gallery {
  margin-bottom: 1.5em;
}

.gallery-item {
  display: inline-block;
  text-align: center;
  vertical-align: top;
  width: 100%;
}
.gallery-columns-2 .gallery-item {
  max-width: 50%;
}
.gallery-columns-3 .gallery-item {
  max-width: 33.33%;
}
.gallery-columns-4 .gallery-item {
  max-width: 25%;
}
.gallery-columns-5 .gallery-item {
  max-width: 20%;
}
.gallery-columns-6 .gallery-item {
  max-width: 16.66%;
}
.gallery-columns-7 .gallery-item {
  max-width: 14.28%;
}
.gallery-columns-8 .gallery-item {
  max-width: 12.5%;
}
.gallery-columns-9 .gallery-item {
  max-width: 11.11%;
}

.gallery-item {
  margin: 0.5em 0;
  padding: 0 0.5em;
}

.gallery-caption {
  display: block;
}

.widget .gallery {
  margin-bottom: 0;
}
.widget .gallery-item {
  margin: 0.25em 0;
  padding: 0 0.25em;
}

/*--------------------------------------------------------------
# Layout
--------------------------------------------------------------*/
.rt-wrapper,
#content {
  margin-left: auto;
  margin-right: auto;
  padding-left: 20px;
  padding-right: 20px;
}

#content {
  padding-top: 40px;
  padding-bottom: 40px;
  min-height: 350px;
}

.relative {
  position: relative;
}

.page-section {
  padding: 50px 0;
}

.align-center {
  text-align: center;
}

.rt-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: 0.2;
  z-index: 11;
  display: none;
}

.error404 section.error-404 {
  text-align: center;
}
.error404 section.error-404 .search-form {
  margin: auto;
  width: 300px;
}

.search-no-results .no-results {
  text-align: center;
}
.search-no-results .no-results .search-form {
  margin: auto;
  width: 300px;
}

body #content #primary {
  width: 70%;
  float: left;
  padding-right: 40px;
  box-sizing: border-box;
}
body #content #secondary {
  width: 30%;
  float: left;
}
body.no-sidebar #content #primary {
  width: 100%;
  padding-right: 0;
}
body.no-sidebar #content #secondary {
  display: none;
}

body.left-sidebar #content #primary {
  padding-right: 0;
  padding-left: 40px;
}

@media (max-width: 360.98px) {
  .rt-wrapper {
    width: auto;
  }

  .right-sidebar #content,
  .left-sidebar #content {
    display: flex;
    flex-direction: column;
  }
  .right-sidebar #content #primary,
  .left-sidebar #content #primary {
    width: auto;
    float: unset;
    padding-right: 0;
    padding-left: 0;
    order: 1;
  }
  .right-sidebar #content #secondary,
  .left-sidebar #content #secondary {
    width: auto;
    float: unset;
    margin-top: 30px;
    order: 2;
  }

  .no-sidebar #content #primary {
    width: auto;
  }
  .no-sidebar #content #secondary {
    width: auto;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  .rt-wrapper {
    width: auto;
  }

  .right-sidebar #content,
  .left-sidebar #content {
    display: flex;
    flex-direction: column;
  }
  .right-sidebar #content #primary,
  .left-sidebar #content #primary {
    width: auto;
    float: unset;
    padding-right: 0;
    padding-left: 0;
    order: 1;
  }
  .right-sidebar #content #secondary,
  .left-sidebar #content #secondary {
    width: auto;
    float: unset;
    margin-top: 30px;
    order: 2;
  }

  .no-sidebar #content #primary {
    width: auto;
  }
  .no-sidebar #content #secondary {
    width: auto;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .rt-wrapper {
    width: 540px;
  }

  .right-sidebar #content,
  .left-sidebar #content {
    display: flex;
    flex-direction: column;
  }
  .right-sidebar #content #primary,
  .left-sidebar #content #primary {
    width: auto;
    float: unset;
    padding-right: 0;
    padding-left: 0;
    order: 1;
  }
  .right-sidebar #content #secondary,
  .left-sidebar #content #secondary {
    width: auto;
    float: unset;
    margin-top: 30px;
    order: 2;
  }

  .no-sidebar #content #primary {
    width: 540px;
  }
  .no-sidebar #content #secondary {
    display: none;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .rt-wrapper {
    width: 750px;
  }

  .right-sidebar #content,
  .left-sidebar #content {
    width: 750px;
    display: flex;
    flex-direction: column;
  }
  .right-sidebar #content #primary,
  .left-sidebar #content #primary {
    width: 100%;
    float: unset;
    padding-right: 0;
    padding-left: 0;
    order: 1;
  }
  .right-sidebar #content #secondary,
  .left-sidebar #content #secondary {
    width: auto;
    float: unset;
    margin-top: 30px;
    order: 2;
  }

  .no-sidebar #content {
    width: 720px;
  }
  .no-sidebar #content #secondary {
    display: none;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .rt-wrapper {
    width: 960px;
  }

  body.edu-axis-inner-page .right-sidebar #content,
  body.edu-axis-inner-page .left-sidebar #content {
    width: 960px;
  }
  body.edu-axis-inner-page .right-sidebar #content #primary,
  body.edu-axis-inner-page .left-sidebar #content #primary {
    width: 70%;
  }
  body.edu-axis-inner-page .right-sidebar #content #secondary,
  body.edu-axis-inner-page .left-sidebar #content #secondary {
    width: 30%;
  }
  body.edu-axis-inner-page .no-sidebar #content {
    width: 960px;
  }
  body.edu-axis-inner-page .no-sidebar #content #secondary {
    display: none;
  }
}
@media (min-width: 1200px) and (max-width: 1599.98px) {
  .rt-wrapper {
    width: 1140px;
  }

  .right-sidebar #content,
  .left-sidebar #content {
    width: 1140px;
  }
  .right-sidebar #content #primary,
  .left-sidebar #content #primary {
    width: 70%;
  }
  .right-sidebar #content #secondary,
  .left-sidebar #content #secondary {
    width: 30%;
  }

  .no-sidebar #content {
    width: 1140px;
  }
  .no-sidebar #content #secondary {
    display: none;
  }
}
@media (min-width: 1600px) {
  .rt-wrapper {
    width: 1320px;
  }

  body #content {
    width: 1320px;
  }
  body #content #primary {
    width: 75%;
  }
  body #content #secondary {
    width: 25%;
  }
  body.no-sidebar #content #primary {
    width: 100%;
  }
  body.no-sidebar #content #secondary {
    display: none;
  }
}
.edu-axis-front-page #content {
  padding: 0;
  width: 100%;
}

body.page-layout-framed {
  margin: 50px auto;
  padding: 0 50px;
  box-shadow: none;
}
body.page-layout-framed #page {
  position: relative;
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
}

#secondary {
  margin-top: 0;
}

.no-sidebar #secondary {
  display: none;
}

body.page-layout-framed .edu-axis-post-container,
body.page-layout-boxed .edu-axis-post-container {
  padding: 30px;
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
}

a:hover {
  text-decoration: none;
}

.center {
  text-align: center;
}

.no-background {
  background: transparent;
}

/*--------------------------------------------------------------
# Pagination
--------------------------------------------------------------*/
.navigation.pagination .nav-links,
nav.woocommerce-pagination {
  width: 100%;
  display: block;
  text-align: center;
}
.navigation.pagination .nav-links .page-numbers,
nav.woocommerce-pagination .page-numbers {
  padding: 7px 18px;
  display: inline-block;
  background: #f5f5f5;
  font-size: 16px;
  color: #272727;
  margin: 5px;
}
.navigation.pagination .nav-links span.current, .navigation.pagination .nav-links span:hover, .navigation.pagination .nav-links span:active, .navigation.pagination .nav-links a.current, .navigation.pagination .nav-links a:hover, .navigation.pagination .nav-links a:active,
nav.woocommerce-pagination span.current,
nav.woocommerce-pagination span:hover,
nav.woocommerce-pagination span:active,
nav.woocommerce-pagination a.current,
nav.woocommerce-pagination a:hover,
nav.woocommerce-pagination a:active {
  background: #1abc9c;
  color: #fff;
}

.posts-navigation,
.post-navigation {
  width: 100%;
  display: inline-block;
  border-bottom: 2px solid;
  border-color: #1abc9c;
  margin: 30px 0 0;
}

.posts-navigation .nav-links .nav-previous,
.posts-navigation .nav-links .nav-next,
.post-navigation .nav-links .nav-previous,
.post-navigation .nav-links .nav-next {
  position: relative;
  width: auto;
  background: #1abc9c;
}

.posts-navigation .nav-links .nav-previous a,
.posts-navigation .nav-links .nav-next a,
.post-navigation .nav-links .nav-previous a,
.post-navigation .nav-links .nav-next a {
  color: #fff;
  padding: 10px 15px;
  display: block;
}

.posts-navigation .nav-links .nav-previous,
.post-navigation .nav-links .nav-previous {
  float: left;
}

.posts-navigation .nav-links .nav-previous a:before,
.post-navigation .nav-links .nav-previous a:before {
  content: "";
  color: #1abc9c;
  position: absolute;
  top: 0;
  width: 0;
  height: 0;
  left: 100%;
  border-bottom: 45px solid;
  border-right: 45px solid transparent;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.posts-navigation .nav-links .nav-previous a:after,
.post-navigation .nav-links .nav-previous a:after {
  content: '\f100';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  padding-left: 10px;
}

.posts-navigation .nav-links .nav-next,
.post-navigation .nav-links .nav-next {
  float: right;
  text-align: right;
}

.posts-navigation .nav-links .nav-next a:after,
.post-navigation .nav-links .nav-next a:after {
  content: "";
  color: #1abc9c;
  position: absolute;
  top: 0;
  width: 0;
  height: 0;
  left: -45px;
  border-top: 45px solid transparent;
  border-right: 45px solid;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.posts-navigation .nav-links .nav-next a:before,
.post-navigation .nav-links .nav-next a:before {
  content: '\f101';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  padding-right: 10px;
}

/*--------------------------------------------------------------
# Sections
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Default
--------------------------------------------------------------*/
.edu-axis-section {
  background-color: #fff;
  padding: 45px 0px;
}
.edu-axis-section:nth-child(2) {
  background-color: #f3f3f3;
}
.edu-axis-section:first-child {
  z-index: 1111;
}
.edu-axis-section .rt-wrapper {
  padding-top: 20px;
  padding-bottom: 20px;
}
.edu-axis-section#services .rt-wrapper {
  text-align: center;
}
.edu-axis-section .section-header {
  text-align: center;
  margin-bottom: 45px;
}
.edu-axis-section .section-header .section-title {
  font-size: 35px;
  margin: 0 0 2px;
}
.edu-axis-section .section-header .section-subtitle {
  color: #656565;
  font-size: 14px;
  text-transform: capitalize;
  font-weight: 400;
  line-height: 1.8;
}
.edu-axis-section .section-contents .section-content {
  box-sizing: border-box;
  float: left;
  padding: 0 15px;
}

/*--------------------------------------------------------------
## Services
--------------------------------------------------------------*/
#featured-posts .section-header {
  margin-bottom: 25px;
}
#featured-posts .featured-post-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
#featured-posts article {
  flex-basis: 32%;
  margin-right: 2%;
  border-bottom: 5px solid #2d3e50;
  background-size: cover;
  background-position: center;
  display: block;
  box-shadow: -3px 0px 30px 0px rgba(0, 0, 0, 0.1);
  padding: 12px;
  position: relative;
  overflow: hidden;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
}
#featured-posts article:nth-child(3n+3) {
  margin-right: 0;
}
#featured-posts article h3.entry-title {
  position: absolute;
  z-index: 111;
  bottom: 0;
  width: 100%;
  left: 0;
  padding: 10px 15px;
  margin-bottom: 0px;
  background: #1abc9c;
}
#featured-posts article h3.entry-title a {
  color: #fff;
  font-size: 16px;
  font-size: 1rem;
}
#featured-posts article h3.entry-title:hover a {
  color: #2d3e50;
}
#featured-posts article .section-body {
  position: relative;
  z-index: 111;
  text-align: center;
  padding: 30px;
  background: transparent;
  min-height: 250px;
  height: 100%;
}
#featured-posts article .background-overlay {
  padding: 50px 0;
  background: rgba(255, 255, 255, 0.9);
  height: 100%;
}
@media (max-width: 360.98px) {
  #featured-posts article {
    flex-basis: 100%;
    margin-right: 0%;
    margin-bottom: 30px;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  #featured-posts article {
    flex-basis: 100%;
    margin-right: 0%;
    margin-bottom: 30px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  #featured-posts article {
    flex-basis: 100%;
    margin-right: 0%;
    margin-bottom: 30px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  #featured-posts article {
    flex-basis: 48%;
    margin-right: 4%;
    margin-bottom: 30px;
  }
  #featured-posts article:nth-child(2n+2) {
    margin-right: 0;
  }
}

/*--------------------------------------------------------------
## Header Image
--------------------------------------------------------------*/
.no-background {
  background: transparent !important;
  box-shadow: none !important;
}

.edu-axis-main-slider {
  visibility: visible;
}

.slick-initialized,
.slide-item:first-child {
  display: block;
}

#custom-header-media, .edu-axis-inner-page #custom-header-media .slide-item,
body.blog:not(.home) #custom-header-media .slide-item, .edu-axis-inner-page #custom-header-media .wp-custom-header img,
body.blog:not(.home) #custom-header-media .wp-custom-header img {
  background-color: rgba(0, 0, 0, 0.5);
  overflow: hidden;
}
#custom-header-media .wp-custom-header img, .edu-axis-inner-page #custom-header-media .slide-item .wp-custom-header img,
body.blog:not(.home) #custom-header-media .slide-item .wp-custom-header img, .edu-axis-inner-page #custom-header-media .wp-custom-header img .wp-custom-header img,
body.blog:not(.home) #custom-header-media .wp-custom-header img .wp-custom-header img {
  object-fit: cover;
  height: 100vh;
  width: 100%;
}
#custom-header-media .rt-slider-wrapper, .edu-axis-inner-page #custom-header-media .slide-item .rt-slider-wrapper,
body.blog:not(.home) #custom-header-media .slide-item .rt-slider-wrapper, .edu-axis-inner-page #custom-header-media .wp-custom-header img .rt-slider-wrapper,
body.blog:not(.home) #custom-header-media .wp-custom-header img .rt-slider-wrapper {
  z-index: 11;
  position: relative;
}
#custom-header-media .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .slide-item .edu-axis-main-slider,
body.blog:not(.home) #custom-header-media .slide-item .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .wp-custom-header img .edu-axis-main-slider,
body.blog:not(.home) #custom-header-media .wp-custom-header img .edu-axis-main-slider {
  min-height: 540px;
  height: 540px;
}
#custom-header-media .slide-item, .edu-axis-inner-page #custom-header-media .slide-item .slide-item,
body.blog:not(.home) #custom-header-media .slide-item .slide-item, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item {
  position: relative;
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 540px;
  height: 540px;
  background-position: center;
  display: flex;
  align-items: center;
}
#custom-header-media .slide-item.slick-slide .rt-wrapper,
body.blog:not(.home) #custom-header-media .slide-item .slide-item.slick-slide .rt-wrapper,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item.slick-slide .rt-wrapper {
  display: none;
}
#custom-header-media .slide-item .rt-overlay,
body.blog:not(.home) #custom-header-media .slide-item .slide-item .rt-overlay, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item .rt-overlay,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-overlay {
  display: block;
}
#custom-header-media .slide-item .rt-wrapper,
body.blog:not(.home) #custom-header-media .slide-item .slide-item .rt-wrapper, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item .rt-wrapper,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper {
  padding: 30px;
  border-radius: 3px;
  margin: 0 auto;
  position: relative;
  z-index: 1111;
}
#custom-header-media .slide-item .rt-wrapper .slider-caption,
body.blog:not(.home) #custom-header-media .slide-item .slide-item .rt-wrapper .slider-caption,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .slider-caption {
  max-width: 500px;
  width: 500px;
  float: right;
}
#custom-header-media .slide-item .rt-wrapper .cat-links,
body.blog:not(.home) #custom-header-media .slide-item .slide-item .rt-wrapper .cat-links,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .cat-links {
  color: #c5edf5;
}
#custom-header-media .slide-item .rt-wrapper .cat-links:before,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .cat-links:before {
  color: #c5edf5;
}
#custom-header-media .slide-item .rt-wrapper .cat-links a,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .cat-links a {
  color: #c5edf5;
  text-shadow: 1px 1px 1px #555;
}
#custom-header-media .slide-item .rt-wrapper .cat-links a:hover, #custom-header-media .slide-item .rt-wrapper .cat-links a:focus, #custom-header-media .slide-item .rt-wrapper .cat-links a:active {
  color: #fff;
}
#custom-header-media .slide-item .rt-wrapper .entry-header,
body.blog:not(.home) #custom-header-media .slide-item .slide-item .rt-wrapper .entry-header,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .entry-header {
  max-width: 100%;
  margin: 0;
}
#custom-header-media .slide-item .rt-wrapper .entry-header .entry-title {
  font-weight: 600;
  margin-bottom: 0;
  font-size: 35px;
  font-size: 2.1875rem;
  color: #fff;
  text-shadow: 1px 1px 1px #555;
  text-transform: uppercase;
}
#custom-header-media .slide-item .rt-wrapper .entry-header .entry-title a {
  color: #fff;
  display: block;
}
#custom-header-media .slide-item .rt-wrapper .entry-header .entry-title a:hover, #custom-header-media .slide-item .rt-wrapper .entry-header .entry-title a:focus, #custom-header-media .slide-item .rt-wrapper .entry-header .entry-title a:active {
  color: #fff;
}
#custom-header-media .slide-item .rt-wrapper .entry-meta,
body.blog:not(.home) #custom-header-media .slide-item .slide-item .rt-wrapper .entry-meta,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .entry-meta {
  padding: 10px 0 0;
  display: block;
}
#custom-header-media .slide-item .rt-wrapper .entry-meta > span:before {
  color: #fff;
}
#custom-header-media .slide-item .rt-wrapper .entry-meta p,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .entry-meta p {
  font-size: 18px;
  font-size: 1.125rem;
}
#custom-header-media .slide-item .rt-wrapper .entry-meta a,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .entry-meta a,
#custom-header-media .slide-item .rt-wrapper .entry-meta p,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .entry-meta p {
  color: #fff;
  text-shadow: 1px 1px 1px #555;
}
#custom-header-media .slide-item .rt-wrapper .entry-meta a:hover, #custom-header-media .slide-item .rt-wrapper .entry-meta a:focus, #custom-header-media .slide-item .rt-wrapper .entry-meta a:active,
#custom-header-media .slide-item .rt-wrapper .entry-meta p:hover,
#custom-header-media .slide-item .rt-wrapper .entry-meta p:focus,
#custom-header-media .slide-item .rt-wrapper .entry-meta p:active {
  color: #fff;
}
#custom-header-media .slide-item .rt-wrapper .entry-meta .read-more {
  display: none;
}
#custom-header-media .slide-item .rt-wrapper .read-more a,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .read-more a {
  margin-top: 0;
}
@media (max-width: 1599.98px) {
  #custom-header-media .slide-item .rt-wrapper .slider-caption,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .rt-wrapper .slider-caption,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .slider-caption {
    width: 420px;
  }
}
@media (max-width: 991.98px) {
  #custom-header-media .slide-item .rt-wrapper .slider-caption,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .rt-wrapper .slider-caption,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .slider-caption {
    width: 400px;
  }
  #custom-header-media .slide-item .rt-wrapper .entry-header .entry-title {
    font-size: 24px;
    font-size: 1.5rem;
  }
}
@media (max-width: 767.98px) {
  #custom-header-media .slide-item .rt-wrapper .slider-caption,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .rt-wrapper .slider-caption,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .slider-caption {
    width: 100%;
  }
}
@media (max-width: 575.98px) {
  #custom-header-media .slide-item .rt-wrapper .entry-header .entry-title {
    margin-bottom: 15x;
  }
  #custom-header-media .slide-item .rt-wrapper .entry-meta,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .rt-wrapper .entry-meta,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .rt-wrapper .entry-meta {
    display: none;
  }
}
#custom-header-media .slide-item:focus,
body.blog:not(.home) #custom-header-media .slide-item .slide-item:focus, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item:focus,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item:focus {
  outline: none;
}
#custom-header-media .slide-item.slick-current .rt-wrapper,
body.blog:not(.home) #custom-header-media .slide-item .slide-item.slick-current .rt-wrapper,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item.slick-current .rt-wrapper, #custom-header-media .slide-item.slick-active .rt-wrapper,
body.blog:not(.home) #custom-header-media .slide-item .slide-item.slick-active .rt-wrapper,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item.slick-active .rt-wrapper {
  display: block;
}
@media (max-width: 360.98px) {
  #custom-header-media .slide-item .wrapper,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .wrapper, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item .wrapper,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .wrapper {
    padding: 30px;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  #custom-header-media .slide-item .wrapper,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .wrapper, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item .wrapper,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .wrapper {
    padding: 30px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  #custom-header-media .slide-item .wrapper,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .wrapper, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item .wrapper,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .wrapper {
    min-width: auto;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  #custom-header-media .slide-item .wrapper,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .wrapper, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item .wrapper,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .wrapper {
    min-width: 360px;
  }
}
#custom-header-media .slide-item:not(.slick-active):after,
body.blog:not(.home) #custom-header-media .slide-item .slide-item:not(.slick-active):after,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item:not(.slick-active):after {
  z-index: 0;
}
#custom-header-media .slide-item.slick-cloned,
body.blog:not(.home) #custom-header-media .slide-item .slide-item.slick-cloned, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item.slick-cloned,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item.slick-cloned {
  visibility: hidden;
}
#custom-header-media .slick-dotted.slick-slider,
body.blog:not(.home) #custom-header-media .slide-item .slick-dotted.slick-slider, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slick-dotted.slick-slider,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slick-dotted.slick-slider {
  margin-bottom: 0;
}
#custom-header-media .slick-dots, .edu-axis-inner-page #custom-header-media .slide-item .slick-dots,
body.blog:not(.home) #custom-header-media .slide-item .slick-dots, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slick-dots,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slick-dots {
  bottom: 15px;
  text-align: right;
  padding-right: 15px;
}
#custom-header-media .slick-dots li button:hover,
body.blog:not(.home) #custom-header-media .slide-item .slick-dots li button:hover,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slick-dots li button:hover,
#custom-header-media .slick-dots li button:focus,
body.blog:not(.home) #custom-header-media .slide-item .slick-dots li button:focus,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slick-dots li button:focus,
#custom-header-media .slick-dots li button:active,
body.blog:not(.home) #custom-header-media .slide-item .slick-dots li button:active,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slick-dots li button:active {
  letter-spacing: initial;
}
#custom-header-media .slick-dots li button:before, .edu-axis-inner-page #custom-header-media .slide-item .slick-dots li button:before,
body.blog:not(.home) #custom-header-media .slide-item .slick-dots li button:before, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slick-dots li button:before,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slick-dots li button:before {
  font-size: 12px;
  color: #2d3e50;
  opacity: 0.9;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  border-radius: 50%;
}
#custom-header-media .slick-dots li.slick-active button:before,
body.blog:not(.home) #custom-header-media .slide-item .slick-dots li.slick-active button:before,
body.blog:not(.home) #custom-header-media .wp-custom-header img .slick-dots li.slick-active button:before {
  border: 1px solid #1abc9c;
  background: #2d3e50;
  opacity: 1;
  font-size: 15px;
}
@media (max-width: 360.98px) {
  #custom-header-media .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .slide-item .edu-axis-main-slider,
  body.blog:not(.home) #custom-header-media .slide-item .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .wp-custom-header img .edu-axis-main-slider,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .edu-axis-main-slider {
    min-height: 280px;
    height: 280px;
  }
  #custom-header-media .slide-item, .edu-axis-inner-page #custom-header-media .slide-item .slide-item,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item {
    min-height: 280px;
    height: 280px;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  #custom-header-media .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .slide-item .edu-axis-main-slider,
  body.blog:not(.home) #custom-header-media .slide-item .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .wp-custom-header img .edu-axis-main-slider,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .edu-axis-main-slider {
    min-height: 280px;
    height: 280px;
  }
  #custom-header-media .slide-item, .edu-axis-inner-page #custom-header-media .slide-item .slide-item,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item {
    min-height: 280px;
    height: 280px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  #custom-header-media .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .slide-item .edu-axis-main-slider,
  body.blog:not(.home) #custom-header-media .slide-item .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .wp-custom-header img .edu-axis-main-slider,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .edu-axis-main-slider {
    min-height: 280px;
    height: 280px;
  }
  #custom-header-media .slide-item, .edu-axis-inner-page #custom-header-media .slide-item .slide-item,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item {
    min-height: 280px;
    height: 280px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  #custom-header-media .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .slide-item .edu-axis-main-slider,
  body.blog:not(.home) #custom-header-media .slide-item .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .wp-custom-header img .edu-axis-main-slider,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .edu-axis-main-slider {
    min-height: 280px;
    height: 280px;
  }
  #custom-header-media .slide-item, .edu-axis-inner-page #custom-header-media .slide-item .slide-item,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item {
    min-height: 280px;
    height: 280px;
  }
  #custom-header-media .slide-item .entry-header .entry-title,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .entry-header .entry-title,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .entry-header .entry-title {
    font-size: 28px;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  #custom-header-media .slide-item .entry-header .entry-title,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .entry-header .entry-title,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .entry-header .entry-title {
    font-size: 32px;
  }
}
@media (min-width: 1200px) and (max-width: 1599.98px) {
  #custom-header-media .slide-item .entry-header .entry-title,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .entry-header .entry-title,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .entry-header .entry-title {
    font-size: 35px;
  }
}
@media (min-width: 1600px) {
  #custom-header-media .slide-item, .edu-axis-inner-page #custom-header-media .slide-item .slide-item,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item, .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item {
    min-height: 540px;
    height: 540px;
  }
  #custom-header-media .slide-item .entry-header .entry-title,
  body.blog:not(.home) #custom-header-media .slide-item .slide-item .entry-header .entry-title,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .entry-header .entry-title {
    font-size: 35px;
  }
}

.absolute-header #custom-header-media .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .absolute-header .slide-item .edu-axis-main-slider,
.absolute-header body.blog:not(.home) #custom-header-media .slide-item .edu-axis-main-slider,
body.blog:not(.home) #custom-header-media .absolute-header .slide-item .edu-axis-main-slider, .absolute-header .edu-axis-inner-page #custom-header-media .wp-custom-header img .edu-axis-main-slider, .edu-axis-inner-page #custom-header-media .wp-custom-header .absolute-header img .edu-axis-main-slider,
.absolute-header body.blog:not(.home) #custom-header-media .wp-custom-header img .edu-axis-main-slider,
body.blog:not(.home) #custom-header-media .wp-custom-header .absolute-header img .edu-axis-main-slider {
  height: 100vh;
}
.absolute-header #custom-header-media .slide-item, .edu-axis-inner-page #custom-header-media .absolute-header .slide-item .slide-item,
.absolute-header body.blog:not(.home) #custom-header-media .slide-item .slide-item,
body.blog:not(.home) #custom-header-media .absolute-header .slide-item .slide-item, .absolute-header .edu-axis-inner-page #custom-header-media .wp-custom-header img .slide-item, .edu-axis-inner-page #custom-header-media .wp-custom-header .absolute-header img .slide-item,
.absolute-header body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item,
body.blog:not(.home) #custom-header-media .wp-custom-header .absolute-header img .slide-item {
  height: 100vh;
}
.absolute-header #custom-header-media .slide-item .wrapper, .edu-axis-inner-page #custom-header-media .absolute-header .slide-item .slide-item .wrapper,
.absolute-header body.blog:not(.home) #custom-header-media .slide-item .slide-item .wrapper,
body.blog:not(.home) #custom-header-media .absolute-header .slide-item .slide-item .wrapper, .edu-axis-inner-page #custom-header-media .wp-custom-header .absolute-header img .slide-item .wrapper,
.absolute-header body.blog:not(.home) #custom-header-media .wp-custom-header img .slide-item .wrapper,
body.blog:not(.home) #custom-header-media .wp-custom-header .absolute-header img .slide-item .wrapper {
  margin-top: 60px;
}

.edu-axis-inner-page #custom-header-media, .edu-axis-inner-page #custom-header-media .slide-item,
.edu-axis-inner-page body.blog:not(.home) #custom-header-media .slide-item,
body.blog:not(.home) #custom-header-media .edu-axis-inner-page .slide-item, .edu-axis-inner-page #custom-header-media .wp-custom-header img,
.edu-axis-inner-page body.blog:not(.home) #custom-header-media .wp-custom-header img,
body.blog:not(.home) #custom-header-media .wp-custom-header .edu-axis-inner-page img,
body.blog:not(.home) #custom-header-media,
.edu-axis-inner-page #custom-header-media body.blog:not(.home) .slide-item,
body.blog:not(.home) #custom-header-media .slide-item,
.edu-axis-inner-page #custom-header-media .wp-custom-header body.blog:not(.home) img,
body.blog:not(.home) #custom-header-media .wp-custom-header img {
  height: 150px;
  min-height: 150px;
}

@media screen and (min-width: 75em) {
  #custom-header-media, .edu-axis-inner-page #custom-header-media .slide-item,
  body.blog:not(.home) #custom-header-media .slide-item, .edu-axis-inner-page #custom-header-media .wp-custom-header img,
  body.blog:not(.home) #custom-header-media .wp-custom-header img {
    overflow: hidden;
  }
  #custom-header-media .wp-custom-header img, .edu-axis-inner-page #custom-header-media .slide-item .wp-custom-header img,
  body.blog:not(.home) #custom-header-media .slide-item .wp-custom-header img, .edu-axis-inner-page #custom-header-media .wp-custom-header img .wp-custom-header img,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .wp-custom-header img {
    max-height: 100%;
  }
  #custom-header-media .entry-header .entry-title,
  body.blog:not(.home) #custom-header-media .slide-item .entry-header .entry-title, .edu-axis-inner-page #custom-header-media .wp-custom-header img .entry-header .entry-title,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .entry-header .entry-title {
    font-size: 35px;
  }
}
.edu-axis-main-slider .rt-overlay {
  background: transparent;
  opacity: 1;
}
.edu-axis-main-slider .rt-overlay:after {
  background: #1abc9c;
  content: "";
  height: 2000px;
  opacity: 0.3;
  position: absolute;
  right: 0;
  -webkit-clip-path: polygon(62% 0, 100% 0, 100% 100%, 22.5% 100%);
  -moz-clip-path: polygon(62% 0, 100% 0, 100% 100%, 22.5% 100%);
  clip-path: polygon(62% 0, 100% 0, 100% 100%, 22.5% 100%);
  width: 2000px;
  z-index: 9;
  -webkit-animation: 700ms ease-in-out 0s normal none 1 running slideInLeft;
  animation: 700ms ease-in-out 0s normal none 1 running slideInLeft;
}

@media (min-width: 1200px) and (max-width: 1599.98px) {
  .edu-axis-main-slider .rt-overlay:after {
    -webkit-clip-path: polygon(66.5% 0, 100% 0, 100% 100%, 23.5% 100%);
    -moz-clip-path: polygon(66.5% 0, 100% 0, 100% 100%, 23.5% 100%);
    clip-path: polygon(66.5% 0, 100% 0, 100% 100%, 23.5% 100%);
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .edu-axis-main-slider .rt-overlay:after {
    -webkit-clip-path: polygon(71.8% 0, 100% 0, 100% 100%, 28.5% 100%);
    -moz-clip-path: polygon(71.8% 0, 100% 0, 100% 100%, 28.5% 100%);
    clip-path: polygon(71.8% 0, 100% 0, 100% 100%, 28.5% 100%);
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .edu-axis-main-slider .rt-overlay:after {
    -webkit-clip-path: polygon(73.8% 0, 100% 0, 100% 100%, 35.5% 100%);
    -moz-clip-path: polygon(73.8% 0, 100% 0, 100% 100%, 35.5% 100%);
    clip-path: polygon(73.8% 0, 100% 0, 100% 100%, 35.5% 100%);
  }
}
@media (max-width: 767.98px) {
  .edu-axis-main-slider .rt-overlay:after {
    -webkit-clip-path: polygon(88% 0, 100% 0, 100% 100%, 55% 100%);
    -moz-clip-path: polygon(88% 0, 100% 0, 100% 100%, 55% 100%);
    clip-path: polygon(88% 0, 100% 0, 100% 100%, 55% 100%);
  }
  .edu-axis-main-slider .rt-wrapper {
    text-align: right;
  }
}
@media (max-width: 575.98px) {
  #custom-header-media .edu-axis-main-slider .rt-wrapper,
  body.blog:not(.home) #custom-header-media .slide-item .edu-axis-main-slider .rt-wrapper, .edu-axis-inner-page #custom-header-media .wp-custom-header img .edu-axis-main-slider .rt-wrapper,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .edu-axis-main-slider .rt-wrapper {
    max-width: 340px;
    text-align: center;
  }
  #custom-header-media .edu-axis-main-slider .rt-wrapper .entry-header,
  body.blog:not(.home) #custom-header-media .slide-item .edu-axis-main-slider .rt-wrapper .entry-header,
  body.blog:not(.home) #custom-header-media .wp-custom-header img .edu-axis-main-slider .rt-wrapper .entry-header {
    margin-bottom: 15px;
  }
  #custom-header-media .edu-axis-main-slider .rt-wrapper .entry-header .entry-title {
    font-size: 22px;
  }
}
.edu-axis-main-slider {
  max-height: 100%;
}
.edu-axis-main-slider .wp-custom-header {
  max-height: 100%;
}
.edu-axis-main-slider .slick-arrow {
  z-index: 1;
  top: calc(50% - 10px);
  width: 60px;
  height: 60px;
}
.edu-axis-main-slider .slick-arrow::before {
  font-size: 30px;
  line-height: 50px;
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  padding: 5px 10px;
  background: #2d3e50;
  opacity: 0.5;
}
.edu-axis-main-slider .slick-arrow.slick-prev {
  transform: translate(-100%, -50%);
  left: 0px;
}
.edu-axis-main-slider .slick-arrow.slick-prev:before {
  content: '\f104';
}
.edu-axis-main-slider .slick-arrow.slick-next {
  transform: translate(100%, -50%);
  right: 0px;
}
.edu-axis-main-slider .slick-arrow.slick-next:before {
  content: '\f105';
}
.edu-axis-main-slider .slick-arrow:hover {
  letter-spacing: 0px;
  opacity: 1;
}
.edu-axis-main-slider .slick-arrow:active, .edu-axis-main-slider .slick-arrow:focus {
  transform: translate(0, -50%);
}
.edu-axis-main-slider:hover .slick-arrow, .edu-axis-main-slider:active .slick-arrow, .edu-axis-main-slider:focus .slick-arrow {
  transform: translate(0, -50%);
}

/*--------------------------------------------------------------
## About us
--------------------------------------------------------------*/
section#about-us.edu-axis-section {
  padding: 50px 0;
}
section#about-us.edu-axis-section article.has-featured-image {
  display: flex;
  text-align: left;
  align-items: center;
}
section#about-us.edu-axis-section article.has-featured-image .rt-featured-image {
  min-height: 140px;
}
section#about-us.edu-axis-section article.has-featured-image img {
  position: relative;
  z-index: 111;
}
section#about-us.edu-axis-section article.has-featured-image .section-header {
  text-align: left;
  margin-bottom: 0;
}
section#about-us.edu-axis-section article .entry-content {
  margin-bottom: 25px;
}
section#about-us.edu-axis-section article .entry-content .read-more {
  display: none;
}
@media (max-width: 360.98px) {
  section#about-us.edu-axis-section article.has-featured-image {
    flex-direction: column;
  }
  section#about-us.edu-axis-section article.has-featured-image .rt-featured-image {
    margin-bottom: 20px;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container {
    margin-left: 0;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container .section-title {
    margin-bottom: 0;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container .entry-content {
    margin-top: 0;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  section#about-us.edu-axis-section article.has-featured-image {
    flex-direction: column;
  }
  section#about-us.edu-axis-section article.has-featured-image .rt-featured-image {
    margin-bottom: 20px;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container {
    margin-left: 0;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container .section-title {
    margin-bottom: 0;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container .entry-content {
    margin-top: 0;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  section#about-us.edu-axis-section article.has-featured-image {
    flex-direction: column;
  }
  section#about-us.edu-axis-section article.has-featured-image .rt-featured-image {
    margin-bottom: 20px;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container {
    margin-left: 0;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container .section-title {
    margin-bottom: 0;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container .entry-content {
    margin-top: 0;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  section#about-us.edu-axis-section article.has-featured-image .featured-post-image {
    flex: 1;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container {
    margin-left: 50px;
    flex: 1.5;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  section#about-us.edu-axis-section article.has-featured-image .featured-post-image {
    flex: 1;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container {
    margin-left: 50px;
    flex: 1;
  }
}
@media (min-width: 1200px) and (max-width: 1599.98px) {
  section#about-us.edu-axis-section article.has-featured-image .featured-post-image {
    flex: 1;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container {
    margin-left: 50px;
    flex: 1;
  }
}
@media (min-width: 1600px) {
  section#about-us.edu-axis-section article.has-featured-image .featured-post-image {
    flex: 1;
  }
  section#about-us.edu-axis-section article.has-featured-image .entry-container {
    margin-left: 50px;
    flex: 1;
  }
}

/*--------------------------------------------------------------
## Call to action
--------------------------------------------------------------*/
#call-to-action {
  background-repeat: no-repeat;
  background-position: center center;
  background-attachment: fixed;
  background-size: cover;
  text-align: center;
  background-size: cover;
  background-position: 50%;
}
#call-to-action .rt-overlay {
  display: block;
  opacity: 0.4;
}
#call-to-action .rt-wrapper {
  padding: 100px 0;
  z-index: 111;
}
#call-to-action .rt-wrapper .section-header {
  margin-bottom: 14px;
}
#call-to-action .rt-wrapper .section-header .section-title {
  color: #fff;
  font-weight: 500;
}
#call-to-action .rt-wrapper .section-content {
  color: #fff;
  margin-bottom: 20px;
}

/*--------------------------------------------------------------
## Portfolio gallery
--------------------------------------------------------------*/
#latest-portfolios .grid {
  margin: -5px;
}
#latest-portfolios .grid .grid-item {
  width: 50%;
  float: left;
  padding: 5px;
}
#latest-portfolios .grid .grid-item .grid-inner-wrapper {
  position: relative;
  overflow: hidden;
}
#latest-portfolios .grid .grid-item .grid-inner-wrapper .rt-overlay {
  opacity: 0;
  visibility: hidden;
  background-color: #fff;
}
#latest-portfolios .grid .grid-item .grid-inner-wrapper img {
  object-fit: cover;
  width: 100%;
}
#latest-portfolios .grid .grid-item .grid-inner-wrapper .hover-item {
  position: absolute;
  text-align: center;
  left: 0;
  right: 0;
  width: 100%;
  bottom: 0;
  padding: 5px;
  background-color: rgba(255, 255, 255, 0.9);
}
#latest-portfolios .grid .grid-item .grid-inner-wrapper .hover-item h5 {
  font-size: 13px;
  position: relative;
  margin-bottom: 0;
}
#latest-portfolios .grid .grid-item .grid-inner-wrapper .hover-item h5 a {
  color: #2e2e2e;
}
#latest-portfolios .grid .grid-item .grid-inner-wrapper .hover-item h5 a:hover, #latest-portfolios .grid .grid-item .grid-inner-wrapper .hover-item h5 a:focus {
  color: #cf3140;
}
#latest-portfolios .grid .grid-item .grid-inner-wrapper .hover-item p {
  display: none;
}
#latest-portfolios .grid .grid-item:nth-child(1n) img {
  height: 200px;
}
#latest-portfolios .grid .grid-item:nth-child(2n) img {
  height: 150px;
}
#latest-portfolios .grid .grid-item:nth-child(3n) img {
  height: 150px;
}
#latest-portfolios .grid .grid-item:nth-child(4n) img {
  height: 150px;
}
#latest-portfolios .grid .grid-item:nth-child(5n) img {
  height: 200px;
}
#latest-portfolios .grid .grid-item:nth-child(6n) img {
  height: 150px;
}

/*--------------------------------------------------------------
## Why Us
--------------------------------------------------------------*/
.edu-axis-section#rt-why-us .rt-why-us-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single {
  flex-basis: 49%;
  padding: 20px;
  margin-bottom: 20px;
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single:hover {
  -webkit-transform: translate(0%, -3%);
  -moz-transform: translate(0%, -3%);
  transform: translate(0%, -3%);
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-icon {
  display: table-cell;
  vertical-align: middle;
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-icon-wrap {
  border: 5px solid #1abc9c;
  border-radius: 50%;
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-icon i {
  color: #1abc9c;
  display: block;
  font-size: 40px;
  line-height: 50px;
  width: 70px;
  height: 70px;
  padding: 10px;
  text-align: center;
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content {
  width: calc( 100% - 150px );
  display: table-cell;
  padding-left: 20px;
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content h2 {
  color: #2d3e50;
  margin-bottom: 5px;
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content h2 a {
  color: #2d3e50;
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content h2 a:hover, .edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content h2 a:focus {
  color: #1abc9c;
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content p {
  font-size: 14px;
  line-height: 1.8;
}
.edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content .read-more {
  display: none;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-icon i {
    font-size: 35px;
  }
  .edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content {
    width: calc( 100% - 90px );
  }
}
@media (max-width: 767.98px) {
  .edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single {
    flex-basis: 100%;
  }
  .edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-icon i {
    font-size: 35px;
  }
}
@media (max-width: 575.98px) {
  .edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-icon {
    vertical-align: top;
  }
  .edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-icon i {
    font-size: 16px;
    line-height: 20px;
    width: 40px;
    height: 40px;
  }
  .edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content {
    padding-left: 0;
  }
  .edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content h2 {
    margin-top: 5px;
    padding-left: 10px;
  }
  .edu-axis-section#rt-why-us .rt-why-us-content .rt-why-us-single .rt-why-us-content p {
    margin: 15px 0 0 -50px;
  }
}

/*--------------------------------------------------------------
## Counter
--------------------------------------------------------------*/
#counter {
  background-color: #efefef;
}
#counter .hentry {
  text-align: center;
  padding: 14px 5px 21px 5px;
  margin-bottom: 35px;
}
#counter .hentry .statwrap .stat-count {
  font-size: 52px;
}
#counter .hentry .statwrap h5 {
  font-weight: 400;
  text-transform: uppercase;
  color: #000;
  margin: 0;
}
#counter .hentry:nth-child(odd) {
  border-right: 1px solid #d8d8d8;
}
#counter .col-2 .hentry,
#counter .col-3 .hentry,
#counter .col-4 .hentry {
  float: left;
}
#counter .col-1 .hentry {
  border-right: none;
}
#counter .col-2 .hentry {
  width: 50%;
}
#counter .col-2 .hentry:nth-child(2n+1),
#counter .col-4 .hentry:nth-child(2n+1) {
  clear: left;
}
#counter .col-1 .hentry:last-child,
#counter .col-2 .hentry:nth-last-child(-n+2),
#counter .col-3 .hentry:last-child,
#counter .col-4 .hentry:nth-last-child(-n+2) {
  margin-bottom: 0;
}
#counter .col-3 .hentry {
  width: 100%;
  border-right: none;
}
#counter .col-4 .hentry {
  width: 50%;
}

/*--------------------------------------------------------------
## Latest Posts
--------------------------------------------------------------*/
.edu-axis-section.latest-blog .section-content {
  display: flex;
  flex-wrap: wrap;
}
.edu-axis-section.latest-blog .section-content article {
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
  flex-basis: 32%;
  margin-right: 2%;
  position: relative;
  padding-bottom: 55px;
}
.edu-axis-section.latest-blog .section-content article:nth-child(3n+3) {
  margin-right: 0;
}
.edu-axis-section.latest-blog .section-content article .entry-container {
  padding: 20px 0 0;
}
.edu-axis-section.latest-blog .section-content article .entry-container .entry-title a {
  font-size: 16px;
  font-size: 1rem;
}
.edu-axis-section.latest-blog .section-content article .entry-container .entry-meta,
.edu-axis-section.latest-blog .section-content article .entry-container .entry-header,
.edu-axis-section.latest-blog .section-content article .entry-container .entry-footer {
  padding: 0 20px;
}
.edu-axis-section.latest-blog .section-content article .entry-container .entry-footer {
  border-top: 1px solid #eee;
  padding-top: 15px;
  padding-bottom: 15px;
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
  position: absolute;
  bottom: 0;
  width: 100%;
}
.edu-axis-section.latest-blog .section-content article .entry-container .entry-footer .comment-meta:after {
  content: '';
}
.edu-axis-section.latest-blog .section-content article .entry-container .entry-footer .entry-footer-posted-on {
  float: right;
}
.edu-axis-section.latest-blog .section-content article .entry-container .entry-footer .entry-footer-posted-on a {
  color: #b2b2b2;
}
.edu-axis-section.latest-blog .section-content article .entry-container .entry-footer .entry-footer-posted-on a:hover, .edu-axis-section.latest-blog .section-content article .entry-container .entry-footer .entry-footer-posted-on a:focus {
  color: #1abc9c;
}
.edu-axis-section.latest-blog .section-content article .rt-featured-image img {
  position: relative;
  z-index: 11;
}
.edu-axis-section.latest-blog .section-content article .rt-featured-image:after {
  content: '16:9';
  position: absolute;
  color: #ccc;
  z-index: 0;
  text-align: center;
  top: 60%;
  display: block;
  width: 100%;
}
.edu-axis-section.latest-blog .section-content article .read-more {
  display: none;
  position: absolute;
  z-index: 111;
  left: calc( 50% - 70px );
  top: calc( 50% - 15px );
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.edu-axis-section.latest-blog .section-content article .read-more a {
  display: block;
}
.edu-axis-section.latest-blog .section-content article .rt-overlay {
  display: block;
  width: 0;
  height: 0;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  top: 50%;
  left: 50%;
}
.edu-axis-section.latest-blog .section-content article:hover .rt-overlay, .edu-axis-section.latest-blog .section-content article:focus .rt-overlay {
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.edu-axis-section.latest-blog .section-content article:hover .read-more, .edu-axis-section.latest-blog .section-content article:focus .read-more {
  display: block;
}
.edu-axis-section.latest-blog .section-content.list-mode {
  flex-direction: column;
}
@media (max-width: 360.98px) {
  .edu-axis-section.latest-blog .section-content article {
    flex-basis: 100%;
    margin-right: 0%;
    margin-bottom: 30px;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  .edu-axis-section.latest-blog .section-content article {
    flex-basis: 100%;
    margin-right: 0%;
    margin-bottom: 30px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .edu-axis-section.latest-blog .section-content article {
    flex-basis: 100%;
    margin-right: 0%;
    margin-bottom: 30px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .edu-axis-section.latest-blog .section-content article {
    flex-basis: 48%;
    margin-right: 4%;
    margin-bottom: 30px;
  }
  .edu-axis-section.latest-blog .section-content article:nth-child(2n+2) {
    margin-right: 0;
  }
  .edu-axis-section.latest-blog .section-content article:nth-child(2n+3) {
    margin-right: 4%;
  }
  .edu-axis-section.latest-blog .section-content article .rt-featured-image {
    min-height: 192px;
  }
}
@media (min-width: 1200px) and (max-width: 1599.98px) {
  .edu-axis-section.latest-blog .section-content article .rt-featured-image {
    min-height: 198px;
  }
}
@media (min-width: 1600px) {
  .edu-axis-section.latest-blog .section-content article .rt-featured-image {
    min-height: 230px;
  }
}
@media (max-width: 1199.98px) {
  .edu-axis-section.latest-blog .section-content article .rt-featured-image {
    min-height: 165px;
  }
}
@media (max-width: 991.98px) {
  .edu-axis-section.latest-blog .section-content article .rt-featured-image {
    min-height: 192px;
  }
}

/*--------------------------------------------------------------
## Contact form
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Instagram
--------------------------------------------------------------*/
#sb_instagram {
  position: relative;
}
#sb_instagram .sbi_photo_wrap a {
  opacity: 1 !important;
  position: relative;
}
#sb_instagram .sbi_photo_wrap a:before {
  background-color: transparent;
  bottom: 0;
  content: "";
  display: inline-block;
  height: 50px;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  top: 0;
  width: 50px;
}

#sb_instagram .sbi_photo_wrap a:hover:before, #sb_instagram .sbi_photo_wrap a:focus:before, #sb_instagram .sbi_photo_wrap a:active:before {
  background-color: rgba(0, 0, 0, 0.3);
  height: 100%;
  width: 100%;
  z-index: 999;
}

#sb_instagram .sbi_follow_btn {
  left: 50%;
  position: absolute;
  top: 50%;
  z-index: 999;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

#sb_instagram .sbi_follow_btn a {
  background-color: #fff;
  box-shadow: 7px 7px 0px rgba(0, 0, 0, 0.3);
  color: #1a1a1a;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: flex;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
  font-size: 14px;
  font-weight: 600;
  padding: 15px;
  text-transform: uppercase;
  vertical-align: middle;
  letter-spacing: 0.5px;
}

#sb_instagram .sbi_follow_btn a svg {
  margin-top: 2px;
}

#sb_instagram .sbi_follow_btn a:hover {
  box-shadow: inherit;
  background-color: #1a1a1a;
  color: #ffffff;
}

/*--------------------------------------------------------------
# Breadcrumb
--------------------------------------------------------------*/
#edu-axis-breadcrumb {
  background-color: #fff;
  padding: 19px 0;
  line-height: 1;
  border-bottom: 1px solid #eee;
}
#edu-axis-breadcrumb #crumbs {
  color: #1abc9c;
}
#edu-axis-breadcrumb #crumbs .current {
  color: #939393;
}

/*--------------------------------------------------------------
# Back to Top
--------------------------------------------------------------*/
.back-to-top {
  background: #1abc9c;
  bottom: 50px;
  margin-right: 30px;
  position: fixed;
  right: 30px;
  z-index: 5555;
  color: #fff;
  bottom: -150px;
}
.back-to-top:hover, .back-to-top:active, .back-to-top:focus {
  background: #2d3e50;
  color: #fff;
}
.back-to-top i {
  padding: 10px;
}

/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Footer widget area
--------------------------------------------------------------*/
.footer-widget-area {
  padding: 63px 0;
  background-color: #000;
  color: rgba(255, 255, 255, 0.5);
  line-height: 2.2;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}
.footer-widget-area .hentry {
  margin-bottom: 42px;
}
.footer-widget-area .hentry .widget-title {
  color: #fff;
}
.footer-widget-area .hentry ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.footer-widget-area .hentry ul li {
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 10px;
}
.footer-widget-area .hentry ul li a {
  color: rgba(255, 255, 255, 0.5);
}
.footer-widget-area .hentry ul li a:hover, .footer-widget-area .hentry ul li a:focus {
  color: #1abc9c;
}
.footer-widget-area .hentry ul li:last-child {
  margin-bottom: 0;
}
.footer-widget-area .hentry .widget_archive li {
  text-align: right;
}
.footer-widget-area .hentry .widget_archive li a {
  float: left;
}
.footer-widget-area .hentry:last-child {
  margin-bottom: 0;
}

.footer-widget-area.footer-column-1 .hentry {
  text-align: center;
}
.footer-widget-area.footer-column-1 .hentry .widget_archive li {
  text-align: center;
}
.footer-widget-area.footer-column-1 .hentry .widget_archive li a {
  float: none;
}

.footer-widget-area .rt-wrapper {
  display: flex;
  flex-wrap: wrap;
}
.footer-widget-area.footer-column-4 .rt-wrapper {
  justify-content: center;
}
.footer-widget-area.footer-column-4 .rt-wrapper .hentry {
  flex-basis: 23.5%;
}
.footer-widget-area.footer-column-4 .rt-wrapper .hentry:not(:last-child) {
  margin-right: 2%;
}
@media (max-width: 991.98px) {
  .footer-widget-area.footer-column-4 .rt-wrapper .hentry {
    flex-basis: 32%;
    margin-right: 2%;
  }
  .footer-widget-area.footer-column-4 .rt-wrapper .hentry:nth-child(3n) {
    margin-right: 0;
  }
}
@media (max-width: 767.98px) {
  .footer-widget-area.footer-column-4 .rt-wrapper .hentry {
    flex-basis: 49%;
    margin-right: 2%;
  }
  .footer-widget-area.footer-column-4 .rt-wrapper .hentry:nth-child(2n) {
    margin-right: 0;
  }
  .footer-widget-area.footer-column-4 .rt-wrapper .hentry:nth-child(3n) {
    margin-right: 2%;
  }
}
@media (max-width: 575.98px) {
  .footer-widget-area.footer-column-4 .rt-wrapper .hentry {
    flex-basis: 100%;
    margin-right: 0%;
  }
}
.footer-widget-area.footer-column-3 .rt-wrapper {
  justify-content: center;
}
.footer-widget-area.footer-column-3 .rt-wrapper .hentry {
  flex-basis: 32%;
}
.footer-widget-area.footer-column-3 .rt-wrapper .hentry:not(:last-child) {
  margin-right: 2%;
}
@media (max-width: 991.98px) {
  .footer-widget-area.footer-column-3 .rt-wrapper .hentry {
    flex-basis: 49%;
    margin-right: 2%;
  }
  .footer-widget-area.footer-column-3 .rt-wrapper .hentry:nth-child(2n) {
    margin-right: 0;
  }
}
@media (max-width: 575.98px) {
  .footer-widget-area.footer-column-3 .rt-wrapper .hentry {
    flex-basis: 100%;
    margin-right: 0%;
  }
}
.footer-widget-area.footer-column-2 .rt-wrapper .hentry {
  flex-basis: 49%;
}
.footer-widget-area.footer-column-2 .rt-wrapper .hentry:not(:last-child) {
  margin-right: 2%;
}
@media (max-width: 767.98px) {
  .footer-widget-area.footer-column-2 .rt-wrapper .hentry {
    flex-basis: 100%;
    margin-right: 0%;
  }
}
.footer-widget-area.footer-column-1 .rt-wrapper .hentry {
  flex-basis: 100%;
}

/*--------------------------------------------------------------
## Site Info
--------------------------------------------------------------*/
.site-info {
  background-color: #1b1b1b;
}
.site-info .rt-footer {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
}
.site-info .rt-footer .copyright-info,
.site-info .rt-footer .credit-info {
  flex-basis: 50%;
  margin: 0;
  padding: 5px 10px;
  color: #fff;
}
.site-info .rt-footer .copyright-info {
  text-align: left;
}
.site-info .rt-footer .credit-info {
  text-align: right;
}
@media (max-width: 360.98px) {
  .site-info .rt-footer .copyright-info,
  .site-info .rt-footer .credit-info {
    flex-basis: 100%;
    text-align: center;
  }
}
@media (min-width: 361px) and (max-width: 575.98px) {
  .site-info .rt-footer .copyright-info,
  .site-info .rt-footer .credit-info {
    flex-basis: 100%;
    text-align: center;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .site-info .rt-footer .copyright-info,
  .site-info .rt-footer .credit-info {
    flex-basis: 100%;
    text-align: center;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .site-info .rt-footer .copyright-info,
  .site-info .rt-footer .credit-info {
    flex-basis: 100%;
    text-align: center;
  }
}
.site-info ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
