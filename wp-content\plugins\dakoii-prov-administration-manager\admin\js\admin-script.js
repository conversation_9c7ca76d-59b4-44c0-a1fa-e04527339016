/**
 * Provincial Administration Manager - Admin JavaScript
 */

(function($) {
    'use strict';

    // Initialize admin functionality
    $(document).ready(function() {
        initTabs();
        initMediaUpload();
        initFormValidation();
        initTooltips();
        initConfirmDialogs();
        initAutoSave();
        initSlideshowManagement();
        initMapFileUpload();
    });

    // Tab functionality
    function initTabs() {
        $('.esp-tab-nav button').on('click', function() {
            const tabId = $(this).data('tab');
            
            // Update nav
            $('.esp-tab-nav button').removeClass('active');
            $(this).addClass('active');
            
            // Update content
            $('.esp-tab-content').removeClass('active');
            $('#' + tabId).addClass('active');
        });
    }

    // Media upload functionality
    function initMediaUpload() {
        $('.esp-upload-photo').on('click', function(e) {
            e.preventDefault();

            // Check if wp.media is available
            if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
                alert('Media uploader not available. Please refresh the page.');
                return;
            }

            const button = $(this);
            const targetInput = button.data('target');

            const mediaUploader = wp.media({
                title: 'Select Governor Photo',
                button: {
                    text: 'Use this photo'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });

            mediaUploader.on('select', function() {
                const attachment = mediaUploader.state().get('selection').first().toJSON();

                if (targetInput) {
                    $('#' + targetInput).val(attachment.id);

                    // Update preview immediately
                    const formRow = button.closest('.esp-form-row');
                    const existingPreview = formRow.find('.esp-media-preview');
                    const uploadArea = formRow.find('.esp-media-upload');

                    // Create new preview HTML
                    const newPreviewHtml = '<div class="esp-media-preview"><img src="' + attachment.url + '" style="max-width: 300px; height: auto; border-radius: 8px; object-fit: cover;" alt="Governor Photo"><p><button type="button" class="button esp-upload-photo" data-target="' + targetInput + '">Change Photo</button><input type="hidden" id="' + targetInput + '" name="governor_photo" value="' + attachment.id + '"><div style="font-size: 11px; color: #666; margin-top: 5px;">Photo ID: ' + attachment.id + '</div></p></div>';

                    if (existingPreview.length > 0) {
                        existingPreview.replaceWith(newPreviewHtml);
                    } else if (uploadArea.length > 0) {
                        uploadArea.replaceWith(newPreviewHtml);
                    }

                    // Re-initialize upload functionality for new button
                    initMediaUpload();

                    // Show success message
                    showNotification('Photo selected successfully. Don\'t forget to save the form!', 'success');
                }
            });

            mediaUploader.open();
        });
    }

    // Form validation
    function initFormValidation() {
        $('form').on('submit', function(e) {
            let isValid = true;
            
            // Check required fields
            $(this).find('[required]').each(function() {
                if (!$(this).val().trim()) {
                    isValid = false;
                    $(this).addClass('error');
                    showFieldError($(this), 'This field is required');
                } else {
                    $(this).removeClass('error');
                    hideFieldError($(this));
                }
            });
            
            // Check email fields
            $(this).find('input[type="email"]').each(function() {
                const email = $(this).val();
                if (email && !isValidEmail(email)) {
                    isValid = false;
                    $(this).addClass('error');
                    showFieldError($(this), 'Please enter a valid email address');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showNotification('Please correct the errors below', 'error');
            }
        });
        
        // Real-time validation
        $('input, textarea').on('blur', function() {
            validateField($(this));
        });
    }

    // Field validation helper
    function validateField(field) {
        const value = field.val().trim();
        
        if (field.prop('required') && !value) {
            field.addClass('error');
            showFieldError(field, 'This field is required');
            return false;
        }
        
        if (field.attr('type') === 'email' && value && !isValidEmail(value)) {
            field.addClass('error');
            showFieldError(field, 'Please enter a valid email address');
            return false;
        }
        
        field.removeClass('error');
        hideFieldError(field);
        return true;
    }

    // Email validation
    function isValidEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }

    // Show field error
    function showFieldError(field, message) {
        hideFieldError(field);
        field.after('<div class="esp-field-error" style="color: #dc3232; font-size: 12px; margin-top: 5px;">' + message + '</div>');
    }

    // Hide field error
    function hideFieldError(field) {
        field.next('.esp-field-error').remove();
    }

    // Tooltips
    function initTooltips() {
        $('[data-tooltip]').hover(
            function() {
                const tooltip = $('<div class="esp-tooltip">' + $(this).data('tooltip') + '</div>');
                $('body').append(tooltip);
                
                const pos = $(this).offset();
                tooltip.css({
                    top: pos.top - tooltip.outerHeight() - 10,
                    left: pos.left + ($(this).outerWidth() / 2) - (tooltip.outerWidth() / 2)
                });
            },
            function() {
                $('.esp-tooltip').remove();
            }
        );
    }

    // Confirm dialogs
    function initConfirmDialogs() {
        $('.esp-delete-button, .delete').on('click', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
        
        $('.esp-bulk-delete').on('click', function(e) {
            const checkedItems = $('input[name="bulk-delete[]"]:checked').length;
            if (checkedItems === 0) {
                e.preventDefault();
                alert('Please select items to delete.');
                return;
            }
            
            if (!confirm('Are you sure you want to delete ' + checkedItems + ' item(s)? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    }

    // Auto-save functionality
    function initAutoSave() {
        let autoSaveTimer;
        
        $('input, textarea').on('input', function() {
            clearTimeout(autoSaveTimer);
            
            // Show unsaved changes indicator
            $('.esp-unsaved-indicator').remove();
            $('.submit').prepend('<span class="esp-unsaved-indicator" style="color: #d63638; margin-right: 10px;">Unsaved changes</span>');
            
            // Auto-save after 5 seconds of inactivity
            autoSaveTimer = setTimeout(function() {
                // Implement auto-save logic here if needed
            }, 5000);
        });
        
        $('form').on('submit', function() {
            $('.esp-unsaved-indicator').remove();
        });
    }

    // Search and filter functionality - REMOVED
    // MP search functionality has been removed as requested

    // Notification system
    function showNotification(message, type = 'success') {
        const notification = $('<div class="esp-notification esp-notification-' + type + '">' + message + '</div>');
        
        $('body').append(notification);
        
        notification.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '4px',
            zIndex: 9999,
            maxWidth: '300px'
        });
        
        if (type === 'success') {
            notification.css({
                background: '#d4edda',
                border: '1px solid #c3e6cb',
                color: '#155724'
            });
        } else if (type === 'error') {
            notification.css({
                background: '#f8d7da',
                border: '1px solid #f5c6cb',
                color: '#721c24'
            });
        }
        
        setTimeout(function() {
            notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Bulk actions
    function initBulkActions() {
        $('#bulk-action-selector-top, #bulk-action-selector-bottom').on('change', function() {
            const action = $(this).val();
            const otherSelector = $(this).attr('id') === 'bulk-action-selector-top' ? 
                '#bulk-action-selector-bottom' : '#bulk-action-selector-top';
            
            $(otherSelector).val(action);
        });
        
        $('.check-all').on('change', function() {
            const isChecked = $(this).prop('checked');
            $('input[name="bulk-delete[]"]').prop('checked', isChecked);
        });
    }

    // Data export functionality
    function initDataExport() {
        $('.esp-export-button').on('click', function() {
            const dataType = $(this).data('type');
            const format = $(this).data('format') || 'csv';
            
            // Show loading
            $(this).prop('disabled', true).text('Exporting...');
            
            // Simulate export (replace with actual AJAX call)
            setTimeout(() => {
                $(this).prop('disabled', false).text('Export ' + format.toUpperCase());
                showNotification('Export completed successfully');
            }, 2000);
        });
    }

    // Slideshow management functionality
    function initSlideshowManagement() {
        // Modal functionality
        $('.esp-modal-close, .esp-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).closest('.esp-modal').hide();
            }
        });

        // Edit group functionality
        $('.edit-group').on('click', function(e) {
            e.preventDefault();
            const groupId = $(this).data('id');
            loadGroupData(groupId);
        });

        // Save group changes
        $('#save-group-changes').on('click', function() {
            saveGroupChanges();
        });

        // Cancel edit
        $('#cancel-edit').on('click', function() {
            $('#edit-group-modal').hide();
        });

        // Manage slides functionality
        $('.manage-slides').on('click', function(e) {
            e.preventDefault();
            const groupId = $(this).data('id');
            loadSlidesData(groupId);
        });

        // Add slide functionality
        $('#add-slide-btn').on('click', function() {
            addSlide();
        });

        // Cancel slides management
        $('#cancel-slides').on('click', function() {
            $('#manage-slides-modal').hide();
        });

        // Delete group functionality
        $('.delete-group').on('click', function(e) {
            e.preventDefault();
            const groupId = $(this).data('id');
            $('#delete-group-id').val(groupId);
            $('#delete-confirm-modal').show();
        });

        // Cancel delete
        $('#cancel-delete').on('click', function() {
            $('#delete-confirm-modal').hide();
        });

        // Media selection for slides
        $('#select-image').on('click', function(e) {
            e.preventDefault();
            selectSlideImage();
        });
    }

    // Load group data for editing
    function loadGroupData(groupId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'esp_slideshow_action',
                slideshow_action: 'get_group',
                group_id: groupId,
                nonce: $('#edit-group-modal').data('nonce') || espAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    const group = response.data;
                    $('#edit-group-id').val(group.id);
                    $('#edit-group-name').val(group.name);
                    $('#edit-group-description').val(group.description);
                    $('#edit-group-tags').val(group.tags);
                    $('#edit-group-modal').show();
                } else {
                    showNotification('Error loading group data: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Error loading group data', 'error');
            }
        });
    }

    // Save group changes
    function saveGroupChanges() {
        const groupId = $('#edit-group-id').val();
        const name = $('#edit-group-name').val();
        const description = $('#edit-group-description').val();
        const tags = $('#edit-group-tags').val();

        if (!name.trim()) {
            showNotification('Group name is required', 'error');
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'esp_slideshow_action',
                slideshow_action: 'update_group',
                group_id: groupId,
                name: name,
                description: description,
                tags: tags,
                nonce: espAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Group updated successfully', 'success');
                    $('#edit-group-modal').hide();
                    location.reload(); // Refresh to show changes
                } else {
                    showNotification('Error updating group: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Error updating group', 'error');
            }
        });
    }

    // Load slides data for management
    function loadSlidesData(groupId) {
        $('#slide-group-id').val(groupId);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'esp_slideshow_action',
                slideshow_action: 'get_slides',
                group_id: groupId,
                nonce: espAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    displaySlides(response.data);
                    $('#manage-slides-modal').show();
                } else {
                    showNotification('Error loading slides: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Error loading slides', 'error');
            }
        });
    }

    // Display slides in management interface
    function displaySlides(slides) {
        const container = $('#slides-container');
        container.empty();

        if (slides.length === 0) {
            container.html('<p>No slides found. Add your first slide below.</p>');
            return;
        }

        slides.forEach(function(slide) {
            const slideHtml = `
                <div class="slide-item" data-slide-id="${slide.id}">
                    <div class="slide-preview">
                        <img src="${slide.image_url}" alt="${slide.title}" style="width: 80px; height: 60px; object-fit: cover;">
                        <div class="slide-info">
                            <strong>${slide.title || 'Untitled'}</strong>
                            <p>${slide.description || 'No description'}</p>
                            <small>Order: ${slide.slide_order}</small>
                        </div>
                        <div class="slide-actions">
                            <button type="button" class="button button-small delete-slide" data-slide-id="${slide.id}">Delete</button>
                        </div>
                    </div>
                </div>
            `;
            container.append(slideHtml);
        });

        // Bind delete slide events
        $('.delete-slide').on('click', function() {
            const slideId = $(this).data('slide-id');
            deleteSlide(slideId);
        });
    }

    // Add new slide
    function addSlide() {
        const groupId = $('#slide-group-id').val();
        const title = $('#slide-title').val();
        const description = $('#slide-description').val();
        const imageUrl = $('#slide-image').val();
        const linkUrl = $('#slide-link').val();
        const order = $('#slide-order').val();

        if (!imageUrl.trim()) {
            showNotification('Image is required', 'error');
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'esp_slideshow_action',
                slideshow_action: 'add_slide',
                group_id: groupId,
                title: title,
                description: description,
                image_url: imageUrl,
                link_url: linkUrl,
                order: order,
                nonce: espAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Slide added successfully', 'success');
                    // Clear form
                    $('#add-slide-form')[0].reset();
                    // Reload slides
                    loadSlidesData(groupId);
                } else {
                    showNotification('Error adding slide: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Error adding slide', 'error');
            }
        });
    }

    // Delete slide
    function deleteSlide(slideId) {
        if (!confirm('Are you sure you want to delete this slide?')) {
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'esp_slideshow_action',
                slideshow_action: 'delete_slide',
                slide_id: slideId,
                nonce: espAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Slide deleted successfully', 'success');
                    // Reload slides
                    const groupId = $('#slide-group-id').val();
                    loadSlidesData(groupId);
                } else {
                    showNotification('Error deleting slide: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Error deleting slide', 'error');
            }
        });
    }

    // Select image for slide
    function selectSlideImage() {
        if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
            alert('Media uploader not available. Please refresh the page.');
            return;
        }

        const mediaUploader = wp.media({
            title: 'Select Slide Image',
            button: {
                text: 'Use this image'
            },
            multiple: false
        });

        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            $('#slide-image').val(attachment.url);
        });

        mediaUploader.open();
    }

    // Initialize additional features
    initBulkActions();
    initDataExport();

    // Map file upload functionality
    function initMapFileUpload() {
        const fileInput = $('#esp_map_json_file');
        const uploadSection = $('.esp-map-upload-section');

        if (fileInput.length) {
            // File input change handler
            fileInput.on('change', function() {
                const file = this.files[0];
                if (file) {
                    // Validate file type
                    if (!file.name.toLowerCase().endsWith('.json')) {
                        showNotification('Only JSON files are allowed.', 'error');
                        this.value = '';
                        return;
                    }

                    // Validate file size (max 50MB)
                    if (file.size > 50 * 1024 * 1024) {
                        showNotification('File size must be less than 50MB.', 'error');
                        this.value = '';
                        return;
                    }

                    // Show file info
                    const fileInfo = `
                        <div class="esp-file-preview" style="margin-top: 10px; padding: 10px; background: #e8f5e8; border-radius: 4px;">
                            <strong>Selected file:</strong> ${file.name}<br>
                            <strong>Size:</strong> ${formatFileSize(file.size)}<br>
                            <strong>Type:</strong> ${file.type || 'application/json'}
                        </div>
                    `;

                    // Remove existing preview
                    $('.esp-file-preview').remove();

                    // Add new preview
                    uploadSection.append(fileInfo);

                    showNotification('File selected successfully. Save the post to upload.', 'success');
                }
            });

            // Copy shortcode functionality
            $('.copy-shortcode').on('click', function() {
                const shortcode = $(this).data('shortcode');
                const button = $(this);

                navigator.clipboard.writeText(shortcode).then(function() {
                    const originalText = button.text();
                    button.text('Copied!');
                    setTimeout(function() {
                        button.text(originalText);
                    }, 2000);
                    showNotification('Shortcode copied to clipboard!', 'success');
                }).catch(function() {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = shortcode;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);

                    const originalText = button.text();
                    button.text('Copied!');
                    setTimeout(function() {
                        button.text(originalText);
                    }, 2000);
                    showNotification('Shortcode copied to clipboard!', 'success');
                });
            });
        }
    }

    // Helper function to format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Global utility functions
    window.espAdmin = {
        showNotification: showNotification,
        validateField: validateField,
        isValidEmail: isValidEmail,
        nonce: typeof provincial_admin_ajax !== 'undefined' ? provincial_admin_ajax.slideshow_nonce : ''
    };

})(jQuery);
