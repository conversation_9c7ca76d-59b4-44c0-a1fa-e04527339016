# Dakoii Prov - Hero Slideshow Plugin

A powerful WordPress slideshow plugin that allows you to create multiple slideshow groups with keyword tags for targeted display on different pages.

## Features

- **Multiple Slideshow Groups**: Create and manage multiple slideshow collections
- **Tag-Based Display**: Use tags to automatically display appropriate slideshows on different pages
- **Easy Admin Interface**: WordPress-native admin interface for managing slideshows and slides
- **Responsive Design**: Mobile-friendly slideshows that adapt to all screen sizes
- **Flexible Shortcodes**: Display slideshows by ID or tag using simple shortcodes
- **Rich Media Support**: Full WordPress media library integration
- **Autoplay Controls**: Configurable autoplay with pause/resume functionality
- **Navigation Options**: Previous/next buttons and dot indicators
- **Touch Support**: Swipe gestures for mobile devices
- **Keyboard Navigation**: Arrow key support for accessibility

## Installation

1. Upload the `dakoii-prov-slideshow` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to 'Slideshows' in your WordPress admin to create your first slideshow group

## Usage

### Creating Slideshow Groups

1. Navigate to **Slideshows > Add New Group** in your WordPress admin
2. Enter a name and description for your slideshow group
3. Add comma-separated tags (e.g., "home, featured, products")
4. Click "Create Group"

### Adding Slides

1. Go to **Slideshows > All Groups**
2. Click "Manage Slides" for the group you want to edit
3. Add slide title, description, image, and optional link URL
4. Set the slide order (lower numbers appear first)
5. Click "Add Slide"

### Displaying Slideshows

#### Using Shortcodes

Display by slideshow ID:
```
[dakoii_slideshow id="1"]
```

Display by tag:
```
[dakoii_slideshow tag="home"]
```

With custom options:
```
[dakoii_slideshow id="1" autoplay="true" duration="3000" show_nav="true" show_dots="true"]
```

#### Using Template Functions

In your theme files:
```php
<?php echo dakoii_get_slideshow(1); // By ID ?>
<?php echo dakoii_get_slideshow(0, 'home'); // By tag ?>
```

## Shortcode Parameters

- `id` - Slideshow group ID (required if not using tag)
- `tag` - Tag name to match (required if not using ID)
- `autoplay` - Enable/disable autoplay (default: true)
- `duration` - Autoplay duration in milliseconds (default: 5000)
- `show_nav` - Show previous/next buttons (default: true)
- `show_dots` - Show dot indicators (default: true)

## Database Tables

The plugin creates two database tables:

- `wp_dakoii_slideshow_groups` - Stores slideshow group information
- `wp_dakoii_slides` - Stores individual slide data

## Customization

### CSS Classes

The plugin uses semantic CSS classes for easy customization:

- `.dakoii-slideshow` - Main slideshow container
- `.slideshow-container` - Slide container
- `.slide` - Individual slide
- `.slide.active` - Currently active slide
- `.slide-content` - Text overlay content
- `.slideshow-nav` - Navigation buttons
- `.slideshow-dots` - Dot indicators

### Custom Styling

Add custom CSS to your theme or child theme:

```css
.dakoii-slideshow {
    border-radius: 0; /* Remove border radius */
    box-shadow: none; /* Remove shadow */
}

.slide-content h3 {
    color: #your-color;
    font-size: 2.5rem;
}
```

## Developer API

### JavaScript Functions

Control slideshows programmatically:

```javascript
// Navigate slides
dakoiiSlideshowNext('slideshow-id');
dakoiiSlideshowPrev('slideshow-id');
dakoiiSlideshowGoTo('slideshow-id', slideIndex);

// Control autoplay
dakoiiSlideshowPause('slideshow-id');
dakoiiSlideshowPlay('slideshow-id');
```

### PHP Functions

```php
// Create slideshow group
DakoiiSlideshow::create_group($name, $description, $tags);

// Add slide
DakoiiSlideshow::add_slide($group_id, $title, $description, $image_url, $link_url, $order);

// Get slideshow groups by tag
DakoiiSlideshow::get_groups_by_tag($tag);
```

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

## Support

For support and feature requests, please visit our support forum or contact the plugin author.

## License

This plugin is licensed under GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Multiple slideshow groups with tag support
- Responsive design
- Touch and keyboard navigation
- WordPress media library integration
- Comprehensive admin interface