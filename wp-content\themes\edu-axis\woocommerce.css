/*
Theme Name: edu-axis

WooCommerce styles override
*/
/**
 * WooCommerce color variables
 */
/**
 * Imports
 */
/**
 * Shop tables
 */
table.shop_table_responsive thead {
  display: none;
}
table.shop_table_responsive tbody th {
  display: none;
}
table.shop_table_responsive tr td {
  display: block;
  text-align: right;
  clear: both;
}
table.shop_table_responsive tr td:before {
  content: attr(data-title) ": ";
  float: left;
}
table.shop_table_responsive tr td.product-remove a {
  text-align: center;
  background: #f00;
  color: #fff;
  border-radius: 50%;
  font-size: 24px;
  line-height: 24px;
  width: 24px;
  height: 24px;
  display: block;
}
table.shop_table_responsive tr td.product-remove:before {
  display: none;
}
table.shop_table_responsive tr td.actions:before, table.shop_table_responsive tr td.download-actions:before {
  display: none;
}
table.shop_table_responsive tr td.download-actions .button {
  display: block;
  text-align: center;
}

@media screen and (min-width: 48em) {
  table.shop_table_responsive thead {
    display: table-header-group;
  }

  table.shop_table_responsive tbody th {
    display: table-cell;
  }

  table.shop_table_responsive tr th, table.shop_table_responsive tr td {
    text-align: left;
  }

  table.shop_table_responsive tr td {
    display: table-cell;
  }

  table.shop_table_responsive tr td:before {
    display: none;
  }
}
/**
 * Products
 */
ul.products {
  margin: 0;
  padding: 0;
}
ul.products li.product {
  list-style: none;
  position: relative;
  margin-bottom: 2em;
}
ul.products li.product .woocommerce-loop-product__title {
  font-size: 1rem;
}
ul.products li.product img {
  display: block;
  width: 100%;
}
ul.products li.product .button {
  display: block;
}

section.products.related {
  margin-top: 30px;
}

@media screen and (min-width: 48em) {
  ul.products li.product {
    width: 30.7966666667%;
    float: left;
    margin-right: 3.8%;
  }

  ul.products li.product.first {
    clear: both;
  }

  ul.products li.product.last {
    margin-right: 0;
  }

  .columns-1 ul.products li.product {
    float: none;
    width: 100%;
  }

  .columns-2 ul.products li.product {
    width: 48.1%;
  }

  .columns-3 ul.products li.product {
    width: 30.7966666667%;
  }

  .columns-4 ul.products li.product {
    width: 22.15%;
  }

  .columns-5 ul.products li.product {
    width: 16.96%;
  }

  .columns-6 ul.products li.product {
    width: 13.4933333333%;
  }
}
/**
 * Single Product
 */
.single-product div.product {
  content: "";
  display: table;
  table-layout: fixed;
  position: relative;
  margin-top: 0;
}
.single-product div.product figure {
  margin-top: 0;
}
.single-product div.product .woocommerce-product-gallery {
  position: relative;
}
.single-product div.product .woocommerce-product-gallery .woocommerce-product-gallery__trigger {
  position: absolute;
  top: 1px;
  right: 1px;
  display: block;
  z-index: 99;
  padding: 10px 15px;
  background: #fff;
}
.single-product div.product .woocommerce-product-gallery .flex-viewport {
  margin-bottom: 1em;
}
.single-product div.product .woocommerce-product-gallery .flex-control-thumbs {
  content: "";
  display: table;
  table-layout: fixed;
  margin: 0;
  padding: 0;
}
.single-product div.product .woocommerce-product-gallery .flex-control-thumbs li {
  list-style: none;
  cursor: pointer;
  float: left;
}
.single-product div.product .woocommerce-product-gallery .flex-control-thumbs li:hover img {
  opacity: 1;
}
.single-product div.product .woocommerce-product-gallery .flex-control-thumbs li img {
  opacity: .5;
}
.single-product div.product .woocommerce-product-gallery .flex-control-thumbs li img.flex-active {
  opacity: 1;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-2 .flex-control-thumbs li {
  width: 48.1%;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-2 .flex-control-thumbs li:nth-child(2n) {
  margin-right: 0;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-2 .flex-control-thumbs li:nth-child(2n+1) {
  clear: both;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-3 .flex-control-thumbs li {
  width: 30.7966666667%;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-3 .flex-control-thumbs li:nth-child(3n) {
  margin-right: 0;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-3 .flex-control-thumbs li:nth-child(3n+1) {
  clear: both;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-4 .flex-control-thumbs li {
  width: 22.15%;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-4 .flex-control-thumbs li:nth-child(4n) {
  margin-right: 0;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-4 .flex-control-thumbs li:nth-child(4n+1) {
  clear: both;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-5 .flex-control-thumbs li {
  width: 16.96%;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-5 .flex-control-thumbs li:nth-child(5n) {
  margin-right: 0;
}
.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-5 .flex-control-thumbs li:nth-child(5n+1) {
  clear: both;
}

.stock:empty:before {
  display: none;
}

.stock.in-stock {
  color: #0f834d;
}

.stock.out-of-stock {
  color: #e2401c;
}

.woocommerce div.product form.cart div.quantity {
  width: 100px;
  display: inline-block;
  margin-right: 10px;
  vertical-align: top;
}
.woocommerce div.product form.cart div.quantity .qty {
  margin-top: 0;
}
.woocommerce div.product .product_meta {
  margin-top: 15px;
}
.woocommerce div.product.product-type-grouped form.cart {
  text-align: right;
}
.woocommerce div.product.product-type-grouped form.cart td {
  padding: 5px;
}
.woocommerce div.product.product-type-grouped form.cart td div.quantity {
  width: 60px;
}

.woocommerce #content div.product div.images,
.woocommerce div.product div.images,
.woocommerce-page #content div.product div.images,
.woocommerce-page div.product div.images {
  float: left;
  width: 48%;
}

.woocommerce #content div.product div.summary,
.woocommerce div.product div.summary,
.woocommerce-page #content div.product div.summary,
.woocommerce-page div.product div.summary {
  float: right;
  width: 48%;
}
.woocommerce #content div.product div.summary:after,
.woocommerce div.product div.summary:after,
.woocommerce-page #content div.product div.summary:after,
.woocommerce-page div.product div.summary:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
}

.woocommerce-tabs.wc-tabs-wrapper:before {
  content: "";
  display: block;
  height: 0;
  clear: both;
}

.pswp--open {
  z-index: 3001;
}

.woocommerce-product-attributes-item__value p {
  margin-bottom: 0;
}

.woocommerce #content table.cart td.actions, .woocommerce table.cart td.actions, .woocommerce-page #content table.cart td.actions, .woocommerce-page table.cart td.actions {
  text-align: right;
}

#add_payment_method table.cart img, .woocommerce-cart table.cart img, .woocommerce-checkout table.cart img {
  width: 60px;
}

.woocommerce #content table.cart td.actions .coupon label, .woocommerce table.cart td.actions .coupon label, .woocommerce-page #content table.cart td.actions .coupon label, .woocommerce-page table.cart td.actions .coupon label {
  display: none;
}

.woocommerce #content table.cart td.actions .coupon, .woocommerce table.cart td.actions .coupon, .woocommerce-page #content table.cart td.actions .coupon, .woocommerce-page table.cart td.actions .coupon {
  float: left;
}

.woocommerce #content table.cart td.actions .input-text, .woocommerce table.cart td.actions .input-text, .woocommerce-page #content table.cart td.actions .input-text, .woocommerce-page table.cart td.actions .input-text {
  width: auto;
  margin-top: 0;
  vertical-align: top;
}

.woocommerce #respond input#submit.disabled, .woocommerce #respond input#submit:disabled, .woocommerce #respond input#submit:disabled[disabled], .woocommerce a.button.disabled, .woocommerce a.button:disabled, .woocommerce a.button:disabled[disabled], .woocommerce button.button.disabled, .woocommerce button.button:disabled, .woocommerce button.button:disabled[disabled], .woocommerce input.button.disabled, .woocommerce input.button:disabled, .woocommerce input.button:disabled[disabled] {
  cursor: not-allowed;
  opacity: 0.6;
  background: #ddd;
  border: #ddd;
  color: #222;
}

.shop_table td, .shop_table th {
  padding: 5px 10px;
}
.shop_table .quantity input.qty {
  margin-top: 0;
}

.cart-collaterals .cart_totals {
  float: right;
  width: 50%;
}
.cart-collaterals .cart_totals h2 {
  text-align: right;
}
.cart-collaterals .cart_totals .shop_table td {
  text-align: right;
}

.wc-proceed-to-checkout .checkout-button {
  background: #1abc9c;
  color: #fff;
  float: right;
  display: inline-block;
  padding: 15px 25px;
  font-size: 1.1rem;
}

/**
 * Checkout
 */
.woocommerce .col2-set,
.woocommerce-page .col2-set {
  width: 100%;
  margin: 30px 0;
  padding: 30px;
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
}

.woocommerce .col2-set::after,
.woocommerce .col2-set::before,
.woocommerce-page .col2-set::after,
.woocommerce-page .col2-set::before {
  content: ' ';
  display: table;
}

.woocommerce .col2-set::after,
.woocommerce-page .col2-set::after {
  clear: both;
}

.woocommerce .col2-set .col-1,
.woocommerce-page .col2-set .col-1 {
  float: left;
  width: 48%;
  max-width: 48%;
  flex: none;
}
.woocommerce .col2-set .col-1 .form-row,
.woocommerce-page .col2-set .col-1 .form-row {
  display: block;
}

.woocommerce .col2-set .col-2,
.woocommerce-page .col2-set .col-2 {
  float: right;
  width: 48%;
  max-width: 48%;
  flex: none;
}
.woocommerce .col2-set .col-2 .form-row,
.woocommerce-page .col2-set .col-2 .form-row {
  display: block;
}

.woocommerce-checkout th.product-total,
.woocommerce-checkout td:not(.product-name) {
  text-align: right;
}
.woocommerce-checkout td.product-name {
  padding-left: 30px;
}

ul.wc_payment_methods {
  padding-left: 0;
}

form.woocommerce-form-coupon {
  margin-top: 30px;
  padding: 20px 30px;
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
}
form.woocommerce-form-coupon p.form-row {
  display: block;
  float: left;
  width: 48%;
  margin: 0;
}
form.woocommerce-form-coupon p.form-row input {
  vertical-align: top;
  margin-top: 0;
  margin-right: 10px;
  width: calc( 100% - 10px );
}
form.woocommerce-form-coupon:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
}

@media screen and (min-width: 768px) {
  .col2-set .form-row-first {
    float: left;
    margin-right: 3.8%;
  }

  .col2-set .form-row-last {
    float: right;
    margin-right: 0;
  }

  .col2-set .form-row-first,
  .col2-set .form-row-last {
    width: 48.1%;
  }
}
/**
 * General WooCommerce components
 */
/**
 * Header cart
 */
.site-header-cart {
  position: relative;
  margin: 0;
  padding: 0;
  content: "";
  display: table;
  table-layout: fixed;
}

.site-header-cart .cart-contents {
  text-decoration: none;
}

.site-header-cart .widget_shopping_cart {
  display: none;
}

.site-header-cart .product_list_widget {
  margin: 0;
  padding: 0;
}

/**
 * Star rating
 */
.star-rating {
  overflow: hidden;
  position: relative;
  height: 1.618em;
  line-height: 1.618;
  font-size: 1em;
  width: 5.3em;
  font-family: 'star';
  font-weight: 400;
}

.star-rating:before {
  content: "\53\53\53\53\53";
  opacity: .25;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
}

.star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
}

.star-rating span:before {
  content: "\53\53\53\53\53";
  top: 0;
  position: absolute;
  left: 0;
  color: royalblue;
}

p.stars a {
  position: relative;
  height: 1em;
  width: 1em;
  text-indent: -999em;
  display: inline-block;
  text-decoration: none;
  margin-right: 1px;
  font-weight: 400;
}

p.stars a:before {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 1em;
  height: 1em;
  line-height: 1;
  font-family: "star";
  content: "\53";
  color: #404040;
  text-indent: 0;
  opacity: .25;
}

p.stars a:hover ~ a:before {
  content: "\53";
  color: #404040;
  opacity: .25;
}

p.stars:hover a:before {
  content: "\53";
  color: royalblue;
  opacity: 1;
}

p.stars.selected a.active:before {
  content: "\53";
  color: royalblue;
  opacity: 1;
}

p.stars.selected a.active ~ a:before {
  content: "\53";
  color: #404040;
  opacity: .25;
}

p.stars.selected a:not(.active):before {
  content: "\53";
  color: royalblue;
  opacity: 1;
}

/**
 * Tabs
 */
.woocommerce-tabs ul.tabs {
  list-style: none;
  margin: 0;
  margin-top: 15px;
  padding: 0 1em;
  text-align: left;
  position: relative;
}
.woocommerce-tabs ul.tabs:before {
  position: absolute;
  content: " ";
  width: 100%;
  bottom: 0;
  left: 0;
  border-bottom: 1px solid #d3ced2;
  z-index: 1;
}

.woocommerce-tabs ul.tabs li {
  position: relative;
  border: 1px solid #d3ced2;
  background-color: #ebe9eb;
  display: inline-block;
  position: relative;
  z-index: 0;
  margin: 0;
  padding: 0 1em;
}
.woocommerce-tabs ul.tabs li.active {
  background: #fff;
  border-bottom-color: #fff;
  z-index: 11;
}

.woocommerce-tabs ul.tabs li a {
  padding: 1em 0;
  display: block;
}

.woocommerce-tabs .panel h2:first-of-type {
  margin-bottom: 1em;
}

/**
 * Password strength meter
 */
.woocommerce-password-strength {
  text-align: right;
}

.woocommerce-password-strength.strong {
  color: #0f834d;
}

.woocommerce-password-strength.short {
  color: #e2401c;
}

.woocommerce-password-strength.bad {
  color: #e2401c;
}

.woocommerce-password-strength.good {
  color: #3D9CD2;
}

/**
 * Forms
 */
.form-row.woocommerce-validated input.input-text {
  box-shadow: inset 2px 0 0 #0f834d;
}

.form-row.woocommerce-invalid input.input-text {
  box-shadow: inset 2px 0 0 #de582f;
}

.required {
  color: #de582f;
}

/**
 * Notices
 */
.woocommerce-message,
.woocommerce-info,
.woocommerce-error,
.woocommerce-noreviews,
p.no-comments {
  background-color: #c5ffe5;
  border-top: 3px solid #1abc9c;
  border-bottom: 1px solid #81eed8;
  width: 100%;
  padding: 10px 15px;
  margin-bottom: 5px;
}
.woocommerce-message:before,
.woocommerce-info:before,
.woocommerce-error:before,
.woocommerce-noreviews:before,
p.no-comments:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  display: inline-block;
  text-align: center;
  color: #1abc9c;
  margin-left: 7px;
  margin-right: 7px;
  position: relative;
  font-size: 15px;
  content: "\f05a";
}
.woocommerce-message .showcoupon,
.woocommerce-info .showcoupon,
.woocommerce-error .showcoupon,
.woocommerce-noreviews .showcoupon,
p.no-comments .showcoupon {
  margin-left: 10px;
}
.woocommerce-message a.button.wc-forward,
.woocommerce-info a.button.wc-forward,
.woocommerce-error a.button.wc-forward,
.woocommerce-noreviews a.button.wc-forward,
p.no-comments a.button.wc-forward {
  margin-right: 10px;
}

.woocommerce-info,
.woocommerce-noreviews,
p.no-comments {
  background-color: #f5f5f5;
}

.woocommerce-error li {
  color: #de582f;
  list-style: none;
}

.demo_store {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  margin: 0;
  padding: 1em;
  background-color: #3D9CD2;
  z-index: 9999;
}

@media screen and (min-width: 48em) {
  /**
   * Header cart
   */
  .site-header-cart .widget_shopping_cart {
    position: absolute;
    top: 100%;
    width: 100%;
    z-index: 999999;
    left: -999em;
    display: block;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.2);
  }

  .site-header-cart:hover .widget_shopping_cart, .site-header-cart.focus .widget_shopping_cart {
    left: 0;
    display: block;
  }
}
/**
 * WooCommerce widgets
 */
/**
 * WooCommerce Price Filter
 */
.widget_price_filter .price_slider {
  margin-bottom: 1.5em;
}

.widget_price_filter .price_slider_amount {
  text-align: right;
  line-height: 2.4em;
}

.widget_price_filter .price_slider_amount .button {
  float: left;
}

.widget_price_filter .ui-slider {
  position: relative;
  text-align: left;
}

.widget_price_filter .ui-slider .ui-slider-handle {
  position: absolute;
  z-index: 2;
  width: 1em;
  height: 1em;
  cursor: ew-resize;
  outline: none;
  background: royalblue;
  box-sizing: border-box;
  margin-top: -.25em;
  opacity: 1;
}

.widget_price_filter .ui-slider .ui-slider-handle:last-child {
  margin-left: -1em;
}

.widget_price_filter .ui-slider .ui-slider-handle:hover, .widget_price_filter .ui-slider .ui-slider-handle.ui-state-active {
  box-shadow: 0 0 0 0.25em rgba(0, 0, 0, 0.1);
}

.widget_price_filter .ui-slider .ui-slider-range {
  position: absolute;
  z-index: 1;
  display: block;
  border: 0;
  background: royalblue;
}

.widget_price_filter .price_slider_wrapper .ui-widget-content {
  background: rgba(0, 0, 0, 0.1);
}

.widget_price_filter .ui-slider-horizontal {
  height: .5em;
}

.widget_price_filter .ui-slider-horizontal .ui-slider-range {
  height: 100%;
}

.top-card-info {
  list-style-type: none;
  padding-left: 0;
  margin-top: 15px;
  margin-bottom: 0;
  float: right;
}
.top-card-info li {
  display: inline-block;
  border-right: 0.0625rem solid rgba(49, 49, 49, 0.1);
  padding: 9px 0px;
}
.top-card-info li:last-child {
  border-right: none;
}
.top-card-info li a {
  line-height: 1;
  padding: 0 1rem;
  display: block;
  color: #fff;
  position: relative;
  font-size: 15px;
}
.top-card-info li a:hover, .top-card-info li a:focus {
  color: #2d3e50;
}
.top-card-info li a.cart {
  padding-right: 20px;
}
.top-card-info li a .cart-num {
  position: absolute;
  top: -1px;
  left: 35px;
  font-size: 13px;
  padding: 3px 5px;
  -moz-border-radius: 0.3125rem;
  -webkit-border-radius: 0.3125rem;
  -ms-border-radius: 0.3125rem;
  border-radius: 0.3125rem;
  color: #fff;
  background-color: #009688;
  line-height: 1;
}

.woocommerce-result-count,
.woocommerce-ordering {
  width: 50%;
  display: inline-block;
  background: #fafafa;
  box-sizing: border-box;
}

.woocommerce-result-count {
  float: left;
  padding: 21px 15px 20px;
}

.woocommerce-ordering {
  float: right;
  text-align: right;
  padding: 13px 15px;
}
.woocommerce-ordering select {
  padding: 8px 10px 8px 15px;
}

.columns-3::before {
  content: "";
  display: block;
  height: 0;
  clear: both;
}

ul.products {
  text-align: center;
  margin: auto;
}
ul.products:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
}
ul.products li.product {
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
}
ul.products li.product .woocommerce-loop-product__title {
  padding: 20px 5px 10px;
  font-size: 18px;
  margin-bottom: 5px;
  color: #656565;
}
ul.products li.product .woocommerce-loop-product__link {
  display: block;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}
ul.products li.product .button {
  display: inline-block;
  margin: 10px 0 20px;
  padding: 5px 15px;
}
ul.products li.product .button.add_to_cart_button {
  background: #148f77;
  color: #fff;
}
ul.products li.product .button.add_to_cart_button:before {
  content: "\f217";
  display: inline;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  margin-right: 10px;
}
ul.products li.product .button.add_to_cart_button.product_type_variable {
  background: #1abc9c;
}
ul.products li.product .button.add_to_cart_button.added {
  display: none;
}
ul.products li.product .button.product_type_grouped {
  background: #28e1bd;
  color: #fff;
}
ul.products li.product .button.product_type_external {
  -webkit-box-shadow: -3px 0px 15px #eee;
  -moz-box-shadow: -3px 0px 15px #eee;
  box-shadow: -3px 0px 15px #eee;
}
ul.products li.product a.added_to_cart.wc-forward {
  background: #6b7a86;
  color: #fff;
  display: inline-block;
  margin: 10px 0 20px;
  padding: 5px 15px;
}
ul.products li.product a.added_to_cart.wc-forward:after {
  content: "\f058";
  display: inline;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  margin-left: 10px;
}
ul.products li.product span.price ins {
  background: transparent;
}
ul.products li.product span.price .woocommerce-Price-amount {
  color: #656565;
}
ul.products li.product span.price del {
  color: #989898;
}
ul.products li.product span.price del .woocommerce-Price-amount {
  color: #989898;
}

.sale {
  overflow: hidden;
}
.sale .onsale {
  background-color: #e4144d;
  border-radius: 0;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  height: 60px;
  line-height: 8px;
  margin: 0;
  min-height: 20px;
  padding: 40px 0 0;
  position: absolute;
  right: -40px;
  text-align: center;
  text-shadow: none;
  top: -12px;
  transform: rotate(51deg);
  width: 95px;
  z-index: 99;
}

main div.product > span.onsale {
  right: initial;
  left: -40px;
  transform: rotate(-51deg);
}

nav.woocommerce-pagination:before {
  content: "";
  display: block;
  height: 0;
  clear: both;
}

.form-row.place-order {
  display: block;
  margin: 0;
}

.single_add_to_cart_button {
  margin-top: 0;
}
