<?php
/**
 * The main template file
 *
 * @package Nols_ESPA_Theme_Two
 */

get_header(); ?>

<div class="site-wrap">
  <main id="primary" class="site-main">
    <div class="container">
      <?php if (have_posts()) : ?>

        <?php if (is_home() && !is_front_page()) : ?>
          <header class="page-header">
            <h1 class="page-title"><?php single_post_title(); ?></h1>
          </header>
        <?php endif; ?>

        <?php if (is_archive()) : ?>
          <header class="page-header">
            <?php
            the_archive_title('<h1 class="archive-title">', '</h1>');
            the_archive_description('<div class="archive-description">', '</div>');
            ?>
          </header>
        <?php endif; ?>

        <?php if (is_search()) : ?>
          <header class="page-header">
            <h1 class="page-title">
              <?php
              printf(
                esc_html__('Search Results for: %s', 'dakoii-provincial-government-theme'),
                '<span>' . get_search_query() . '</span>'
              );
              ?>
            </h1>
          </header>
        <?php endif; ?>

        <?php
        while (have_posts()) :
          the_post();
          ?>

          <article id="post-<?php the_ID(); ?>" <?php post_class('article-card'); ?>>
            <header>
              <?php
              if (is_singular()) :
                the_title('<h1 class="entry-title">', '</h1>');
              else :
                the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>');
              endif;
              ?>
              <div class="entry-meta">
                <?php
                printf(
                  esc_html__('Published on %1$s | By %2$s', 'dakoii-provincial-government-theme'),
                  '<time datetime="' . esc_attr(get_the_date('c')) . '">' . esc_html(get_the_date()) . '</time>',
                  '<span class="author vcard"><a class="url fn n" href="' . esc_url(get_author_posts_url(get_the_author_meta('ID'))) . '">' . esc_html(get_the_author()) . '</a></span>'
                );
                ?>
                <?php if (has_category()) : ?>
                  <span class="categories"> | <?php esc_html_e('Categories:', 'dakoii-provincial-government-theme'); ?> <?php the_category(', '); ?></span>
                <?php endif; ?>
                <?php if (has_tag()) : ?>
                  <span class="tags"> | <?php esc_html_e('Tags:', 'dakoii-provincial-government-theme'); ?> <?php the_tags('', ', '); ?></span>
                <?php endif; ?>
              </div>
            </header>

            <div class="entry-content">
              <?php if (is_singular() || is_search()) :
                the_content();
              else :
                the_excerpt(); ?>
                <a href="<?php echo esc_url(get_permalink()); ?>" class="read-more"><?php esc_html_e('Read More →', 'dakoii-provincial-government-theme'); ?></a>
              <?php endif; ?>
            </div>

            <?php if (is_singular() && (comments_open() || get_comments_number())) : ?>
              <footer class="entry-footer">
                <?php comments_template(); ?>
              </footer>
            <?php endif; ?>
          </article>

          <?php
        endwhile;

        the_posts_pagination(array(
          'mid_size' => 2,
          'prev_text' => __('← Previous', 'dakoii-provincial-government-theme'),
          'next_text' => __('Next →', 'dakoii-provincial-government-theme'),
          'class' => 'pagination',
        ));

      else : ?>
        <section class="no-results not-found">
          <header class="page-header">
            <h1 class="page-title"><?php esc_html_e('Nothing here', 'dakoii-provincial-government-theme'); ?></h1>
          </header>
          <div class="page-content">
            <?php if (is_home() && current_user_can('publish_posts')) : ?>
              <p>
                <?php
                printf(
                  wp_kses(
                    __('Ready to publish your first post? <a href="%1$s">Get started here</a>.', 'dakoii-provincial-government-theme'),
                    array('a' => array('href' => array()))
                  ),
                  esc_url(admin_url('post-new.php'))
                );
                ?>
              </p>
            <?php elseif (is_search()) : ?>
              <p><?php esc_html_e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'dakoii-provincial-government-theme'); ?></p>
              <?php get_search_form(); ?>
            <?php else : ?>
              <p><?php esc_html_e('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'dakoii-provincial-government-theme'); ?></p>
              <?php get_search_form(); ?>
            <?php endif; ?>
          </div>
        </section>
      <?php endif; ?>
    </div>
  </main>
</div>

<?php get_footer(); ?>
