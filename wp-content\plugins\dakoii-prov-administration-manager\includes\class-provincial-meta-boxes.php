<?php
/**
 * Provincial Administration Meta Boxes Class
 * 
 * Handles meta boxes for custom post types
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_Meta_Boxes {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_boxes'));
        add_action('post_edit_form_tag', array($this, 'add_form_enctype'));
    }
    
    /**
     * Add enctype to post edit form for file uploads
     */
    public function add_form_enctype() {
        global $post;
        if ($post && $post->post_type === 'esp_map') {
            echo ' enctype="multipart/form-data"';
        }
    }

    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        // Governor meta boxes
        add_meta_box(
            'provincial_governor_details',
            __('Governor Details', 'esp-admin-manager'),
            array($this, 'governor_meta_box_callback'),
            'esp_governor',
            'normal',
            'high'
        );
        
        // MP meta boxes
        add_meta_box(
            'provincial_mp_details',
            __('MP Details', 'esp-admin-manager'),
            array($this, 'mp_meta_box_callback'),
            'esp_mp',
            'normal',
            'high'
        );
        
        // District meta boxes
        add_meta_box(
            'provincial_district_details',
            __('District Details', 'esp-admin-manager'),
            array($this, 'district_meta_box_callback'),
            'esp_district',
            'normal',
            'high'
        );

        // Contact meta boxes
        add_meta_box(
            'provincial_contact_details',
            __('Contact Details', 'esp-admin-manager'),
            array($this, 'contact_meta_box_callback'),
            'esp_contact',
            'normal',
            'high'
        );

        // Event meta boxes
        add_meta_box(
            'provincial_event_details',
            __('Event Details', 'esp-admin-manager'),
            array($this, 'event_meta_box_callback'),
            'esp_event',
            'normal',
            'high'
        );
        
        // News meta boxes
        add_meta_box(
            'provincial_news_details',
            __('News Details', 'esp-admin-manager'),
            array($this, 'news_meta_box_callback'),
            'esp_news',
            'normal',
            'high'
        );

        // Map meta boxes
        add_meta_box(
            'provincial_map_details',
            __('Map File Management', 'esp-admin-manager'),
            array($this, 'map_meta_box_callback'),
            'esp_map',
            'normal',
            'high'
        );
    }
    
    /**
     * Governor meta box callback
     */
    public function governor_meta_box_callback($post) {
        wp_nonce_field('provincial_governor_meta_box', 'provincial_governor_meta_box_nonce');
        
        $title = get_post_meta($post->ID, '_esp_governor_title', true);
        $party = get_post_meta($post->ID, '_esp_governor_party', true);
        $email = get_post_meta($post->ID, '_esp_governor_email', true);
        $phone = get_post_meta($post->ID, '_esp_governor_phone', true);
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="esp_governor_title"><?php _e('Official Title', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_governor_title" name="esp_governor_title" value="<?php echo esc_attr($title); ?>" class="regular-text" />
                    <p class="description"><?php _e('e.g., Governor of the Province', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_governor_party"><?php _e('Political Party', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_governor_party" name="esp_governor_party" value="<?php echo esc_attr($party); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_governor_email"><?php _e('Email Address', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="email" id="esp_governor_email" name="esp_governor_email" value="<?php echo esc_attr($email); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_governor_phone"><?php _e('Phone Number', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_governor_phone" name="esp_governor_phone" value="<?php echo esc_attr($phone); ?>" class="regular-text" />
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * MP meta box callback
     */
    public function mp_meta_box_callback($post) {
        wp_nonce_field('provincial_mp_meta_box', 'provincial_mp_meta_box_nonce');

        $electorate = get_post_meta($post->ID, '_esp_mp_electorate', true);
        $party = get_post_meta($post->ID, '_esp_mp_party', true);
        $district_id = get_post_meta($post->ID, '_esp_mp_district_id', true);
        $message = get_post_meta($post->ID, '_esp_mp_message', true);

        // Get all districts for dropdown
        $districts = get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => -1,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="esp_mp_electorate"><?php _e('Electorate', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_mp_electorate" name="esp_mp_electorate" value="<?php echo esc_attr($electorate); ?>" class="regular-text" />
                    <p class="description"><?php _e('e.g., Wewak Open, Provincial', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_mp_party"><?php _e('Political Party', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_mp_party" name="esp_mp_party" value="<?php echo esc_attr($party); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_mp_district_id"><?php _e('District', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <select id="esp_mp_district_id" name="esp_mp_district_id" class="regular-text">
                        <option value=""><?php _e('Select District', 'esp-admin-manager'); ?></option>
                        <?php foreach ($districts as $district): ?>
                            <option value="<?php echo esc_attr($district->ID); ?>" <?php selected($district_id, $district->ID); ?>>
                                <?php echo esc_html($district->post_title); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="description"><?php _e('Select the district this MP represents', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_mp_message"><?php _e('MP Bio', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <textarea id="esp_mp_message" name="esp_mp_message" rows="4" class="large-text"><?php echo esc_textarea($message); ?></textarea>
                    <p class="description"><?php _e('Optional biographical information about the MP (e.g., background, experience, education)', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * District meta box callback
     */
    public function district_meta_box_callback($post) {
        wp_nonce_field('provincial_district_meta_box', 'provincial_district_meta_box_nonce');

        // Basic district information
        $llgs = get_post_meta($post->ID, '_esp_district_llgs', true);
        $wards = get_post_meta($post->ID, '_esp_district_wards', true);
        $population = get_post_meta($post->ID, '_esp_district_population', true);
        $area = get_post_meta($post->ID, '_esp_district_area', true);

        // District contact information
        $contact_address = get_post_meta($post->ID, '_esp_district_contact_address', true);
        $contact_phone = get_post_meta($post->ID, '_esp_district_contact_phone', true);
        $contact_fax = get_post_meta($post->ID, '_esp_district_contact_fax', true);
        $contact_emergency = get_post_meta($post->ID, '_esp_district_contact_emergency', true);
        $contact_email = get_post_meta($post->ID, '_esp_district_contact_email', true);
        $contact_admin_email = get_post_meta($post->ID, '_esp_district_contact_admin_email', true);
        $contact_website = get_post_meta($post->ID, '_esp_district_contact_website', true);
        $contact_office_hours = get_post_meta($post->ID, '_esp_district_contact_office_hours', true);
        ?>
        <div style="margin-bottom: 20px;">
            <h3><?php _e('Basic Information', 'esp-admin-manager'); ?></h3>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="esp_district_llgs"><?php _e('Number of LLGs', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="esp_district_llgs" name="esp_district_llgs" value="<?php echo esc_attr($llgs); ?>" class="small-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_wards"><?php _e('Number of Wards', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="number" id="esp_district_wards" name="esp_district_wards" value="<?php echo esc_attr($wards); ?>" class="small-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_population"><?php _e('Population', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_district_population" name="esp_district_population" value="<?php echo esc_attr($population); ?>" class="regular-text" />
                        <p class="description"><?php _e('Optional: District population if available', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_area"><?php _e('Area (km²)', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_district_area" name="esp_district_area" value="<?php echo esc_attr($area); ?>" class="regular-text" />
                        <p class="description"><?php _e('Optional: District area if available', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
            </table>
        </div>

        <div style="margin-bottom: 20px;">
            <h3><?php _e('District Contact Information', 'esp-admin-manager'); ?></h3>
            <p class="description" style="margin-bottom: 15px;">
                <?php _e('Contact details specific to this district. These will be displayed using shortcodes like [dakoii_contact district_id="' . $post->ID . '"] or [dakoii_district_contact id="' . $post->ID . '"]', 'esp-admin-manager'); ?>
            </p>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_address"><?php _e('District Office Address', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="esp_district_contact_address" name="esp_district_contact_address" rows="4" class="large-text"><?php echo esc_textarea($contact_address); ?></textarea>
                        <p class="description"><?php _e('Complete physical address of the district office', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_phone"><?php _e('Phone Number', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_district_contact_phone" name="esp_district_contact_phone" value="<?php echo esc_attr($contact_phone); ?>" class="regular-text" />
                        <p class="description"><?php _e('Main district office phone number', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_fax"><?php _e('Fax Number', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_district_contact_fax" name="esp_district_contact_fax" value="<?php echo esc_attr($contact_fax); ?>" class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_emergency"><?php _e('Emergency Contact', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_district_contact_emergency" name="esp_district_contact_emergency" value="<?php echo esc_attr($contact_emergency); ?>" class="regular-text" />
                        <p class="description"><?php _e('Emergency contact number for the district', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_email"><?php _e('General Email', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="esp_district_contact_email" name="esp_district_contact_email" value="<?php echo esc_attr($contact_email); ?>" class="regular-text" />
                        <p class="description"><?php _e('General inquiries email address', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_admin_email"><?php _e('Administrative Email', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="esp_district_contact_admin_email" name="esp_district_contact_admin_email" value="<?php echo esc_attr($contact_admin_email); ?>" class="regular-text" />
                        <p class="description"><?php _e('Administrative/official business email', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_website"><?php _e('Website', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="url" id="esp_district_contact_website" name="esp_district_contact_website" value="<?php echo esc_attr($contact_website); ?>" class="regular-text" />
                        <p class="description"><?php _e('District website URL (if available)', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_district_contact_office_hours"><?php _e('Office Hours', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="esp_district_contact_office_hours" name="esp_district_contact_office_hours" rows="3" class="large-text"><?php echo esc_textarea($contact_office_hours); ?></textarea>
                        <p class="description"><?php _e('District office operating hours', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
            </table>
        </div>
        <?php
    }

    /**
     * Contact meta box callback
     */
    public function contact_meta_box_callback($post) {
        wp_nonce_field('provincial_contact_meta_box', 'provincial_contact_meta_box_nonce');

        // Get contact information
        $address = get_post_meta($post->ID, '_esp_contact_address', true);
        $phone = get_post_meta($post->ID, '_esp_contact_phone', true);
        $fax = get_post_meta($post->ID, '_esp_contact_fax', true);
        $emergency = get_post_meta($post->ID, '_esp_contact_emergency', true);
        $email = get_post_meta($post->ID, '_esp_contact_email', true);
        $admin_email = get_post_meta($post->ID, '_esp_contact_admin_email', true);
        $website = get_post_meta($post->ID, '_esp_contact_website', true);
        $office_hours = get_post_meta($post->ID, '_esp_contact_office_hours', true);
        ?>
        <div style="margin-bottom: 20px;">
            <p class="description" style="margin-bottom: 15px;">
                <?php _e('Contact information for this contact. Use shortcode: [dakoii_contact id="' . $post->ID . '"] to display this contact.', 'esp-admin-manager'); ?>
            </p>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="esp_contact_address"><?php _e('Address', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="esp_contact_address" name="esp_contact_address" rows="4" class="large-text"><?php echo esc_textarea($address); ?></textarea>
                        <p class="description"><?php _e('Complete physical address', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_phone"><?php _e('Phone Number', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_contact_phone" name="esp_contact_phone" value="<?php echo esc_attr($phone); ?>" class="regular-text" />
                        <p class="description"><?php _e('Main contact phone number', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_fax"><?php _e('Fax Number', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_contact_fax" name="esp_contact_fax" value="<?php echo esc_attr($fax); ?>" class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_emergency"><?php _e('Emergency Contact', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="esp_contact_emergency" name="esp_contact_emergency" value="<?php echo esc_attr($emergency); ?>" class="regular-text" />
                        <p class="description"><?php _e('Emergency contact number', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_email"><?php _e('Email Address', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="esp_contact_email" name="esp_contact_email" value="<?php echo esc_attr($email); ?>" class="regular-text" />
                        <p class="description"><?php _e('General inquiries email address', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_admin_email"><?php _e('Administrative Email', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="esp_contact_admin_email" name="esp_contact_admin_email" value="<?php echo esc_attr($admin_email); ?>" class="regular-text" />
                        <p class="description"><?php _e('Administrative/official business email', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_website"><?php _e('Website', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="url" id="esp_contact_website" name="esp_contact_website" value="<?php echo esc_attr($website); ?>" class="regular-text" />
                        <p class="description"><?php _e('Website URL (if available)', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="esp_contact_office_hours"><?php _e('Office Hours', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="esp_contact_office_hours" name="esp_contact_office_hours" rows="3" class="large-text"><?php echo esc_textarea($office_hours); ?></textarea>
                        <p class="description"><?php _e('Office operating hours', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
            </table>
        </div>
        <?php
    }

    /**
     * Event meta box callback
     */
    public function event_meta_box_callback($post) {
        wp_nonce_field('provincial_event_meta_box', 'provincial_event_meta_box_nonce');

        $start_date = get_post_meta($post->ID, '_esp_event_start_date', true);
        $end_date = get_post_meta($post->ID, '_esp_event_end_date', true);
        $location = get_post_meta($post->ID, '_esp_event_location', true);
        $contact = get_post_meta($post->ID, '_esp_event_contact', true);
        $district_id = get_post_meta($post->ID, '_esp_district_id', true);

        // Get current user information for district selection
        $current_user_id = get_current_user_id();
        $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
        $is_district_user = ($user_type === 'district');

        // Get available districts based on user type
        if ($is_district_user && !current_user_can('manage_options')) {
            // District users see only their assigned districts
            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
            if (!empty($assigned_districts)) {
                $available_districts = get_posts(array(
                    'post_type' => 'esp_district',
                    'numberposts' => -1,
                    'post_status' => 'publish',
                    'post__in' => $assigned_districts,
                    'orderby' => 'title',
                    'order' => 'ASC'
                ));
            } else {
                $available_districts = array();
            }
        } else {
            // Provincial users and administrators see all districts
            $available_districts = get_posts(array(
                'post_type' => 'esp_district',
                'numberposts' => -1,
                'post_status' => 'publish',
                'orderby' => 'title',
                'order' => 'ASC'
            ));
        }
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="esp_event_start_date"><?php _e('Start Date', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="date" id="esp_event_start_date" name="esp_event_start_date" value="<?php echo esc_attr($start_date); ?>" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_event_end_date"><?php _e('End Date', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="date" id="esp_event_end_date" name="esp_event_end_date" value="<?php echo esc_attr($end_date); ?>" />
                    <p class="description"><?php _e('Leave empty for single-day events', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_event_location"><?php _e('Location', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_event_location" name="esp_event_location" value="<?php echo esc_attr($location); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_event_district"><?php _e('District', 'esp-admin-manager'); ?><?php if ($is_district_user) echo ' <span style="color: red;">*</span>'; ?></label>
                </th>
                <td>
                    <select id="esp_event_district" name="esp_event_district" class="regular-text">
                        <?php if (!$is_district_user): ?>
                            <option value=""><?php _e('Select District (Optional)', 'esp-admin-manager'); ?></option>
                        <?php else: ?>
                            <option value=""><?php _e('Select District', 'esp-admin-manager'); ?></option>
                        <?php endif; ?>
                        <?php foreach ($available_districts as $district): ?>
                            <option value="<?php echo esc_attr($district->ID); ?>" <?php selected($district_id, $district->ID); ?>>
                                <?php echo esc_html($district->post_title); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if ($is_district_user): ?>
                        <p class="description"><?php _e('Required: Select the district this event belongs to. You can only select from your assigned districts.', 'esp-admin-manager'); ?></p>
                    <?php else: ?>
                        <p class="description"><?php _e('Optional: Link this event to a specific district.', 'esp-admin-manager'); ?></p>
                    <?php endif; ?>
                    <?php if ($is_district_user && empty($available_districts)): ?>
                        <p class="description" style="color: red;"><?php _e('No districts assigned to your account. Please contact an administrator.', 'esp-admin-manager'); ?></p>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_event_contact"><?php _e('Contact Information', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <textarea id="esp_event_contact" name="esp_event_contact" rows="3" class="large-text"><?php echo esc_textarea($contact); ?></textarea>
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * News meta box callback
     */
    public function news_meta_box_callback($post) {
        wp_nonce_field('provincial_news_meta_box', 'provincial_news_meta_box_nonce');

        $news_date = get_post_meta($post->ID, '_esp_news_date', true);
        $source = get_post_meta($post->ID, '_esp_news_source', true);
        $featured = get_post_meta($post->ID, '_esp_news_featured', true);
        $district_id = get_post_meta($post->ID, '_esp_district_id', true);

        // Get current user information for district selection
        $current_user_id = get_current_user_id();
        $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
        $is_district_user = ($user_type === 'district');

        // Get available districts based on user type
        if ($is_district_user && !current_user_can('manage_options')) {
            // District users see only their assigned districts
            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
            if (!empty($assigned_districts)) {
                $available_districts = get_posts(array(
                    'post_type' => 'esp_district',
                    'numberposts' => -1,
                    'post_status' => 'publish',
                    'post__in' => $assigned_districts,
                    'orderby' => 'title',
                    'order' => 'ASC'
                ));
            } else {
                $available_districts = array();
            }
        } else {
            // Provincial users and administrators see all districts
            $available_districts = get_posts(array(
                'post_type' => 'esp_district',
                'numberposts' => -1,
                'post_status' => 'publish',
                'orderby' => 'title',
                'order' => 'ASC'
            ));
        }
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="esp_news_date"><?php _e('News Date', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="date" id="esp_news_date" name="esp_news_date" value="<?php echo esc_attr($news_date); ?>" />
                    <p class="description"><?php _e('Date when the news occurred (defaults to publish date)', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_news_source"><?php _e('Source', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="text" id="esp_news_source" name="esp_news_source" value="<?php echo esc_attr($source); ?>" class="regular-text" />
                    <p class="description"><?php _e('e.g., Provincial Administration, Department of Health', 'esp-admin-manager'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_news_district"><?php _e('District', 'esp-admin-manager'); ?><?php if ($is_district_user) echo ' <span style="color: red;">*</span>'; ?></label>
                </th>
                <td>
                    <select id="esp_news_district" name="esp_news_district" class="regular-text">
                        <?php if (!$is_district_user): ?>
                            <option value=""><?php _e('Select District (Optional)', 'esp-admin-manager'); ?></option>
                        <?php else: ?>
                            <option value=""><?php _e('Select District', 'esp-admin-manager'); ?></option>
                        <?php endif; ?>
                        <?php foreach ($available_districts as $district): ?>
                            <option value="<?php echo esc_attr($district->ID); ?>" <?php selected($district_id, $district->ID); ?>>
                                <?php echo esc_html($district->post_title); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if ($is_district_user): ?>
                        <p class="description"><?php _e('Required: Select the district this news belongs to. You can only select from your assigned districts.', 'esp-admin-manager'); ?></p>
                    <?php else: ?>
                        <p class="description"><?php _e('Optional: Link this news to a specific district.', 'esp-admin-manager'); ?></p>
                    <?php endif; ?>
                    <?php if ($is_district_user && empty($available_districts)): ?>
                        <p class="description" style="color: red;"><?php _e('No districts assigned to your account. Please contact an administrator.', 'esp-admin-manager'); ?></p>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="esp_news_featured"><?php _e('Featured News', 'esp-admin-manager'); ?></label>
                </th>
                <td>
                    <input type="checkbox" id="esp_news_featured" name="esp_news_featured" value="1" <?php checked($featured, '1'); ?> />
                    <label for="esp_news_featured"><?php _e('Mark as featured news', 'esp-admin-manager'); ?></label>
                </td>
            </tr>
        </table>
        <?php
    }

    /**
     * Map meta box callback
     */
    public function map_meta_box_callback($post) {
        wp_nonce_field('provincial_map_meta_box', 'provincial_map_meta_box_nonce');

        $json_file = get_post_meta($post->ID, '_esp_map_json_file', true);
        $json_path = get_post_meta($post->ID, '_esp_map_json_path', true);
        $upload_dir = wp_upload_dir();

        ?>
        <div class="esp-map-current-file">
            <h3><?php _e('Current Map File', 'esp-admin-manager'); ?></h3>
            <?php if (!empty($json_file)): ?>
                <p><strong><?php _e('Current File:', 'esp-admin-manager'); ?></strong> <?php echo esc_html($json_file); ?></p>
                <p><strong><?php _e('Upload Date:', 'esp-admin-manager'); ?></strong> <?php echo get_the_date('Y-m-d H:i:s', $post->ID); ?></p>
                <?php
                $file_url = $upload_dir['baseurl'] . '/provincial-maps/' . $json_file;
                if (file_exists($json_path)):
                ?>
                    <p>
                        <a href="<?php echo esc_url($file_url); ?>" target="_blank" class="button button-secondary">
                            <span class="dashicons dashicons-download" style="vertical-align: middle;"></span>
                            <?php _e('Download Current File', 'esp-admin-manager'); ?>
                        </a>
                    </p>
                    <p class="description">
                        <?php _e('File size:', 'esp-admin-manager'); ?> <?php echo size_format(filesize($json_path)); ?>
                    </p>
                <?php else: ?>
                    <div class="esp-map-file-warning">
                        <strong><?php _e('Warning:', 'esp-admin-manager'); ?></strong>
                        <?php _e('File not found on server. Please upload a new file.', 'esp-admin-manager'); ?>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <p class="description"><?php _e('No map file uploaded yet.', 'esp-admin-manager'); ?></p>
            <?php endif; ?>
        </div>

        <div class="esp-map-upload-section">
            <h3><?php _e('Upload New Map File', 'esp-admin-manager'); ?></h3>
            <p class="description" style="margin-bottom: 15px;">
                <?php _e('Upload a new JSON file to replace the current map data. Leave empty to keep the current file.', 'esp-admin-manager'); ?>
            </p>
            <input type="file" id="esp_map_json_file" name="esp_map_json_file" accept=".json" />
            <p class="description" style="margin-top: 10px;">
                <strong><?php _e('Supported format:', 'esp-admin-manager'); ?></strong> JSON (GeoJSON boundary files)
            </p>
        </div>

        <div class="esp-map-shortcode-section">
            <h3><?php _e('Map Usage', 'esp-admin-manager'); ?></h3>
            <p><?php _e('Use this shortcode to display the map:', 'esp-admin-manager'); ?></p>
            <p>
                <code>[dakoii_map id="<?php echo $post->ID; ?>"]</code>
                <button type="button" class="button button-small copy-shortcode" data-shortcode='[dakoii_map id="<?php echo $post->ID; ?>"]'>
                    <span class="dashicons dashicons-clipboard" style="vertical-align: middle;"></span>
                    <?php _e('Copy', 'esp-admin-manager'); ?>
                </button>
            </p>
            <p class="description">
                <?php _e('You can also customize the display with width and height parameters:', 'esp-admin-manager'); ?><br>
                <code>[dakoii_map id="<?php echo $post->ID; ?>" width="100%" height="500px"]</code>
            </p>
        </div>

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.querySelector('.copy-shortcode');
            if (copyButton) {
                copyButton.addEventListener('click', function() {
                    const shortcode = this.getAttribute('data-shortcode');
                    navigator.clipboard.writeText(shortcode).then(() => {
                        this.textContent = '<?php _e('Copied!', 'esp-admin-manager'); ?>';
                        setTimeout(() => {
                            this.textContent = '<?php _e('Copy', 'esp-admin-manager'); ?>';
                        }, 2000);
                    });
                });
            }
        });
        </script>
        <?php
    }

    /**
     * Save meta boxes
     */
    public function save_meta_boxes($post_id) {
        // Check if nonce is valid
        if (!isset($_POST['provincial_governor_meta_box_nonce']) &&
            !isset($_POST['provincial_mp_meta_box_nonce']) &&
            !isset($_POST['provincial_district_meta_box_nonce']) &&
            !isset($_POST['provincial_contact_meta_box_nonce']) &&
            !isset($_POST['provincial_event_meta_box_nonce']) &&
            !isset($_POST['provincial_news_meta_box_nonce']) &&
            !isset($_POST['provincial_map_meta_box_nonce'])) {
            return;
        }
        
        // Check if user has permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Check if not an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        $post_type = get_post_type($post_id);

        // Validate district user permissions for districts and MPs
        $this->validate_district_user_permissions($post_id, $post_type);
        
        // Save governor meta
        if ($post_type === 'esp_governor' && isset($_POST['provincial_governor_meta_box_nonce']) && 
            wp_verify_nonce($_POST['provincial_governor_meta_box_nonce'], 'provincial_governor_meta_box')) {
            
            update_post_meta($post_id, '_esp_governor_title', sanitize_text_field($_POST['esp_governor_title']));
            update_post_meta($post_id, '_esp_governor_party', sanitize_text_field($_POST['esp_governor_party']));
            update_post_meta($post_id, '_esp_governor_email', sanitize_email($_POST['esp_governor_email']));
            update_post_meta($post_id, '_esp_governor_phone', sanitize_text_field($_POST['esp_governor_phone']));
        }
        
        // Save MP meta
        if ($post_type === 'esp_mp' && isset($_POST['provincial_mp_meta_box_nonce']) &&
            wp_verify_nonce($_POST['provincial_mp_meta_box_nonce'], 'provincial_mp_meta_box')) {

            update_post_meta($post_id, '_esp_mp_electorate', sanitize_text_field($_POST['esp_mp_electorate']));
            update_post_meta($post_id, '_esp_mp_party', sanitize_text_field($_POST['esp_mp_party']));
            update_post_meta($post_id, '_esp_mp_district_id', intval($_POST['esp_mp_district_id']));
            update_post_meta($post_id, '_esp_mp_message', sanitize_textarea_field($_POST['esp_mp_message']));
        }
        
        // Save district meta
        if ($post_type === 'esp_district' && isset($_POST['provincial_district_meta_box_nonce']) &&
            wp_verify_nonce($_POST['provincial_district_meta_box_nonce'], 'provincial_district_meta_box')) {

            // Save basic district information
            update_post_meta($post_id, '_esp_district_llgs', sanitize_text_field($_POST['esp_district_llgs']));
            update_post_meta($post_id, '_esp_district_wards', sanitize_text_field($_POST['esp_district_wards']));
            update_post_meta($post_id, '_esp_district_population', sanitize_text_field($_POST['esp_district_population']));
            update_post_meta($post_id, '_esp_district_area', sanitize_text_field($_POST['esp_district_area']));

            // Save district contact information
            update_post_meta($post_id, '_esp_district_contact_address', sanitize_textarea_field($_POST['esp_district_contact_address']));
            update_post_meta($post_id, '_esp_district_contact_phone', sanitize_text_field($_POST['esp_district_contact_phone']));
            update_post_meta($post_id, '_esp_district_contact_fax', sanitize_text_field($_POST['esp_district_contact_fax']));
            update_post_meta($post_id, '_esp_district_contact_emergency', sanitize_text_field($_POST['esp_district_contact_emergency']));
            update_post_meta($post_id, '_esp_district_contact_email', sanitize_email($_POST['esp_district_contact_email']));
            update_post_meta($post_id, '_esp_district_contact_admin_email', sanitize_email($_POST['esp_district_contact_admin_email']));
            update_post_meta($post_id, '_esp_district_contact_website', sanitize_url($_POST['esp_district_contact_website']));
            update_post_meta($post_id, '_esp_district_contact_office_hours', sanitize_textarea_field($_POST['esp_district_contact_office_hours']));
        }

        // Save contact meta
        if ($post_type === 'esp_contact' && isset($_POST['provincial_contact_meta_box_nonce']) &&
            wp_verify_nonce($_POST['provincial_contact_meta_box_nonce'], 'provincial_contact_meta_box')) {

            update_post_meta($post_id, '_esp_contact_address', sanitize_textarea_field($_POST['esp_contact_address']));
            update_post_meta($post_id, '_esp_contact_phone', sanitize_text_field($_POST['esp_contact_phone']));
            update_post_meta($post_id, '_esp_contact_fax', sanitize_text_field($_POST['esp_contact_fax']));
            update_post_meta($post_id, '_esp_contact_emergency', sanitize_text_field($_POST['esp_contact_emergency']));
            update_post_meta($post_id, '_esp_contact_email', sanitize_email($_POST['esp_contact_email']));
            update_post_meta($post_id, '_esp_contact_admin_email', sanitize_email($_POST['esp_contact_admin_email']));
            update_post_meta($post_id, '_esp_contact_website', sanitize_url($_POST['esp_contact_website']));
            update_post_meta($post_id, '_esp_contact_office_hours', sanitize_textarea_field($_POST['esp_contact_office_hours']));
        }

        // Save event meta
        if ($post_type === 'esp_event' && isset($_POST['provincial_event_meta_box_nonce']) &&
            wp_verify_nonce($_POST['provincial_event_meta_box_nonce'], 'provincial_event_meta_box')) {

            // Get current user information for validation
            $current_user_id = get_current_user_id();
            $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
            $is_district_user = ($user_type === 'district');

            // Handle district selection
            $selected_district_id = isset($_POST['esp_event_district']) ? intval($_POST['esp_event_district']) : 0;

            // Validate district selection for district users
            if ($is_district_user && !current_user_can('manage_options')) {
                // District users must select a district
                if (empty($selected_district_id)) {
                    wp_die(
                        __('District selection is required for district users.', 'esp-admin-manager'),
                        __('Missing Required Field', 'esp-admin-manager'),
                        array('response' => 400)
                    );
                }

                // Validate that the selected district is in user's assigned districts
                $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
                if (!in_array($selected_district_id, $assigned_districts)) {
                    wp_die(
                        __('You can only assign events to districts you have access to.', 'esp-admin-manager'),
                        __('Permission Denied', 'esp-admin-manager'),
                        array('response' => 403)
                    );
                }
            }

            update_post_meta($post_id, '_esp_event_start_date', sanitize_text_field($_POST['esp_event_start_date']));
            update_post_meta($post_id, '_esp_event_end_date', sanitize_text_field($_POST['esp_event_end_date']));
            update_post_meta($post_id, '_esp_event_location', sanitize_text_field($_POST['esp_event_location']));
            update_post_meta($post_id, '_esp_event_contact', sanitize_textarea_field($_POST['esp_event_contact']));
            update_post_meta($post_id, '_esp_district_id', $selected_district_id);
        }
        
        // Save news meta
        if ($post_type === 'esp_news' && isset($_POST['provincial_news_meta_box_nonce']) &&
            wp_verify_nonce($_POST['provincial_news_meta_box_nonce'], 'provincial_news_meta_box')) {

            // Get current user information for validation
            $current_user_id = get_current_user_id();
            $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
            $is_district_user = ($user_type === 'district');

            // Handle district selection
            $selected_district_id = isset($_POST['esp_news_district']) ? intval($_POST['esp_news_district']) : 0;

            // Validate district selection for district users
            if ($is_district_user && !current_user_can('manage_options')) {
                // District users must select a district
                if (empty($selected_district_id)) {
                    wp_die(
                        __('District selection is required for district users.', 'esp-admin-manager'),
                        __('Missing Required Field', 'esp-admin-manager'),
                        array('response' => 400)
                    );
                }

                // Validate that the selected district is in user's assigned districts
                $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
                if (!in_array($selected_district_id, $assigned_districts)) {
                    wp_die(
                        __('You can only assign news to districts you have access to.', 'esp-admin-manager'),
                        __('Permission Denied', 'esp-admin-manager'),
                        array('response' => 403)
                    );
                }
            }

            update_post_meta($post_id, '_esp_news_date', sanitize_text_field($_POST['esp_news_date']));
            update_post_meta($post_id, '_esp_news_source', sanitize_text_field($_POST['esp_news_source']));
            update_post_meta($post_id, '_esp_news_featured', isset($_POST['esp_news_featured']) ? '1' : '0');
            update_post_meta($post_id, '_esp_district_id', $selected_district_id);
        }

        // Save map meta and handle file upload
        if ($post_type === 'esp_map' && isset($_POST['provincial_map_meta_box_nonce']) &&
            wp_verify_nonce($_POST['provincial_map_meta_box_nonce'], 'provincial_map_meta_box')) {

            // Handle file upload if a new file was selected
            if (isset($_FILES['esp_map_json_file']) && $_FILES['esp_map_json_file']['error'] === UPLOAD_ERR_OK) {
                $this->handle_map_file_upload($post_id);
            }
        }
    }

    /**
     * Handle map file upload for meta box
     */
    private function handle_map_file_upload($post_id) {
        $file = $_FILES['esp_map_json_file'];

        // Validate file type
        $file_info = pathinfo($file['name']);
        if (strtolower($file_info['extension']) !== 'json') {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Only JSON files are allowed.', 'esp-admin-manager') . '</p></div>';
            });
            return false;
        }

        // Create upload directory
        $upload_dir = wp_upload_dir();
        $maps_dir = $upload_dir['basedir'] . '/provincial-maps/';
        if (!file_exists($maps_dir)) {
            wp_mkdir_p($maps_dir);
        }

        // Remove old file if it exists
        $old_json_file = get_post_meta($post_id, '_esp_map_json_file', true);
        $old_json_path = get_post_meta($post_id, '_esp_map_json_path', true);
        if (!empty($old_json_path) && file_exists($old_json_path)) {
            unlink($old_json_path);
        }

        // Generate unique filename
        $filename = time() . '_' . sanitize_file_name($file['name']);
        $file_path = $maps_dir . $filename;

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $file_path)) {
            // Update meta data
            update_post_meta($post_id, '_esp_map_json_file', $filename);
            update_post_meta($post_id, '_esp_map_json_path', $file_path);

            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Map file updated successfully.', 'esp-admin-manager') . '</p></div>';
            });
            return true;
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Failed to upload map file.', 'esp-admin-manager') . '</p></div>';
            });
            return false;
        }
    }

    /**
     * Validate district user permissions for editing districts and MPs
     */
    private function validate_district_user_permissions($post_id, $post_type) {
        // Only validate for district and MP post types
        if (!in_array($post_type, array('esp_district', 'esp_mp'))) {
            return;
        }

        $current_user_id = get_current_user_id();
        $user_type = get_user_meta($current_user_id, 'provincial_user_type', true);

        // Skip validation for administrators and provincial users
        if (current_user_can('manage_options') || current_user_can('administrator') || $user_type === 'provincial' || Provincial_User_Roles::user_has_provincial_access($current_user_id)) {
            return;
        }

        // Validate district users
        if ($user_type === 'district') {
            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

            if ($post_type === 'esp_district') {
                // For districts, check if the district being edited is in user's assigned districts
                if (!in_array($post_id, $assigned_districts)) {
                    wp_die(
                        __('You do not have permission to edit this district. You can only edit districts assigned to your account.', 'esp-admin-manager'),
                        __('Permission Denied', 'esp-admin-manager'),
                        array('response' => 403)
                    );
                }
            } elseif ($post_type === 'esp_mp') {
                // For MPs, district users must assign MPs to one of their assigned districts
                $mp_district_id = get_post_meta($post_id, '_esp_mp_district_id', true);

                // Check if editing existing MP
                if (!empty($mp_district_id) && !in_array($mp_district_id, $assigned_districts)) {
                    wp_die(
                        __('You do not have permission to edit this MP. You can only edit MPs from your assigned districts.', 'esp-admin-manager'),
                        __('Permission Denied', 'esp-admin-manager'),
                        array('response' => 403)
                    );
                }

                // Validate the district assignment in POST data (for both new and existing MPs)
                if (isset($_POST['esp_mp_district_id'])) {
                    $new_district_id = intval($_POST['esp_mp_district_id']);

                    // District users must assign MP to one of their assigned districts
                    if (empty($new_district_id) || !in_array($new_district_id, $assigned_districts)) {
                        wp_die(
                            __('You must assign this MP to one of your assigned districts. MPs without district assignment are not allowed for district users.', 'esp-admin-manager'),
                            __('Permission Denied', 'esp-admin-manager'),
                            array('response' => 403)
                        );
                    }
                }
            }
        }
    }
}
