# Multiple Contacts System - Complete Implementation Guide

## Overview
The Multiple Contacts System allows you to create unlimited contact sets, each with its own shortcode. This is perfect for different departments, offices, services, or any organizational unit that needs its own contact information.

## 🎯 **What's Available Now**

### 1. **Legacy Provincial Contact (Unchanged)**
- **Location**: WordPress Admin → Contact Information (top section)
- **Shortcode**: `[dakoii_contact]` (no parameters)
- **Purpose**: Main provincial headquarters contact
- **Status**: ✅ Fully functional and unchanged

### 2. **District Contacts (Previously Implemented)**
- **Location**: WordPress Admin → Districts → Edit District
- **Shortcodes**: `[dakoii_contact district_id="123"]` or `[dakoii_district_contact id="123"]`
- **Purpose**: Individual district office contacts
- **Status**: ✅ Fully functional

### 3. **Multiple Provincial Contacts (NEW)**
- **Location**: WordPress Admin → Contact Information (bottom section)
- **Shortcodes**: `[dakoii_contact id="contact_id"]` or `[dakoii_provincial_contact id="contact_id"]`
- **Purpose**: Multiple contact sets for different departments/services
- **Status**: ✅ **NEWLY IMPLEMENTED**

## 🚀 **How to Use Multiple Provincial Contacts**

### Step 1: Access the Contact Management
1. Go to WordPress Admin
2. Navigate to **Contact Information** (in the Provincial Admin menu)
3. Scroll down to the **"Multiple Contact Sets"** section

### Step 2: Create a New Contact
1. Click **"Add New Contact"** button
2. Fill in the contact details:
   - **Contact Name** (required): e.g., "Emergency Services", "Tourism Office", "Health Department"
   - **Address**: Physical office address
   - **Phone Number**: Main contact number
   - **Email Address**: General inquiries email
   - **Other fields**: Fax, emergency contact, admin email, website, office hours
3. Click **"Add Contact"**

### Step 3: Get Your Shortcode
After creating a contact, you'll see it in the contacts list with its unique shortcode:
```
[dakoii_contact id="emergency_services_1234567890"]
```

### Step 4: Use the Shortcode
Copy the shortcode and paste it anywhere on your website:
- Pages
- Posts  
- Widgets
- Theme templates

## 📋 **Available Shortcodes**

### Multiple Provincial Contacts
```
[dakoii_contact id="contact_id"]
[dakoii_contact id="contact_id" layout="list"]
[dakoii_provincial_contact id="contact_id"]
[dakoii_provincial_contact id="contact_id" layout="grid"]
```

### District Contacts
```
[dakoii_contact district_id="123"]
[dakoii_district_contact id="123"]
```

### Legacy Provincial Contact
```
[dakoii_contact]
[dakoii_prov_admin_contact]
```

### Alternative Naming Conventions
All these work identically:
- **Primary**: `dakoii_*`
- **Generic**: `provincial_*`
- **Legacy**: `esp_*`

## 💡 **Example Use Cases**

### Emergency Services Contact
```
Contact Name: Emergency Services
Shortcode: [dakoii_contact id="emergency_services_1234567890"]
```

### Tourism Office Contact
```
Contact Name: Tourism Information Office
Shortcode: [dakoii_contact id="tourism_information_office_1234567891"]
```

### Health Department Contact
```
Contact Name: Provincial Health Department
Shortcode: [dakoii_contact id="provincial_health_department_1234567892"]
```

## 🔧 **Management Features**

### Contact List View
- See all your contacts at a glance
- View contact name, phone, email
- Copy shortcodes with one click
- Edit or delete contacts easily

### Edit Contacts
- Click "Edit" next to any contact
- Modify any field
- Shortcode remains the same

### Delete Contacts
- Click "Delete" next to any contact
- Confirmation dialog prevents accidents
- Shortcodes will stop working after deletion

## 🎨 **Layout Options**

### Grid Layout (Default)
```
[dakoii_contact id="contact_id" layout="grid"]
```
Displays contact cards in a responsive grid.

### List Layout
```
[dakoii_contact id="contact_id" layout="list"]
```
Displays contact information in a vertical list.

## 🔒 **Permissions & Security**

- Same permission system as existing contacts
- District-level users see only their assigned content
- Provincial-level users see all contacts
- Public users see published content
- All data is properly sanitized and validated

## 🔄 **Backward Compatibility**

✅ **100% Backward Compatible**
- All existing shortcodes continue to work
- Legacy provincial contact unchanged
- District contacts unchanged
- No breaking changes

## 📊 **Database Structure**

### Multiple Contacts Storage
```php
// Stored in WordPress option: esp_multiple_contacts
array(
    'contact_id_1' => array(
        'id' => 'contact_id_1',
        'name' => 'Emergency Services',
        'address' => '...',
        'phone' => '...',
        'email' => '...',
        // ... other fields
    ),
    'contact_id_2' => array(
        'id' => 'contact_id_2',
        'name' => 'Tourism Office',
        // ... contact data
    )
);
```

### Contact ID Generation
- Based on contact name + timestamp
- Automatically sanitized for URL safety
- Unique and permanent

## 🧪 **Testing Your Implementation**

### Test 1: Create a Contact
1. Add a new contact called "Test Department"
2. Fill in some basic information
3. Save the contact
4. Note the generated shortcode

### Test 2: Use the Shortcode
1. Create a new page or post
2. Add the shortcode: `[dakoii_contact id="your_contact_id"]`
3. Preview/publish the page
4. Verify the contact information displays correctly

### Test 3: Try Different Layouts
1. Test grid layout: `[dakoii_contact id="your_contact_id" layout="grid"]`
2. Test list layout: `[dakoii_contact id="your_contact_id" layout="list"]`

## 🆘 **Troubleshooting**

### Contact Not Displaying
- ✅ Check the contact ID is correct
- ✅ Ensure the contact has some information filled in
- ✅ Verify shortcode syntax

### Shortcode Not Working
- ✅ Check for typos in the shortcode
- ✅ Ensure the contact hasn't been deleted
- ✅ Try clearing any caching plugins

### Permission Issues
- ✅ Check user permissions
- ✅ Verify contact is properly saved
- ✅ Test with admin user

## 🎉 **Summary**

You now have a complete contact management system with:
- ✅ Legacy provincial contact (unchanged)
- ✅ District-specific contacts
- ✅ Multiple provincial contacts (NEW)
- ✅ Unique shortcodes for each contact set
- ✅ Easy management interface
- ✅ Full backward compatibility

Each contact set gets its own shortcode, exactly as requested! The system is flexible, scalable, and maintains all existing functionality while adding powerful new capabilities.
