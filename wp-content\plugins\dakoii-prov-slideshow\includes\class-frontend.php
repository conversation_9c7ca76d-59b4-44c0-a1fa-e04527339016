<?php
class DakoiiSlideshowFrontend {
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_shortcode('dakoii_slideshow', array($this, 'shortcode_handler'));
        add_action('init', array($this, 'add_template_function'));
    }
    
    public function enqueue_scripts() {
        wp_enqueue_style(
            'dakoii-slideshow-css',
            DAKOII_SLIDESHOW_PLUGIN_URL . 'public/css/slideshow-style.css',
            array(),
            DAKOII_SLIDESHOW_VERSION
        );
        
        wp_enqueue_script(
            'dakoii-slideshow-js',
            DAKOII_SLIDESHOW_PLUGIN_URL . 'public/js/slideshow-script.js',
            array('jquery'),
            DAKOII_SLIDESHOW_VERSION,
            true
        );
    }
    
    public function shortcode_handler($atts) {
        $atts = shortcode_atts(array(
            'id' => 0,
            'tag' => '',
            'autoplay' => true,
            'duration' => 5000,
            'show_nav' => true,
            'show_dots' => true
        ), $atts);
        
        if ($atts['id']) {
            return $this->render_slideshow_by_id($atts['id'], $atts);
        } elseif ($atts['tag']) {
            return $this->render_slideshow_by_tag($atts['tag'], $atts);
        }
        
        return '<p>No slideshow specified.</p>';
    }
    
    private function render_slideshow_by_id($group_id, $options) {
        $slides = DakoiiSlideshow::get_slides($group_id);
        if (!$slides) {
            return '<p>No slides found for this slideshow.</p>';
        }
        
        return $this->render_slideshow($slides, $group_id, $options);
    }
    
    private function render_slideshow_by_tag($tag, $options) {
        $groups = DakoiiSlideshow::get_groups_by_tag($tag);
        if (!$groups) {
            return '<p>No slideshow found with tag: ' . esc_html($tag) . '</p>';
        }
        
        // Use the first group found with this tag
        $group = $groups[0];
        $slides = DakoiiSlideshow::get_slides($group->id);
        
        if (!$slides) {
            return '<p>No slides found for this slideshow.</p>';
        }
        
        return $this->render_slideshow($slides, $group->id, $options);
    }
    
    private function render_slideshow($slides, $group_id, $options) {
        $slideshow_id = 'dakoii-slideshow-' . $group_id . '-' . rand(1000, 9999);
        
        ob_start();
        ?>
        <div class="dakoii-slideshow" id="<?php echo $slideshow_id; ?>" 
             data-autoplay="<?php echo $options['autoplay'] ? 'true' : 'false'; ?>"
             data-duration="<?php echo intval($options['duration']); ?>">
            
            <div class="slideshow-container">
                <?php foreach ($slides as $index => $slide): ?>
                    <div class="slide <?php echo $index === 0 ? 'active' : ''; ?>">
                        <?php if ($slide->link_url): ?>
                            <a href="<?php echo esc_url($slide->link_url); ?>">
                        <?php endif; ?>
                        
                        <img src="<?php echo esc_url($slide->image_url); ?>" 
                             alt="<?php echo esc_attr($slide->title); ?>">
                        
                        <?php if ($slide->title || $slide->description): ?>
                            <div class="slide-content">
                                <?php if ($slide->title): ?>
                                    <h3><?php echo esc_html($slide->title); ?></h3>
                                <?php endif; ?>
                                <?php if ($slide->description): ?>
                                    <p><?php echo esc_html($slide->description); ?></p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($slide->link_url): ?>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <?php if ($options['show_nav'] && count($slides) > 1): ?>
                <button class="slideshow-nav prev" onclick="dakoiiSlideshowPrev('<?php echo $slideshow_id; ?>')">‹</button>
                <button class="slideshow-nav next" onclick="dakoiiSlideshowNext('<?php echo $slideshow_id; ?>')">›</button>
            <?php endif; ?>
            
            <?php if ($options['show_dots'] && count($slides) > 1): ?>
                <div class="slideshow-dots">
                    <?php foreach ($slides as $index => $slide): ?>
                        <button class="dot <?php echo $index === 0 ? 'active' : ''; ?>" 
                                onclick="dakoiiSlideshowGoTo('<?php echo $slideshow_id; ?>', <?php echo $index; ?>)"></button>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        <?php
        
        return ob_get_clean();
    }
    
    public function add_template_function() {
        // Make function available for theme developers
        if (!function_exists('dakoii_get_slideshow')) {
            function dakoii_get_slideshow($id = 0, $tag = '', $options = array()) {
                $frontend = new DakoiiSlideshowFrontend();
                $atts = array_merge(array(
                    'id' => $id,
                    'tag' => $tag
                ), $options);
                return $frontend->shortcode_handler($atts);
            }
        }
    }
}