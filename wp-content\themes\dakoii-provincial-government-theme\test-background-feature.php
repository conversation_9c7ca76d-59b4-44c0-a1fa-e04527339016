<?php
/**
 * Test file to verify background image feature functionality
 * 
 * This file can be used to test if the background image controls are properly registered
 * and accessible in the WordPress Customizer.
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if current user can customize
if (!current_user_can('customize')) {
    wp_die('You do not have permission to access this page.');
}

// Get all theme mods related to background
$background_settings = array(
    'background_image' => get_theme_mod('background_image'),
    'background_color' => get_theme_mod('background_color'),
    'background_repeat' => get_theme_mod('background_repeat', 'no-repeat'),
    'background_position' => get_theme_mod('background_position', 'center center'),
    'background_size' => get_theme_mod('background_size', 'cover'),
    'background_attachment' => get_theme_mod('background_attachment', 'fixed'),
);

// Check if customizer sections are registered
global $wp_customize;
if (!isset($wp_customize)) {
    require_once(ABSPATH . WPINC . '/class-wp-customize-manager.php');
    $wp_customize = new WP_Customize_Manager();
}

// Get the background image section
$background_section = $wp_customize->get_section('background_image');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Background Image Feature Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .setting { margin: 5px 0; padding: 5px; background-color: #f8f9fa; border-left: 3px solid #007bff; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Background Image Feature Test Results</h1>
    
    <h2>Customizer Section Status</h2>
    <?php if ($background_section): ?>
        <div class="test-result success">
            ✓ Background Image section is registered in the customizer
        </div>
        <div class="info">
            <strong>Section Details:</strong><br>
            Title: <?php echo esc_html($background_section->title); ?><br>
            Priority: <?php echo esc_html($background_section->priority); ?><br>
            Description: <?php echo esc_html($background_section->description); ?>
        </div>
    <?php else: ?>
        <div class="test-result error">
            ✗ Background Image section is NOT registered in the customizer
        </div>
    <?php endif; ?>
    
    <h2>Current Background Settings</h2>
    <?php foreach ($background_settings as $key => $value): ?>
        <div class="setting">
            <strong><?php echo esc_html(str_replace('_', ' ', ucfirst($key))); ?>:</strong> 
            <?php 
            if ($key === 'background_image' && $value) {
                echo '<a href="' . esc_url($value) . '" target="_blank">' . esc_url($value) . '</a>';
                echo '<br><img src="' . esc_url($value) . '" style="max-width: 200px; max-height: 100px; margin-top: 5px;" alt="Background Image">';
            } elseif ($key === 'background_image' && !$value) {
                echo '<em>No image uploaded</em>';
            } else {
                echo esc_html($value ?: 'Not set (using default)');
            }
            ?>
        </div>
    <?php endforeach; ?>
    
    <h2>Customizer Access</h2>
    <div class="info">
        <p>To access the background image controls:</p>
        <ol>
            <li>Go to <strong>WordPress Admin > Appearance > Customize</strong></li>
            <li>Look for the <strong>"Background Image"</strong> section</li>
            <li>Use the controls to upload and configure your background image</li>
        </ol>
    </div>
    
    <h2>Expected Controls</h2>
    <div class="info">
        <p>The Background Image section should contain these controls:</p>
        <ul>
            <li><strong>Upload Background Image</strong> - Media upload control</li>
            <li><strong>Background Color (Optional)</strong> - Color picker</li>
            <li><strong>Background Repeat</strong> - Select: No Repeat, Tile, Tile Horizontally, Tile Vertically</li>
            <li><strong>Background Position</strong> - Select: 9 positioning options</li>
            <li><strong>Background Size</strong> - Select: Original Size, Fit to Screen, Fill Screen</li>
            <li><strong>Background Attachment</strong> - Select: Scroll with Page, Fixed (Parallax Effect)</li>
        </ul>
    </div>
    
    <h2>Generated CSS Preview</h2>
    <pre><?php
    // Show what CSS would be generated
    if ($bg_image) {
        echo "body {\n";
        echo "    background-image: url(" . esc_url($bg_image) . ");\n";
        echo "    background-repeat: " . esc_attr($bg_repeat) . ";\n";
        echo "    background-position: " . esc_attr($bg_position) . ";\n";
        echo "    background-size: " . esc_attr($bg_size) . ";\n";
        echo "    background-attachment: " . esc_attr($bg_attachment) . ";\n";
        if ($bg_color) {
            echo "    background-color: " . esc_attr($bg_color) . ";\n";
        }
        echo "}\n";
    } else {
        echo "body {\n";
        echo "    background: linear-gradient(135deg, " . esc_attr($colors['png-green']) . " 0%, " . esc_attr($colors['dark-green']) . " 100%);\n";
        if ($bg_color) {
            echo "    background-color: " . esc_attr($bg_color) . ";\n";
        }
        echo "}\n";
    }
    ?></pre>
</body>
</html>
