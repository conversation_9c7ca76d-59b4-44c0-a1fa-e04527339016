<?php
class DakoiiSlideshow {
    
    public static function create_group($name, $description = '', $tags = '') {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        
        return $wpdb->insert(
            $table,
            array(
                'name' => sanitize_text_field($name),
                'description' => sanitize_textarea_field($description),
                'tags' => sanitize_text_field($tags)
            ),
            array('%s', '%s', '%s')
        );
    }
    
    public static function get_groups() {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        return $wpdb->get_results("SELECT * FROM $table ORDER BY name");
    }
    
    public static function get_group($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE id = %d", $id));
    }
    
    public static function update_group($id, $name, $description = '', $tags = '') {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        
        return $wpdb->update(
            $table,
            array(
                'name' => sanitize_text_field($name),
                'description' => sanitize_textarea_field($description),
                'tags' => sanitize_text_field($tags)
            ),
            array('id' => $id),
            array('%s', '%s', '%s'),
            array('%d')
        );
    }
    
    public static function delete_group($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        return $wpdb->delete($table, array('id' => $id), array('%d'));
    }
    
    public static function add_slide($group_id, $title, $description, $image_url, $link_url = '', $order = 0) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slides';
        
        return $wpdb->insert(
            $table,
            array(
                'group_id' => $group_id,
                'title' => sanitize_text_field($title),
                'description' => sanitize_textarea_field($description),
                'image_url' => esc_url_raw($image_url),
                'link_url' => esc_url_raw($link_url),
                'slide_order' => intval($order)
            ),
            array('%d', '%s', '%s', '%s', '%s', '%d')
        );
    }
    
    public static function get_slides($group_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slides';
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table WHERE group_id = %d AND is_active = 1 ORDER BY slide_order, id", 
            $group_id
        ));
    }
    
    public static function update_slide($id, $title, $description, $image_url, $link_url = '', $order = 0) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slides';
        
        return $wpdb->update(
            $table,
            array(
                'title' => sanitize_text_field($title),
                'description' => sanitize_textarea_field($description),
                'image_url' => esc_url_raw($image_url),
                'link_url' => esc_url_raw($link_url),
                'slide_order' => intval($order)
            ),
            array('id' => $id),
            array('%s', '%s', '%s', '%s', '%d'),
            array('%d')
        );
    }
    
    public static function delete_slide($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slides';
        return $wpdb->delete($table, array('id' => $id), array('%d'));
    }
    
    public static function get_groups_by_tag($tag) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table WHERE tags LIKE %s", 
            '%' . $wpdb->esc_like($tag) . '%'
        ));
    }
}