<?php
/**
 * List of demos json
 *
 * @package Blogzee Pro
 * @since 1.0.0
 */
$demos_array = array(
    'blogzee-free-one' => [
        'name' => 'Default',
        'type' => 'free',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-free-one.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/05/blogzee-free-one.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-free-one',
        'menu_array' => [
            'menu-1' => 'Header Menu'
        ],
        'home_slug' => '',
        'blog_slug' => '',
        'plugins' => [
            'contact-form-7' => [
                'name'  =>  'Contact Form 7',
                'source'    =>  'wordpress',
                'file_path' =>  'contact-form-7/contact-form-7.php'
            ]
        ],
        'tags' => [
            'blog'  =>  esc_html__( 'Blog', 'blogzee' )
        ]
    ],
    'blogzee-free-two' => [
        'name' => 'Blog Pro',
        'type' => 'free',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-free-two.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/05/blogzee-free-two.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-free-two/',
        'menu_array' => [
            'menu-1' => 'header-menu'
        ],
        'home_slug' => '',
        'blog_slug' => '',
        'plugins' => [
            'contact-form-7' => [
                'name'  =>  'Contact Form 7',
                'source'    =>  'wordpress',
                'file_path' =>  'contact-form-7/contact-form-7.php'
            ]
        ],
        'tags' => [
            'blog'  =>  esc_html__( 'Blog', 'blogzee' )
        ]
    ],
    'blogzee-free-three' => [
        'name' => 'Blog',
        'type' => 'free',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-free-three.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/07/blogzee-free.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-free-three/',
        'menu_array' => [
            'menu-1' => ''
        ],
        'home_slug' => 'nekit-home',
        'blog_slug' => '',
        'plugins' => [
            'elementor' => array(
                'name' => 'Elementor',
                'source' => 'wordpress',
                'file_path' => 'elementor/elementor.php'
            ),
            'news-kit-elementor-addons' => array(
                'name' => 'News Kit Elementor Addons',
                'source' => 'wordpress',
                'file_path' => 'news-kit-elementor-addons/news-kit-elementor-addons.php'
            )
        ],
        'pagebuilder' => array(
            'elementor' => 'Elementor'
        ),
        'tags' => [
            'free'  =>  esc_html__( 'Free', 'blogzee' ),
            'blog'  =>  esc_html__( 'Blog', 'blogzee' ),
            'elementor'  =>  esc_html__( 'Elementor', 'blogzee' )
        ]
    ],
    'blogzee-free-four' => [
        'name' => 'Classic',
        'type' => 'free',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-free-four.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/07/blogzee-free-1.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-free-four/',
        'menu_array' => [
            'menu-1' => ''
        ],
        'home_slug' => 'nekit-home',
        'blog_slug' => '',
        'plugins' => [
            'elementor' => array(
                'name' => 'Elementor',
                'source' => 'wordpress',
                'file_path' => 'elementor/elementor.php'
            ),
            'news-kit-elementor-addons' => array(
                'name' => 'News Kit Elementor Addons',
                'source' => 'wordpress',
                'file_path' => 'news-kit-elementor-addons/news-kit-elementor-addons.php'
            )
        ],
        'pagebuilder' => array(
            'elementor' => 'Elementor'
        ),
        'tags' => [
            'free'  =>  esc_html__( 'Free', 'blogzee' ),
            'blog'  =>  esc_html__( 'Blog', 'blogzee' ),
            'elementor'  =>  esc_html__( 'Elementor', 'blogzee' )
        ]
    ],
    'blogzee-pro-one' => [
        'name' => 'Magazine',
        'type' => 'pro',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-pro-one.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/05/blogzee-pro-one.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-pro-one/',
        'menu_array' => [
            'menu-1' => 'Header Menu',
            'menu-2' => 'Bottom Menu',
        ],
        'home_slug' => '',
        'blog_slug' => '',
        'plugins' => [
            'contact-form-7' => [
                'name'  =>  'Contact Form 7',
                'source'    =>  'wordpress',
                'file_path' =>  'contact-form-7/contact-form-7.php'
            ]
        ],
        'tags' => [
            'pro'  =>  esc_html__( 'Pro', 'blogzee' ),
            'blog'  =>  esc_html__( 'Blog', 'blogzee' ),
            'magazine'  =>  esc_html__( 'Magazine', 'blogzee' )
        ]
    ],
    'blogzee-pro-two' => [
        'name' => 'Magazine',
        'type' => 'pro',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-pro-two.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/05/blogzee-pro-two.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-pro-two/',
        'menu_array' => [
            'menu-1' => 'Header Menu'
        ],
        'home_slug' => '',
        'blog_slug' => '',
        'plugins' => [
            'contact-form-7' => [
                'name'  =>  'Contact Form 7',
                'source'    =>  'wordpress',
                'file_path' =>  'contact-form-7/contact-form-7.php'
            ]
        ],
        'tags' => [
            'pro'  =>  esc_html__( 'Pro', 'blogzee' ),
            'blog'  =>  esc_html__( 'Blog', 'blogzee' ),
            'magazine'  =>  esc_html__( 'Magazine', 'blogzee' )
        ]
    ],
    'blogzee-pro-three' => [
        'name' => 'Classic',
        'type' => 'pro',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-pro-three.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/05/blogzee-pro-three.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-pro-three/',
        'menu_array' => [
            'menu-1' => 'header-menu'
        ],
        'home_slug' => '',
        'blog_slug' => '',
        'plugins' => [
             'contact-form-7' => [
                'name'  =>  'Contact Form 7',
                'source'    =>  'wordpress',
                'file_path' =>  'contact-form-7/contact-form-7.php'
            ]
        ],
        'tags' => [
            'pro'  =>  esc_html__( 'Pro', 'blogzee' ),
            'blog'  =>  esc_html__( 'Blog', 'blogzee' ),
            'classic'  =>  esc_html__( 'Classic', 'blogzee' )
        ]
    ],
    'blogzee-pro-four' => [
        'name' => 'Personal Blog',
        'type' => 'pro',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-pro-four.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/07/blogzee-pro.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-pro-four/',
        'menu_array' => [
            'menu-1' => ''
        ],
        'home_slug' => 'nekit-home',
        'blog_slug' => '',
        'plugins' => [
            'elementor' => array(
                'name' => 'Elementor',
                'source' => 'wordpress',
                'file_path' => 'elementor/elementor.php'
            ),
            'news-kit-elementor-addons' => array(
                'name' => 'News Kit Elementor Addons',
                'source' => 'wordpress',
                'file_path' => 'news-kit-elementor-addons/news-kit-elementor-addons.php'
            )
        ],
        'pagebuilder' => array(
            'elementor' => 'Elementor'
        ),
        'tags' => [
            'pro'  =>  esc_html__( 'Pro', 'blogzee' ),
            'news'  =>  esc_html__( 'News', 'blogzee' ),
            'elementor'  =>  esc_html__( 'Elementor', 'blogzee' )
        ]
    ],
    'blogzee-pro-five' => [
        'name' => 'Lifestyle Blog',
        'type' => 'pro',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-pro-five.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/07/blogzee-pro-1.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-pro-five/',
        'menu_array' => [
            'menu-1' => ''
        ],
        'home_slug' => 'nekit-home',
        'blog_slug' => '',
        'plugins' => [
            'elementor' => array(
                'name' => 'Elementor',
                'source' => 'wordpress',
                'file_path' => 'elementor/elementor.php'
            ),
            'news-kit-elementor-addons' => array(
                'name' => 'News Kit Elementor Addons',
                'source' => 'wordpress',
                'file_path' => 'news-kit-elementor-addons/news-kit-elementor-addons.php'
            )
        ],
        'pagebuilder' => array(
            'elementor' => 'Elementor'
        ),
        'tags' => [
            'pro'  =>  esc_html__( 'Pro', 'blogzee' ),
            'news'  =>  esc_html__( 'News', 'blogzee' ),
            'elementor'  =>  esc_html__( 'Elementor', 'blogzee' )
        ]
    ],
    'blogzee-pro-six' => [
        'name' => 'SEO Blog',
        'type' => 'pro',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-pro-six.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/07/blogzee.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-pro-six/',
        'menu_array' => [
            'menu-1' => ''
        ],
        'home_slug' => 'nekit-home',
        'blog_slug' => '',
        'plugins' => [
            'elementor' => array(
                'name' => 'Elementor',
                'source' => 'wordpress',
                'file_path' => 'elementor/elementor.php'
            ),
            'news-kit-elementor-addons' => array(
                'name' => 'News Kit Elementor Addons',
                'source' => 'wordpress',
                'file_path' => 'news-kit-elementor-addons/news-kit-elementor-addons.php'
            )
        ],
        'pagebuilder' => array(
            'elementor' => 'Elementor'
        ),
        'tags' => [
            'pro'  =>  esc_html__( 'Pro', 'blogzee' ),
            'news'  =>  esc_html__( 'News', 'blogzee' ),
            'elementor'  =>  esc_html__( 'Elementor', 'blogzee' )
        ]
    ],
    'blogzee-pro-seven' => [
        'name' => 'Fitness Blog',
        'type' => 'pro',
        'buy_url'=> 'https://blazethemes.com/theme/blogzee-pro/',
        'external_url' => 'https://preview.blazethemes.com/import-files/blogzee/blogzee-pro-seven.zip',
        'image' => 'https://blazethemes.com/wp-content/uploads/2025/07/blog.jpg',
        'preview_url' => 'https://preview.blazethemes.com/blogzee-pro-seven/',
        'menu_array' => [
            'menu-1' => ''
        ],
        'home_slug' => 'nekit-home',
        'blog_slug' => '',
        'plugins' => [
            'elementor' => array(
                'name' => 'Elementor',
                'source' => 'wordpress',
                'file_path' => 'elementor/elementor.php'
            ),
            'news-kit-elementor-addons' => array(
                'name' => 'News Kit Elementor Addons',
                'source' => 'wordpress',
                'file_path' => 'news-kit-elementor-addons/news-kit-elementor-addons.php'
            )
        ],
        'pagebuilder' => array(
            'elementor' => 'Elementor'
        ),
        'tags' => [
            'pro'  =>  esc_html__( 'Pro', 'blogzee' ),
            'news'  =>  esc_html__( 'News', 'blogzee' ),
            'elementor'  =>  esc_html__( 'Elementor', 'blogzee' )
        ]
    ]
);
return apply_filters( 'blogzee__demos_array_filter', $demos_array );