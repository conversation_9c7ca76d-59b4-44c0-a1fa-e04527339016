/* preloader effect */
body .blogzee_loading_box {
    position: fixed;
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999;
    background: var(--blogzee-site-preloader-bk-color);
    top: 0;
    left: 0;
}

   .blogzee_loading_box {
       text-align: center;
   }

   /** loading one **/
    .blogzee_loading_box.preloader-style--one .box {
        width: 30px;
        height: 30px;
        position: absolute;
        border: 4px solid var(--blogzee-global-preset-theme-color);
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        animation: loader 2s infinite ease;
    }

    .blogzee_loading_box.preloader-style--one .box .one {
        vertical-align: top;
        display: inline-block;
        width: 100%;
        background-color: var(--blogzee-global-preset-theme-color);
        animation: loader-inner 2s infinite ease-in;
    }

    @keyframes loader {
        0% {
            transform: rotate(0deg);
        }
          
        25% {
            transform: rotate(180deg);
        }
        
        50% {
            transform: rotate(180deg);
        }
        
        75% {
            transform: rotate(360deg);
        }
        
        100% {
            transform: rotate(360deg);
        }
    }

    @keyframes loader-inner {
        0% {
            height: 0%;
        }
        
        25% {
            height: 0%;
        }
        
        50% {
            height: 100%;
        }
        
        75% {
            height: 100%;
        }
        
        100% {
            height: 0%;
        }
      }

/** Loader two **/
    .blogzee_loading_box.preloader-style--two .box .two {
        position: absolute;
        animation: circle-spin 1s linear infinite;
        width: 55.00000000000001px;
        height: 55.00000000000001px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
        box-shadow: 0 4px 0 0 var(--blogzee-global-preset-theme-color);
        transform-origin: 27.500000000000004px 28.6px;
    }

    @keyframes circle-spin {
        0% {
            transform: rotate(0deg);
        }
        50% {
            transform: rotate(180deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

/** loader three **/

    .blogzee_loading_box.preloader-style--three .box {
      position: relative;
      padding-top: 100px;
      width: 40px;
      margin: auto;
    }
    .blogzee_loading_box.preloader-style--three .box > div {
      position: absolute;
      width: 38px;
      height: 38px;
      opacity: 0;
      transform: rotate(225deg);
      animation-iteration-count: infinite;
      animation-name: orbit;
      animation-duration: 5.5s;
    }
    .blogzee_loading_box.preloader-style--three .box > div:after {
      content: '';
      position: absolute;
      width: 5px;
      height: 5px;
      border-radius: 5px;
      background: var(--blogzee-global-preset-theme-color);
    }
    .blogzee_loading_box.preloader-style--three .box .two {
      animation-delay: 240ms;
    }
    .blogzee_loading_box.preloader-style--three .box .three {
      animation-delay: 480ms;
    }
    .blogzee_loading_box.preloader-style--three .box .four {
      animation-delay: 720ms;
    }
    .blogzee_loading_box.preloader-style--three .box .five {
      animation-delay: 960ms;
    }
    @keyframes orbit {
      0% {
        transform: rotate(225deg);
        opacity: 1;
        animation-timing-function: ease-out;
      }
      7% {
        transform: rotate(345deg);
        animation-timing-function: linear;
      }
      30% {
        transform: rotate(455deg);
        animation-timing-function: ease-in-out;
      }
      39% {
        transform: rotate(690deg);
        animation-timing-function: linear;
      }
      70% {
        transform: rotate(815deg);
        opacity: 1;
        animation-timing-function: ease-out;
      }
      75% {
        transform: rotate(945deg);
        animation-timing-function: ease-out;
      }
      76% {
        transform: rotate(945deg);
        opacity: 0;
      }
      100% {
        transform: rotate(945deg);
        opacity: 0;
      }
    }

    /** loader four **/
        .blogzee_loading_box.preloader-style--four .box {
            width: 250px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .blogzee_loading_box.preloader-style--four .box div {
            width: 33px;
            height: 33px;
            position: relative;
            display: block;
            transform-origin: -50% center;
            border-radius: 15%;
        }

        .blogzee_loading_box.preloader-style--four .box div:after {
            content: "";
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            right: 0;
            background-color: var(--blogzee-global-preset-theme-color);
            border-radius: 15%;
            box-shadow: 0px 0px 10px 0px var(--blogzee-global-preset-theme-color);
        }

        .blogzee_loading_box.preloader-style--four .box div:nth-child(1) {
            -webkit-animation: slide 1.5s ease-in-out infinite alternate;
            animation: slide 1.5s ease-in-out infinite alternate;        
        }

        @keyframes slide {
            0% {
                transform: translatex(0vw);
            }
            100% {
                transform: translatex(208.75px);
            }
        }

        .blogzee_loading_box.preloader-style--four .box div:nth-child(2) {
            -webkit-animation: flip-1 1.5s ease-in-out infinite alternate;
            animation: flip-1 1.5s ease-in-out infinite alternate;
        }

        @keyframes flip-1 {
            0%, 15% {
                transform: rotate(0);
            }
            35%, 100% {
                transform: rotate(-180deg);
            }
        }

        .blogzee_loading_box.preloader-style--four .box div:nth-child(2):after {
            -webkit-animation: squidge-1 1.5s ease-in-out infinite alternate;
            animation: squidge-1 1.5s ease-in-out infinite alternate;
        }

        @keyframes squidge-1 {
            5% {
                transform-origin: center bottom;
                transform: scalex(1) scaley(1);
            }
            
            15% {
                transform-origin: center bottom;
                transform: scalex(1.3) scaley(0.7);
            }
            25%, 20% {
                transform-origin: center bottom;
                transform: scalex(0.8) scaley(1.4);
            }
            55%, 100% {
                transform-origin: center top;
                transform: scalex(1) scaley(1);
            }
            40% {
                transform-origin: center top;
                transform: scalex(1.3) scaley(0.7);
            }
        }

        .blogzee_loading_box.preloader-style--four .box div:nth-child(3) {
            -webkit-animation: flip-2 1.5s ease-in-out infinite alternate;
            animation: flip-2 1.5s ease-in-out infinite alternate;
        }

        @keyframes flip-2 {
            0%, 30% {
                transform: rotate(0);
            }
            
            50%, 100% {
                transform: rotate(-180deg);
            }
        }

        .blogzee_loading_box.preloader-style--four .box div:nth-child(3):after {
            -webkit-animation: squidge-2 1.5s ease-in-out infinite alternate;
            animation: squidge-2 1.5s ease-in-out infinite alternate;
        }

        @keyframes squidge-2 {
            20% {
                transform-origin: center bottom;
                transform: scalex(1) scaley(1);
            }
            
            30% {
                transform-origin: center bottom;
                transform: scalex(1.3) scaley(0.7);
            }
            40%, 35% {
                transform-origin: center bottom;
                transform: scalex(0.8) scaley(1.4);
            }
            70%, 100% {
                transform-origin: center top;
                transform: scalex(1) scaley(1);
            }
            55% {
                transform-origin: center top;
                transform: scalex(1.3) scaley(0.7);
            }
        }

        .blogzee_loading_box.preloader-style--four .box div:nth-child(4) {
            -webkit-animation: flip-3 1.5s ease-in-out infinite alternate;
            animation: flip-3 1.5s ease-in-out infinite alternate;
        }

        @keyframes flip-3 {
            0%, 45% {
                transform: rotate(0);
            }
            65%, 100% {
                transform: rotate(-180deg);
            }
        }

        .blogzee_loading_box.preloader-style--four .box div:nth-child(4):after {
            -webkit-animation: squidge-3 1.5s ease-in-out infinite alternate;
            animation: squidge-3 1.5s ease-in-out infinite alternate;
        }

        @keyframes squidge-3 {
            35% {
                transform-origin: center bottom;
                transform: scalex(1) scaley(1);
            }
            
            45% {
                transform-origin: center bottom;
                transform: scalex(1.3) scaley(0.7);
            }
            55%, 50% {
                transform-origin: center bottom;
                transform: scalex(0.8) scaley(1.4);
            }
            85%, 100% {
                transform-origin: center top;
                transform: scalex(1) scaley(1);
            }
            70% {
                transform-origin: center top;
                transform: scalex(1.3) scaley(0.7);
            }
        }

        .blogzee_loading_box.preloader-style--four .box div:nth-child(5) {
            -webkit-animation: flip-4 1.5s ease-in-out infinite alternate;
            animation: flip-4 1.5s ease-in-out infinite alternate;
        }

        @keyframes flip-4 {
            0%, 60% {
                transform: rotate(0);
            }
            
            80%, 100% {
                transform: rotate(-180deg);
            }
        }

        .blogzee_loading_box.preloader-style--four .box div:nth-child(5):after {
            -webkit-animation: squidge-4 1.5s ease-in-out infinite alternate;
            animation: squidge-4 1.5s ease-in-out infinite alternate;
        }

        @keyframes squidge-4 {
            50% {
                transform-origin: center bottom;
                transform: scalex(1) scaley(1);
            }
            
            60% {
                transform-origin: center bottom;
                transform: scalex(1.3) scaley(0.7);
            }
            70%, 65% {
                transform-origin: center bottom;
                transform: scalex(0.8) scaley(1.4);
            }
            100%, 100% {
                transform-origin: center top;
                transform: scalex(1) scaley(1);
            }
            85% {
                transform-origin: center top;
                transform: scalex(1.3) scaley(0.7);
            }
        }

    /** loader five **/
        .blogzee_loading_box.preloader-style--five .box div {
            position: absolute;
            width: 100px;
            height: 100px;
            animation: orbit-2 3s linear infinite;
        }

        @keyframes orbit-2 {
            0%{
                transform: rotate(0deg);
               }
            100%{
                transform: rotate(360deg);
            }
        }

        .blogzee_loading_box.preloader-style--five .box div:before {
            content: '\f005';
            position: absolute;
            font-family: 'Font Awesome 6 Free';
            color: #fff;
            font-weight: 400;
        }

        .blogzee_loading_box.preloader-style--five .box div:nth-child(1):before {
            top: 2px;
            left: 50%;
            transform: translateX(-50%);
        }

        .blogzee_loading_box.preloader-style--five .box div:nth-child(2):before {
            top: 35px;
            right: 7px;
        }

        .blogzee_loading_box.preloader-style--five .box div:nth-child(3):before {
            top: 68px;
            left: 50%;
            transform: translateX(-50%);
        }

        .blogzee_loading_box.preloader-style--five .box div:nth-child(4):before {
            top: 35px;
            left: 7px;
        }

        .blogzee_loading_box.preloader-style--five .box div:nth-child(5):before {
            display: none;
        }