=== Charity Zone ===
Contributors: TheMagnifico52
Requires at least: 5.0
Tested up to: 6.2
Requires PHP: 5.6
Stable tag: 0.6.4
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Tags: custom-logo, one-column, two-columns, wide-blocks, custom-background, custom-colors, custom-header, custom-menu, block-styles, sticky-post, footer-widgets, editor-style, featured-images, full-width-template, threaded-comments, theme-options, rtl-language-support, left-sidebar, right-sidebar, grid-layout, blog, e-commerce, education

Charity is a theme for designing websites for NGO, non profit, volunteer, fundraising, church, conservation, trust, foundation, donation, welfare activities, campaigns, activism, change in society, community support, corona, covid, donate, fundraise, green, kids, ngo, nonprofit, organization, old age, foster home, blood donation camps and related activities.

== Description ==

Charity is a theme for designing websites for NGO, non profit, volunteer, fundraising, church, conservation, trust, foundation, donation, welfare activities, campaigns, activism, change in society, community support, corona, gree movement, covid, donate, disaster relief, Peace and Human Rights NGOs, Competition, Auction, Art Exhibit, Concert, Sporting Event gala, animal shelters, aid, financial relief, funding, handouts, Child Sponsorship Organizations, disastor releif funds, hunger index, pandemic relief, eco nature, plasma donation camp, auction, a-thon events, fundraiser, green, kids, ngo, voluntary organization, charitable institution, fund, cause, nonprofit, organization, old age, foster home, Helping Hands, Crowdfunding & Fundraising Organizations blood donation camps and related activities. This multipurpose theme is also suitable for any business websites, digital agency, consultancy, corporate business, International Development NGOs, Humanitarian NGOs, Conservation NGOs. If you are looking to create a website for collecting funds and spread your message then this theme is perfect for you. It is user – friendly with lot of options for customizations like it includes a custom menu with Call to Action Button, Donation Button, advanced full-width slider, community section, Stats counter, Sponsors Section, Custom Header, Donation Section, Banner with Call to Action Button (CTA), left sidebar and social media. It also has the testimonial section for users to add comments. It is SEO friendly with optimized and clean codes so that it ranks easily on search engines like Google. It is also mobile responsive and based on a bootstrap framework which makes it very handy and easy to use.

== Installation ==

1. In your admin panel, go to Appearance -> Themes and click the 'Add New' button.
2. Type in Charity Zone in the search form and press the 'Enter' key on your keyboard.
3. Click on the 'Activate' button to use your new theme right away.
4. Navigate to Appearance > Customize in your admin panel and customize to taste.

== Copyright ==

Charity Zone WordPress Theme, Copyright 2022 TheMagnifico52
Charity Zone is distributed under the terms of the GNU GPL

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 2 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

== Frequently Asked Questions ==

= Does this theme support any plugins? =

Charity Zone includes support for Infinite Scroll in Jetpack.

== Credits ==

Charity Zone bundles the following third-party resources:

Bootstrap, Copyright (c) 2011-2019 Twitter, Inc.
**License:** MIT
Source: https://github.com/twbs/bootstrap

Font Awesome Free 5.6.3 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)

PSR-4 autoloader
 * Justin Tadlock
 * License: https://www.gnu.org/licenses/gpl-2.0.html GPL-2.0-or-later
 * Source: https://github.com/WPTRT/autoload

CustomizeSectionButton
 * Justin Tadlock
 * Copyright 2019, Justin Tadlock.
 * License: https://www.gnu.org/licenses/gpl-2.0.html GPL-2.0-or-later
 * https://github.com/WPTRT/customize-section-button

Bootstrap
 * Bootstrap v4.3.1 (https://getbootstrap.com/)
 * Copyright 2011-2019 The Bootstrap Authors
 * Copyright 2011-2019 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)

OwlCarousel
 * Copyright 2013-2018 David Deutsch
 * https://github.com/OwlCarousel2/OwlCarousel2
 * https://github.com/OwlCarousel2/OwlCarousel2/blob/develop/LICENSE

Webfont-Loader
 * License: https://github.com/WPTT/webfont-loader/blob/master/LICENSE
 * Source: https://github.com/WPTT/webfont-loader

Google Web Fonts
  *	Licenses: Lato is a sans serif typeface family, Licensed under Open Font License
  *	Source: https://fonts.google.com/specimen/Lato

* Pxhere Images,
	License: CC0 1.0 Universal (CC0 1.0)
	Source: https://pxhere.com/en/license

	Screenshot Image
	License: CC0 1.0 Universal (CC0 1.0)
	Source: https://pxhere.com/en/photo/1367013

	Screenshot Image, Copyright rawpixel.com
	License: CC0 1.0 Universal (CC0 1.0)
	Source: https://pxhere.com/en/photo/1451437

	Screenshot Image, Copyright mohamed hassan
	License: CC0 1.0 Universal (CC0 1.0)
	Source: https://pxhere.com/en/photo/1577079

== Changelog ==

= 0.1

* Initial release

= 0.2

* Removed jquery.js.
* Changed custom-header.php code.
* Removed other icons from screenshot and used fontawesome icon.

= 0.3

* Resolved uncought errors.

= 0.3.1

* Removed jquery.js file.
* Added jquery as a dependency

= 0.3.2

* Fixed some CSS issues.
* Added RTL tag.

= 0.3.3

* Fixed theme issues.
* Added required WP points.

= 0.3.4

* Added Wide Align and Block Editor Styles tags.
* Changed CSS.
* Added Full Width Template file.

= 0.3.5

* Added footer widget, editer style and sticky post tags.
* Removed unused CSS.

= 0.3.6

* Fixed escaping issues.
* Updated RTL file.

= 0.3.7

* Removed customize.js file.
* Removed extra code from customizer.php file.
* Removed header text color.

= 0.3.8

* Added the Scroll Back Functionality.

= 0.3.9

* Bugs Fixed.

= 0.4

* Added theme color option.

= 0.4.1

* Added new tags.
* Resolved themes check issues.

= 0.4.2

* Changes in tags.
* Resolved CSS.
* Updated RTL file.

= 0.4.3

* Removed unused functions.

= 0.4.4

* Resolved themescheck error.

= 0.4.5

* Added Sticky Header.

= 0.4.6

* Added Preloader Settings.

= 0.4.7

* Added Get Started.

= 0.4.8

* Solve Minor Bug.

= 0.4.9

* Updated Theme Color.

= 0.5

* Added Filter For Shop Page.
* Added CSS Classes.
* Updated PHP Version.
* Fixes minor bugs.

= 0.5.1

* Updated customiser settings.
* Updated footer link.

= 0.5.2

* Resolved issue.

= 0.5.3

* Updated icon settings.

= 0.5.4

* Updated rtl support.
* Updated PHP version.

= 0.5.5

* Updated sticky header.
* Added classes in global color.
* Updated flexible header.
* Resolved console error.

= 0.5.6

* Updated CSS.

= 0.5.7

* Added web-font file.
* Added google font license.
* Added require atleast.
* Updated PHP version.
* Prefixing the variable.
* Changed flatly to Bootstrap.
* Escaping URLs.

= 0.5.8

* Resolve header issue.

= 0.5.9

* Resolve minor error.
* Added site title tagline settings.
* Removed preloader settings.
* Added scroll to Top condtion.

= 0.6.0

* Resolve responsive error.
* Added Phone link.
* Added some hover effects.

= 0.6.1

* Added preloader on off setting.
* Added logo resize setting.

= 0.6.2

* Updated customizer.

= 0.6.3

* Added footer css.
* Added product per page setting.
* Added product per row setting.

= 0.6.4

* Added prloader color setting.
* Added slider on off setting.
* Added about us section on off setting.
* Added services section on off setting.