// Dakoii Slideshow Frontend JavaScript
(function($) {
    'use strict';
    
    // Global slideshow management object
    window.DakoiiSlideshows = {};
    
    // Initialize all slideshows when document is ready
    $(document).ready(function() {
        initializeSlideshows();
    });
    
    function initializeSlideshows() {
        $('.dakoii-slideshow').each(function() {
            var slideshowId = $(this).attr('id');
            var autoplay = $(this).data('autoplay') !== false;
            var duration = parseInt($(this).data('duration')) || 5000;
            
            if (slideshowId) {
                window.DakoiiSlideshows[slideshowId] = new DakoiiSlideshow(slideshowId, {
                    autoplay: autoplay,
                    duration: duration
                });
            }
        });
    }
    
    // Slideshow class constructor
    function DakoiiSlideshow(containerId, options) {
        this.container = $('#' + containerId);
        this.slides = this.container.find('.slide');
        this.currentSlide = 0;
        this.totalSlides = this.slides.length;
        this.autoplay = options.autoplay || true;
        this.duration = options.duration || 5000;
        this.timer = null;
        this.isPaused = false;
        
        this.init();
    }
    
    // Initialize slideshow
    DakoiiSlideshow.prototype.init = function() {
        if (this.totalSlides <= 1) {
            return; // No need for slideshow functionality with single slide
        }
        
        this.setupEventListeners();
        this.startAutoplay();
        this.updateDots();
    };
    
    // Set up event listeners
    DakoiiSlideshow.prototype.setupEventListeners = function() {
        var self = this;
        
        // Pause on hover
        this.container.hover(
            function() { self.pauseAutoplay(); },
            function() { self.resumeAutoplay(); }
        );
        
        // Keyboard navigation
        $(document).keydown(function(e) {
            if (self.container.is(':hover')) {
                if (e.keyCode === 37) { // Left arrow
                    e.preventDefault();
                    self.prevSlide();
                } else if (e.keyCode === 39) { // Right arrow
                    e.preventDefault();
                    self.nextSlide();
                }
            }
        });
        
        // Touch events for mobile swipe
        var startX = 0;
        var endX = 0;
        
        this.container.on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });
        
        this.container.on('touchend', function(e) {
            endX = e.originalEvent.changedTouches[0].clientX;
            handleSwipe();
        });
        
        function handleSwipe() {
            var threshold = 50; // Minimum swipe distance
            var diff = startX - endX;
            
            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    self.nextSlide(); // Swipe left - next slide
                } else {
                    self.prevSlide(); // Swipe right - previous slide
                }
            }
        }
    };
    
    // Go to specific slide
    DakoiiSlideshow.prototype.goToSlide = function(slideIndex) {
        if (slideIndex < 0 || slideIndex >= this.totalSlides) {
            return;
        }
        
        // Remove active class from current slide
        this.slides.eq(this.currentSlide).removeClass('active');
        
        // Update current slide index
        this.currentSlide = slideIndex;
        
        // Add active class to new slide
        this.slides.eq(this.currentSlide).addClass('active');
        
        // Update dots
        this.updateDots();
        
        // Reset autoplay timer
        this.resetAutoplay();
    };
    
    // Go to next slide
    DakoiiSlideshow.prototype.nextSlide = function() {
        var nextIndex = (this.currentSlide + 1) % this.totalSlides;
        this.goToSlide(nextIndex);
    };
    
    // Go to previous slide
    DakoiiSlideshow.prototype.prevSlide = function() {
        var prevIndex = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
        this.goToSlide(prevIndex);
    };
    
    // Update dot indicators
    DakoiiSlideshow.prototype.updateDots = function() {
        var dots = this.container.find('.dot');
        dots.removeClass('active');
        dots.eq(this.currentSlide).addClass('active');
    };
    
    // Start autoplay
    DakoiiSlideshow.prototype.startAutoplay = function() {
        if (!this.autoplay || this.totalSlides <= 1) {
            return;
        }
        
        var self = this;
        this.timer = setInterval(function() {
            if (!self.isPaused) {
                self.nextSlide();
            }
        }, this.duration);
    };
    
    // Stop autoplay
    DakoiiSlideshow.prototype.stopAutoplay = function() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    };
    
    // Pause autoplay
    DakoiiSlideshow.prototype.pauseAutoplay = function() {
        this.isPaused = true;
    };
    
    // Resume autoplay
    DakoiiSlideshow.prototype.resumeAutoplay = function() {
        this.isPaused = false;
    };
    
    // Reset autoplay timer
    DakoiiSlideshow.prototype.resetAutoplay = function() {
        this.stopAutoplay();
        this.startAutoplay();
    };
    
    // Public API functions for external control
    window.dakoiiSlideshowNext = function(slideshowId) {
        if (window.DakoiiSlideshows[slideshowId]) {
            window.DakoiiSlideshows[slideshowId].nextSlide();
        }
    };
    
    window.dakoiiSlideshowPrev = function(slideshowId) {
        if (window.DakoiiSlideshows[slideshowId]) {
            window.DakoiiSlideshows[slideshowId].prevSlide();
        }
    };
    
    window.dakoiiSlideshowGoTo = function(slideshowId, slideIndex) {
        if (window.DakoiiSlideshows[slideshowId]) {
            window.DakoiiSlideshows[slideshowId].goToSlide(slideIndex);
        }
    };
    
    window.dakoiiSlideshowPause = function(slideshowId) {
        if (window.DakoiiSlideshows[slideshowId]) {
            window.DakoiiSlideshows[slideshowId].stopAutoplay();
        }
    };
    
    window.dakoiiSlideshowPlay = function(slideshowId) {
        if (window.DakoiiSlideshows[slideshowId]) {
            window.DakoiiSlideshows[slideshowId].startAutoplay();
        }
    };
    
    // Handle AJAX-loaded content
    $(document).on('DOMNodeInserted', function(e) {
        if ($(e.target).hasClass('dakoii-slideshow') || $(e.target).find('.dakoii-slideshow').length > 0) {
            setTimeout(initializeSlideshows, 100);
        }
    });
    
    // Reinitialize on window resize (for responsive adjustments)
    $(window).resize(function() {
        // Debounce resize events
        clearTimeout(window.dakoiiResizeTimer);
        window.dakoiiResizeTimer = setTimeout(function() {
            // Re-calculate slideshow dimensions if needed
            $('.dakoii-slideshow').each(function() {
                var slideshowId = $(this).attr('id');
                if (window.DakoiiSlideshows[slideshowId]) {
                    // Trigger any responsive adjustments here
                    window.DakoiiSlideshows[slideshowId].updateDots();
                }
            });
        }, 250);
    });
    
})(jQuery);