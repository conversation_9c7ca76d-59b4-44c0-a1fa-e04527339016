<?php
/**
 * Provincial Administration Frontend Class
 * 
 * Handles frontend functionality and template functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_Frontend {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_head', array($this, 'add_custom_css_variables'));
        add_filter('the_content', array($this, 'enhance_single_event_content'));
        add_filter('the_content', array($this, 'enhance_single_news_content'));
    }
    
    /**
     * Add custom CSS variables to head
     */
    public function add_custom_css_variables() {
        ?>
        <style>
        :root {
            --png-red: #CE1126;
            --png-green: #006A4E;
            --png-yellow: #FFD700;
            --dark-green: #004d3a;
            --light-green: #00a86b;
            --cream: #FFF8DC;
            --dark-brown: #8B4513;
            --official-blue: #1e3a8a;
            --light-gray: #f8fafc;
            --medium-gray: #64748b;
        }
        </style>
        <?php
    }
    
    /**
     * Get governor data
     */
    public static function get_governor() {
        $governors = get_posts(array(
            'post_type' => 'esp_governor',
            'numberposts' => 1,
            'post_status' => 'publish'
        ));
        
        if (empty($governors)) {
            return null;
        }
        
        $governor = $governors[0];
        
        return array(
            'id' => $governor->ID,
            'name' => $governor->post_title,
            'message' => $governor->post_content,
            'title' => get_post_meta($governor->ID, '_esp_governor_title', true),
            'party' => get_post_meta($governor->ID, '_esp_governor_party', true),
            'email' => get_post_meta($governor->ID, '_esp_governor_email', true),
            'phone' => get_post_meta($governor->ID, '_esp_governor_phone', true),
            'photo' => get_the_post_thumbnail_url($governor->ID, 'medium')
        );
    }
    
    /**
     * Get MPs data
     */
    public static function get_mps($limit = -1) {
        $mps = get_posts(array(
            'post_type' => 'esp_mp',
            'numberposts' => $limit,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        
        $mps_data = array();
        
        foreach ($mps as $mp) {
            $district_id = get_post_meta($mp->ID, '_esp_mp_district_id', true);
            $district_name = '';
            if ($district_id) {
                $district = get_post($district_id);
                if ($district && $district->post_status === 'publish') {
                    $district_name = $district->post_title;
                }
            }

            $mps_data[] = array(
                'id' => $mp->ID,
                'name' => $mp->post_title,
                'bio' => get_post_meta($mp->ID, '_esp_mp_message', true), // Bio is now from meta field
                'electorate' => get_post_meta($mp->ID, '_esp_mp_electorate', true),
                'party' => get_post_meta($mp->ID, '_esp_mp_party', true),
                'district_id' => $district_id,
                'district_name' => $district_name,
                'message' => $mp->post_content, // Message is now from post content
                'photo' => get_the_post_thumbnail_url($mp->ID, 'thumbnail')
            );
        }
        
        return $mps_data;
    }
    
    /**
     * Get districts data
     */
    public static function get_districts($limit = -1) {
        $districts = get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => $limit,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        
        $districts_data = array();
        
        foreach ($districts as $district) {
            $districts_data[] = array(
                'id' => $district->ID,
                'name' => $district->post_title,
                'description' => $district->post_content,
                'llgs' => get_post_meta($district->ID, '_esp_district_llgs', true),
                'wards' => get_post_meta($district->ID, '_esp_district_wards', true),
                'population' => get_post_meta($district->ID, '_esp_district_population', true),
                'area' => get_post_meta($district->ID, '_esp_district_area', true),
                'photo' => get_the_post_thumbnail_url($district->ID, 'medium')
            );
        }
        
        return $districts_data;
    }
    
    /**
     * Get provincial statistics
     */
    public static function get_provincial_statistics() {
        return get_option('esp_provincial_statistics', array());
    }
    
    /**
     * Get administrative structure
     */
    public static function get_administrative_structure() {
        return get_option('esp_administrative_structure', array());
    }
    
    /**
     * Get contact information
     */
    public static function get_contact_information() {
        return get_option('esp_contact_information', array());
    }
    
    /**
     * Get events
     */
    public static function get_events($limit = 5, $upcoming_only = true) {
        $args = array(
            'post_type' => 'esp_event',
            'numberposts' => $limit,
            'post_status' => 'publish',
            'orderby' => 'meta_value',
            'meta_key' => '_esp_event_start_date',
            'order' => 'ASC'
        );

        if ($upcoming_only) {
            $args['meta_query'] = array(
                array(
                    'key' => '_esp_event_start_date',
                    'value' => date('Y-m-d'),
                    'compare' => '>='
                )
            );
        }

        $events = get_posts($args);

        // Filter events by district access for district users
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            $user_type = Provincial_User_Roles::get_user_provincial_type($user_id);

            // District users see only their assigned districts' events
            if ($user_type === 'district' && !current_user_can('manage_options')) {
                $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user_id);

                $events = array_filter($events, function($event) use ($assigned_districts) {
                    $district_id = get_post_meta($event->ID, '_esp_district_id', true);
                    // Only show events that are specifically linked to assigned districts
                    return !empty($district_id) && in_array($district_id, $assigned_districts);
                });
            }
        }

        $events_data = array();

        foreach ($events as $event) {
            $events_data[] = array(
                'id' => $event->ID,
                'title' => $event->post_title,
                'description' => $event->post_content,
                'start_date' => get_post_meta($event->ID, '_esp_event_start_date', true),
                'end_date' => get_post_meta($event->ID, '_esp_event_end_date', true),
                'location' => get_post_meta($event->ID, '_esp_event_location', true),
                'contact' => get_post_meta($event->ID, '_esp_event_contact', true),
                'photo' => get_the_post_thumbnail_url($event->ID, 'medium')
            );
        }

        return $events_data;
    }
    
    /**
     * Get news
     */
    public static function get_news($limit = 5, $featured_only = false) {
        $args = array(
            'post_type' => 'esp_news',
            'numberposts' => $limit,
            'post_status' => 'publish',
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        if ($featured_only) {
            $args['meta_query'] = array(
                array(
                    'key' => '_esp_news_featured',
                    'value' => '1',
                    'compare' => '='
                )
            );
        }
        
        $news = get_posts($args);
        $news_data = array();
        
        foreach ($news as $news_item) {
            $news_data[] = array(
                'id' => $news_item->ID,
                'title' => $news_item->post_title,
                'content' => $news_item->post_content,
                'excerpt' => $news_item->post_excerpt,
                'date' => get_post_meta($news_item->ID, '_esp_news_date', true) ?: get_the_date('Y-m-d', $news_item),
                'source' => get_post_meta($news_item->ID, '_esp_news_source', true),
                'featured' => get_post_meta($news_item->ID, '_esp_news_featured', true),
                'photo' => get_the_post_thumbnail_url($news_item->ID, 'medium')
            );
        }
        
        return $news_data;
    }
    
    /**
     * Format date for display
     */
    public static function format_date($date, $format = 'F j, Y') {
        if (empty($date)) {
            return '';
        }
        
        return date($format, strtotime($date));
    }
    
    /**
     * Format date range for events
     */
    public static function format_date_range($start_date, $end_date = '') {
        if (empty($start_date)) {
            return '';
        }
        
        $formatted_start = self::format_date($start_date);
        
        if (!empty($end_date) && $end_date !== $start_date) {
            $formatted_end = self::format_date($end_date);
            return $formatted_start . ' - ' . $formatted_end;
        }
        
        return $formatted_start;
    }
    
    /**
     * Get placeholder image URL
     */
    public static function get_placeholder_image($type = 'person') {
        $placeholders = array(
            'person' => '👤',
            'building' => '🏛️',
            'map' => '🗺️',
            'event' => '🎉',
            'news' => '📰'
        );
        
        return isset($placeholders[$type]) ? $placeholders[$type] : '📷';
    }
    
    /**
     * Truncate text
     */
    public static function truncate_text($text, $length = 150, $suffix = '...') {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length) . $suffix;
    }
    
    /**
     * Check if plugin has data
     */
    public static function has_data() {
        $governor = self::get_governor();
        $mps = self::get_mps(1);
        $districts = self::get_districts(1);

        return !empty($governor) || !empty($mps) || !empty($districts);
    }

    /**
     * Enhance single event content
     */
    public function enhance_single_event_content($content) {
        if (!is_singular('esp_event') || !in_the_loop() || !is_main_query()) {
            return $content;
        }

        global $post;

        $start_date = get_post_meta($post->ID, '_esp_event_start_date', true);
        $end_date = get_post_meta($post->ID, '_esp_event_end_date', true);
        $location = get_post_meta($post->ID, '_esp_event_location', true);
        $contact = get_post_meta($post->ID, '_esp_event_contact', true);

        // Check if we should show our own image (only if theme doesn't show featured images)
        $show_our_image = !current_theme_supports('post-thumbnails') ||
                         apply_filters('esp_show_event_image_in_content', false);

        ob_start();
        ?>
        <?php if ($show_our_image && has_post_thumbnail()): ?>
            <div class="esp-single-event-image-wrapper">
                <div class="esp-single-event-image">
                    <?php the_post_thumbnail('large'); ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="esp-single-event-meta">
            <?php if ($start_date): ?>
                <div class="esp-single-event-date">
                    <span class="esp-meta-icon">📅</span>
                    <strong><?php _e('Date:', 'esp-admin-manager'); ?></strong>
                    <?php
                    $formatted_start = date('F j, Y', strtotime($start_date));
                    if ($end_date && $end_date !== $start_date) {
                        $formatted_end = date('F j, Y', strtotime($end_date));
                        echo esc_html($formatted_start . ' - ' . $formatted_end);
                    } else {
                        echo esc_html($formatted_start);
                    }
                    ?>
                </div>
            <?php endif; ?>

            <?php if ($location): ?>
                <div class="esp-single-event-location">
                    <span class="esp-meta-icon">📍</span>
                    <strong><?php _e('Location:', 'esp-admin-manager'); ?></strong>
                    <?php echo esc_html($location); ?>
                </div>
            <?php endif; ?>

            <?php if ($contact): ?>
                <div class="esp-single-event-contact">
                    <span class="esp-meta-icon">📞</span>
                    <strong><?php _e('Contact:', 'esp-admin-manager'); ?></strong>
                    <?php echo esc_html($contact); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="esp-single-event-content">
            <?php echo $content; ?>
        </div>

        <div class="esp-single-event-footer">
            <a href="javascript:history.back()" class="esp-back-button">
                ← <?php _e('Back to Events', 'esp-admin-manager'); ?>
            </a>
        </div>
        <?php

        return ob_get_clean();
    }
    
    /**
     * Get admin structure as formatted HTML
     */
    public static function get_admin_structure_html() {
        $structure = self::get_administrative_structure();
        
        if (empty($structure)) {
            return '';
        }
        
        $html = '<div class="esp-admin-structure">';
        
        if (isset($structure['government_sectors'])) {
            $html .= '<div class="esp-structure-section">';
            $html .= '<h3>' . __('Government Sectors', 'esp-admin-manager') . '</h3>';
            $html .= wp_kses_post($structure['government_sectors']);
            $html .= '</div>';
        }
        
        if (isset($structure['administrative_divisions'])) {
            $html .= '<div class="esp-structure-section">';
            $html .= '<h3>' . __('Administrative Divisions', 'esp-admin-manager') . '</h3>';
            $html .= wp_kses_post($structure['administrative_divisions']);
            $html .= '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Enhance single news content
     */
    public function enhance_single_news_content($content) {
        if (!is_singular('esp_news') || !in_the_loop() || !is_main_query()) {
            return $content;
        }

        global $post;

        $news_date = get_post_meta($post->ID, '_esp_news_date', true);
        $source = get_post_meta($post->ID, '_esp_news_source', true);
        $featured = get_post_meta($post->ID, '_esp_news_featured', true);

        // Check if we should show our own image (only if theme doesn't show featured images)
        $show_our_image = !current_theme_supports('post-thumbnails') ||
                         apply_filters('esp_show_news_image_in_content', false);

        ob_start();
        ?>
        <?php if ($show_our_image && has_post_thumbnail()): ?>
            <div class="esp-single-news-image-wrapper">
                <div class="esp-single-news-image">
                    <?php the_post_thumbnail('large'); ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="esp-single-news-meta">
            <div class="esp-single-news-date">
                <span class="esp-meta-icon">📅</span>
                <strong><?php _e('Published:', 'esp-admin-manager'); ?></strong>
                <?php
                if ($news_date) {
                    echo esc_html(date('F j, Y', strtotime($news_date)));
                } else {
                    echo esc_html(get_the_date('F j, Y'));
                }
                ?>
            </div>

            <?php if ($source): ?>
                <div class="esp-single-news-source">
                    <span class="esp-meta-icon">📢</span>
                    <strong><?php _e('Source:', 'esp-admin-manager'); ?></strong>
                    <?php echo esc_html($source); ?>
                </div>
            <?php endif; ?>

            <?php if ($featured): ?>
                <div class="esp-single-news-featured">
                    <span class="esp-meta-icon">⭐</span>
                    <strong><?php _e('Featured Article', 'esp-admin-manager'); ?></strong>
                </div>
            <?php endif; ?>
        </div>

        <div class="esp-single-news-content">
            <?php echo $content; ?>
        </div>

        <div class="esp-single-news-footer">
            <a href="javascript:history.back()" class="esp-back-button">
                ← <?php _e('Back to News', 'esp-admin-manager'); ?>
            </a>
        </div>
        <?php

        return ob_get_clean();
    }
}
