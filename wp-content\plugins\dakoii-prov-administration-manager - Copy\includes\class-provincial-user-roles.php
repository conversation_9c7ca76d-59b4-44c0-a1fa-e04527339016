<?php
/**
 * Provincial User Roles Management Class
 * 
 * Handles creation and management of custom user roles for Provincial Administration Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_User_Roles {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Initialize hooks
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize
     */
    public function init() {
        // Update existing user capabilities if needed
        add_action('admin_init', array($this, 'maybe_update_user_capabilities'));
    }

    /**
     * Update existing user capabilities if needed
     */
    public function maybe_update_user_capabilities() {
        // Check if we need to update capabilities
        $capabilities_updated = get_option('esp_user_capabilities_updated_v2', false);

        if (!$capabilities_updated) {
            $this->update_existing_user_capabilities();
            update_option('esp_user_capabilities_updated_v2', true);
        }
    }

    /**
     * Update existing user capabilities
     */
    private function update_existing_user_capabilities() {
        // Update district user role
        $district_role = get_role('district_user');
        if ($district_role) {
            $district_capabilities = $this->get_district_user_capabilities();
            foreach ($district_capabilities as $cap => $grant) {
                $district_role->add_cap($cap, $grant);
            }
        }

        // Update provincial user role
        $provincial_role = get_role('provincial_user');
        if ($provincial_role) {
            $provincial_capabilities = $this->get_provincial_user_capabilities();
            foreach ($provincial_capabilities as $cap => $grant) {
                $provincial_role->add_cap($cap, $grant);
            }
        }
    }
    
    /**
     * Create custom user roles on plugin activation
     */
    public function create_roles() {
        // Remove existing roles first to ensure clean setup
        $this->remove_roles();
        
        // Create Provincial User role
        add_role(
            'provincial_user',
            __('Provincial User', 'esp-admin-manager'),
            $this->get_provincial_user_capabilities()
        );
        
        // Create District User role
        add_role(
            'district_user',
            __('District User', 'esp-admin-manager'),
            $this->get_district_user_capabilities()
        );
        
        // Add capabilities to administrator role
        $this->add_admin_capabilities();
    }
    
    /**
     * Remove custom user roles on plugin deactivation
     */
    public function remove_roles() {
        remove_role('provincial_user');
        remove_role('district_user');
        
        // Remove capabilities from administrator role
        $this->remove_admin_capabilities();
    }
    
    /**
     * Get capabilities for Provincial User role
     */
    private function get_provincial_user_capabilities() {
        return array(
            // Basic WordPress capabilities
            'read' => true,
            
            // Provincial Administration capabilities
            'view_provincial_admin' => true,
            'manage_provincial_events' => true,
            'manage_district_events' => true,
            'manage_provincial_news' => true,
            'manage_district_news' => true,
            'manage_provincial_contacts' => true,
            'manage_district_contacts' => true,
            'manage_provincial_statistics' => true,
            'manage_district_statistics' => true,
            'manage_provincial_info' => true,
            'manage_district_info' => true,
            'manage_governor_info' => true,
            'manage_district_mps' => true,
            'manage_provincial_mps' => true,
            'manage_provincial_slideshows' => true,
            'manage_district_slideshows' => true,
            
            // Post type capabilities for ESP content
            'edit_esp_events' => true,
            'edit_others_esp_events' => true,
            'publish_esp_events' => true,
            'read_private_esp_events' => true,
            'delete_esp_events' => true,
            'delete_others_esp_events' => true,
            'edit_esp_news' => true,
            'edit_others_esp_news' => true,
            'publish_esp_news' => true,
            'read_private_esp_news' => true,
            'delete_esp_news' => true,
            'delete_others_esp_news' => true,
            'edit_esp_districts' => true,
            'edit_others_esp_districts' => true,
            'publish_esp_districts' => true,
            'read_private_esp_districts' => true,
            'edit_esp_mps' => true,
            'edit_others_esp_mps' => true,
            'publish_esp_mps' => true,
            'read_private_esp_mps' => true,
            'edit_esp_governor' => true,
            'edit_others_esp_governor' => true,
            'publish_esp_governor' => true,
            'read_private_esp_governor' => true,
        );
    }
    
    /**
     * Get capabilities for District User role
     */
    private function get_district_user_capabilities() {
        return array(
            // Basic WordPress capabilities
            'read' => true,
            'edit_posts' => true,
            'publish_posts' => true,
            'delete_posts' => true,
            'edit_published_posts' => true,
            'delete_published_posts' => true,
            'upload_files' => true,

            // District Administration capabilities (limited to assigned districts)
            'view_district_admin' => true,
            'view_provincial_admin' => true, // Allow access to dashboard
            'manage_district_events' => true,
            'manage_district_news' => true,
            'manage_district_contacts' => true,
            'manage_district_info' => true,
            'manage_district_mps' => true,
            'manage_district_slideshows' => true,

            // Post type capabilities for ESP content (limited)
            'edit_esp_events' => true,
            'publish_esp_events' => true,
            'read_private_esp_events' => true,
            'delete_esp_events' => true,
            'edit_esp_news' => true,
            'publish_esp_news' => true,
            'read_private_esp_news' => true,
            'delete_esp_news' => true,
            'edit_esp_districts' => true,
            'read_private_esp_districts' => true,
            'edit_esp_mps' => true,
            'publish_esp_mps' => true,
            'read_private_esp_mps' => true,
        );
    }
    
    /**
     * Add capabilities to administrator role
     */
    private function add_admin_capabilities() {
        $admin_role = get_role('administrator');
        if ($admin_role) {
            $capabilities = array_merge(
                $this->get_provincial_user_capabilities(),
                array(
                    'manage_provincial_users' => true,
                    'manage_district_users' => true,
                    'assign_districts' => true,
                )
            );
            
            foreach ($capabilities as $cap => $grant) {
                $admin_role->add_cap($cap, $grant);
            }
        }
    }
    
    /**
     * Remove capabilities from administrator role
     */
    private function remove_admin_capabilities() {
        $admin_role = get_role('administrator');
        if ($admin_role) {
            $capabilities = array_merge(
                $this->get_provincial_user_capabilities(),
                $this->get_district_user_capabilities(),
                array(
                    'manage_provincial_users' => true,
                    'manage_district_users' => true,
                    'assign_districts' => true,
                )
            );
            
            foreach ($capabilities as $cap => $grant) {
                $admin_role->remove_cap($cap);
            }
        }
    }
    
    /**
     * Check if user has provincial access
     */
    public static function user_has_provincial_access($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return false;
        }
        
        return in_array('provincial_user', $user->roles) || in_array('administrator', $user->roles);
    }
    
    /**
     * Check if user has district access
     */
    public static function user_has_district_access($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return false;
        }
        
        return in_array('district_user', $user->roles) || 
               in_array('provincial_user', $user->roles) || 
               in_array('administrator', $user->roles);
    }
    
    /**
     * Get user's assigned districts
     */
    public static function get_user_assigned_districts($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $assigned_districts = get_user_meta($user_id, 'assigned_districts', true);
        return is_array($assigned_districts) ? $assigned_districts : array();
    }
    
    /**
     * Check if user can access specific district
     */
    public static function user_can_access_district($district_id, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        // Provincial users and administrators can access all districts
        if (self::user_has_provincial_access($user_id) || current_user_can('manage_options')) {
            return true;
        }
        
        // District users can only access assigned districts
        $assigned_districts = self::get_user_assigned_districts($user_id);
        return in_array($district_id, $assigned_districts);
    }
    
    /**
     * Get user's provincial type
     */
    public static function get_user_provincial_type($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return false;
        }
        
        if (in_array('administrator', $user->roles)) {
            return 'administrator';
        } elseif (in_array('provincial_user', $user->roles)) {
            return 'provincial';
        } elseif (in_array('district_user', $user->roles)) {
            return 'district';
        }
        
        return false;
    }
}
