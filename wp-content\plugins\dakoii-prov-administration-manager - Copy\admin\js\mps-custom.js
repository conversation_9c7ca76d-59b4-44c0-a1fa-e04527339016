/**
 * Provincial Administration Manager - Custom MPs CRUD JavaScript
 */

(function($) {
    'use strict';

    // Global variables
    let mediaUploader;
    let currentMPId = null;
    let isEditing = false;

    // Initialize when document is ready
    $(document).ready(function() {
        initializeEventHandlers();
    });

    /**
     * Initialize all event handlers
     */
    function initializeEventHandlers() {
        // Add new MP button
        $('#add-new-mp, #add-first-mp').on('click', function() {
            openMPModal();
        });

        // Edit MP buttons
        $(document).on('click', '.edit-mp', function() {
            const mpId = $(this).data('mp-id');
            editMP(mpId);
        });

        // Delete MP buttons
        $(document).on('click', '.delete-mp', function() {
            const mpId = $(this).data('mp-id');
            const mpName = $(this).data('mp-name');
            deleteMP(mpId, mpName);
        });

        // Modal close buttons
        $('#close-modal, .esp-modal-overlay').on('click', function() {
            closeMPModal();
        });

        // Cancel form button
        $('#cancel-form').on('click', function() {
            closeMPModal();
        });

        // Form submission
        $('#mp-form').on('submit', function(e) {
            e.preventDefault();
            saveMPForm();
        });

        // Photo upload button
        $('#upload-photo').on('click', function(e) {
            e.preventDefault();
            openMediaUploader();
        });

        // Remove photo button
        $('#remove-photo').on('click', function() {
            removePhoto();
        });

        // Prevent modal close when clicking inside modal content
        $('.esp-modal-content').on('click', function(e) {
            e.stopPropagation();
        });
    }

    /**
     * Open MP modal for adding new MP
     */
    function openMPModal() {
        resetForm();
        isEditing = false;
        currentMPId = null;
        
        $('#modal-title').text(window.espMPsData.strings.addMP);
        $('#save-text').text(window.espMPsData.strings.saveMP);
        $('input[name="action"]').val('esp_mp_create');
        
        $('#mp-form-modal').fadeIn(300);
        $('#mp-name').focus();
    }

    /**
     * Edit existing MP
     */
    function editMP(mpId) {
        isEditing = true;
        currentMPId = mpId;
        
        $('#modal-title').text(window.espMPsData.strings.editMP);
        $('#save-text').text(window.espMPsData.strings.updateMP);
        $('input[name="action"]').val('esp_mp_update');
        $('#mp-id').val(mpId);
        
        // Show loading state
        showLoadingState();
        
        // Get MP data via AJAX
        $.ajax({
            url: window.espMPsData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'esp_mp_get',
                mp_id: mpId,
                nonce: window.espMPsData.nonce
            },
            success: function(response) {
                hideLoadingState();
                
                if (response.success) {
                    populateForm(response.data);
                    $('#mp-form-modal').fadeIn(300);
                    $('#mp-name').focus();
                } else {
                    showNotification(response.data || window.espMPsData.strings.error, 'error');
                }
            },
            error: function() {
                hideLoadingState();
                showNotification(window.espMPsData.strings.error, 'error');
            }
        });
    }

    /**
     * Delete MP
     */
    function deleteMP(mpId, mpName) {
        if (!window.espMPsData.canDelete) {
            showNotification('You do not have permission to delete MPs.', 'error');
            return;
        }

        const confirmMessage = window.espMPsData.strings.confirmDelete + '\n\n' + mpName;
        
        if (!confirm(confirmMessage)) {
            return;
        }

        // Show loading state on the delete button
        const $deleteBtn = $(`.delete-mp[data-mp-id="${mpId}"]`);
        const originalText = $deleteBtn.html();
        $deleteBtn.html('<span class="esp-btn-icon">⏳</span>' + window.espMPsData.strings.deleting).prop('disabled', true);

        $.ajax({
            url: window.espMPsData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'esp_mp_delete',
                mp_id: mpId,
                nonce: window.espMPsData.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Remove the MP card with animation
                    $(`.esp-mp-card[data-mp-id="${mpId}"]`).fadeOut(300, function() {
                        $(this).remove();
                        
                        // Check if no MPs left
                        if ($('.esp-mp-card').length === 0) {
                            location.reload(); // Reload to show empty state
                        }
                    });
                    
                    showNotification(window.espMPsData.strings.success, 'success');
                } else {
                    $deleteBtn.html(originalText).prop('disabled', false);
                    showNotification(response.data || window.espMPsData.strings.error, 'error');
                }
            },
            error: function() {
                $deleteBtn.html(originalText).prop('disabled', false);
                showNotification(window.espMPsData.strings.error, 'error');
            }
        });
    }

    /**
     * Save MP form
     */
    function saveMPForm() {
        const $form = $('#mp-form');
        const $saveBtn = $('#save-mp');
        const originalText = $saveBtn.html();
        
        // Show loading state
        const loadingText = isEditing ? window.espMPsData.strings.updating : window.espMPsData.strings.saving;
        $saveBtn.html('<span class="esp-btn-icon">⏳</span>' + loadingText).prop('disabled', true);
        
        // Prepare form data
        const formData = new FormData($form[0]);
        
        $.ajax({
            url: window.espMPsData.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $saveBtn.html(originalText).prop('disabled', false);
                
                if (response.success) {
                    showNotification(window.espMPsData.strings.success, 'success');
                    closeMPModal();
                    
                    // Reload page to show updated data
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showNotification(response.data || window.espMPsData.strings.error, 'error');
                }
            },
            error: function() {
                $saveBtn.html(originalText).prop('disabled', false);
                showNotification(window.espMPsData.strings.error, 'error');
            }
        });
    }

    /**
     * Close MP modal
     */
    function closeMPModal() {
        $('#mp-form-modal').fadeOut(300);
        resetForm();
    }

    /**
     * Reset form to initial state
     */
    function resetForm() {
        $('#mp-form')[0].reset();
        $('#mp-id').val('');
        $('#photo-preview').hide();
        $('#mp-photo-id').val('');
        currentMPId = null;
        isEditing = false;
    }

    /**
     * Populate form with MP data
     */
    function populateForm(data) {
        $('#mp-name').val(data.name || '');
        $('#mp-electorate').val(data.electorate || '');
        $('#mp-party').val(data.party || '');
        $('#mp-district').val(data.district_id || '');
        $('#mp-message').val(data.message || '');
        $('#mp-bio').val(data.bio || '');
        
        if (data.photo_url) {
            $('#photo-image').attr('src', data.photo_url);
            $('#photo-preview').show();
            $('#mp-photo-id').val(data.photo_id || '');
        }
    }

    /**
     * Open WordPress media uploader
     */
    function openMediaUploader() {
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }

        mediaUploader = wp.media({
            title: 'Select MP Photo',
            button: {
                text: 'Use this photo'
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });

        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            
            $('#mp-photo-id').val(attachment.id);
            $('#photo-image').attr('src', attachment.url);
            $('#photo-preview').show();
        });

        mediaUploader.open();
    }

    /**
     * Remove photo
     */
    function removePhoto() {
        $('#mp-photo-id').val('');
        $('#photo-preview').hide();
    }

    /**
     * Show loading state
     */
    function showLoadingState() {
        // You can implement a global loading indicator here
    }

    /**
     * Hide loading state
     */
    function hideLoadingState() {
        // You can implement hiding global loading indicator here
    }

    /**
     * Show notification
     */
    function showNotification(message, type) {
        // Create notification element
        const $notification = $('<div class="esp-notification esp-notification-' + type + '">' + message + '</div>');
        
        // Add to page
        $('body').append($notification);
        
        // Show with animation
        $notification.fadeIn(300);
        
        // Auto hide after 3 seconds
        setTimeout(function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

})(jQuery);

// Add notification styles
jQuery(document).ready(function($) {
    if (!$('#esp-notification-styles').length) {
        $('head').append(`
            <style id="esp-notification-styles">
                .esp-notification {
                    position: fixed;
                    top: 32px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 6px;
                    color: white;
                    font-weight: 500;
                    z-index: 100001;
                    display: none;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                }
                .esp-notification-success {
                    background: #10b981;
                }
                .esp-notification-error {
                    background: #ef4444;
                }
                .esp-notification-warning {
                    background: #f59e0b;
                }
            </style>
        `);
    }
});
