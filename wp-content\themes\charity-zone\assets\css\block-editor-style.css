/*
Description: Used to style Gutenberg Blocks.
*/

[class^="wp-block-"]:not(.wp-block-gallery) figcaption {
  font-style: italic;
  margin-bottom: 1.5em;
  text-align: left;
  color: rgba(255, 255, 255, 0.70);
}
.rtl [class^="wp-block-"]:not(.wp-block-gallery) figcaption {
  text-align: right;
}

/*--------------------------------------------------------------
2.0 Blocks - Common Blocks
--------------------------------------------------------------*/

/* Paragraph */

p.has-drop-cap:not(:focus)::first-letter {
  font-size: 5em;
  margin-top: 0.075em;
}

/* Image */

.wp-block-image {
  margin-bottom: 1.5em;
}
.wp-block-image figure {
  margin-bottom: 0;
  margin-top: 0;
}
.wp-block-image figure.alignleft {
  margin-right: 1.5em;
}
.wp-block-image figure.alignright {
  margin-left: 1.5em;
}

/* Gallery */

.wp-block-gallery {
  margin-bottom: 1.5em;
}
.wp-block-gallery figcaption {
  font-style: italic;
}
.wp-block-gallery.aligncenter {
  display: flex;
  margin: 0 -8px;
}

/* Quote */

.wp-block-quote.alignleft p:last-of-type,
.wp-block-quote.alignright p:last-of-type {
  margin-bottom: 0;
}
.wp-block-quote cite {
  color: inherit;
  font-size: inherit;
}
.wp-block-quote,
.wp-block-quote:not(.is-large):not(.is-style-large),
.wp-block-pullquote {
  border-left: 4px solid #fe5722;
  padding: 1em;
  background-color: rgba(0, 0, 0, 0.03);
}
.wp-block-quote.is-large {
  padding: 1em 2em;
  border: none;
}
.wp-block-quote.is-large > p:before, 
.wp-block-quote.is-large > p:after {
  content: "''";
  font-size: 30px;
  display: inline-block;
  vertical-align: middle;
  margin: 0 10px;
}

/* Audio */

.wp-block-audio audio {
  display: block;
  width: 100%;
}

/* Cover */

.wp-block-cover-image.alignright,
.wp-block-cover.alignright,
.wp-block-cover-image.alignleft,
.wp-block-cover.alignleft,
.wp-block-cover-image.aligncenter,
.wp-block-cover.aligncenter {
  display: flex;
}
.no-sidebar .wp-block-embed.is-type-video.alignfull iframe {    
  width: 100vw !important;
  max-width : 100vw;
  margin-left  : calc( -100vw / 2 + 100% / 2 ) !important;
  margin-right : calc( -100vw / 2 + 100% / 2 ) !important;
}
.no-sidebar .wp-block-image.alignfull figcaption,
.no-sidebar .wp-block-audio.alignfull figcaption {
  text-align: center;
  max-width: 1200px;
  width: 90%;
  padding: 0 15px;
  margin: 5px auto 0;
}
.no-sidebar .wp-block-table.alignfull {
  display: table;
}

/* File */

.wp-block-file .wp-block-file__button {
  background-color: #222;
  -webkit-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #fff;
  display: inline-block;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: 800;
  margin-top: 2em;
  padding: 0.7em 2em;
  -webkit-transition: background-color 0.2s ease-in-out;
  transition: background-color 0.2s ease-in-out;
  white-space: nowrap;
}

.wp-block-file .wp-block-file__button:hover,
.wp-block-file .wp-block-file__button:focus {
  background-color: #767676;
  -webkit-box-shadow: none;
  box-shadow: none;
}

/* Separator */

.wp-block-separator {
  border-color: #eee;
  border: 0;
}
/*--------------------------------------------------------------
3.0 Blocks - Formatting
--------------------------------------------------------------*/

/* Code */

.wp-block-code {
  background: transparent;
  border: 0;
  padding: 0;
}

/* Pullquote */

.wp-block-pullquote {
  border: 0;
  border-top: 4px solid #fe5722;
  border-bottom: 4px solid #fe5722;
  padding: 1em;
}
.wp-block-pullquote__citation,
.wp-block-pullquote cite {
  font-size: inherit;
  text-transform: none;
  color: rgba(255, 255, 255, 0.70);
}

/* Table */

.wp-block-table thead th {
  border-bottom: 2px solid #bbb;
  padding-bottom: 0.5em;
}
.wp-block-table tr {
  border-bottom: 1px solid #eee;
}
.wp-block-table th,
.wp-block-table td {
  border-color: #ccc;
  padding: 15px 25px;
}
.rtl .wp-block-table th,
.rtl .wp-block-table td {
  text-align: right;
}

/*--------------------------------------------------------------
4.0 Blocks - Layout Elements
--------------------------------------------------------------*/

.wp-block-button__link {
  padding: 10px 20px;
    font-weight: bold;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
  background: #fe5722;
  border-radius: 0;
  color: #fff !important;
}
.wp-block-button .wp-block-button__link:hover,
.wp-block-button .wp-block-button__link:focus {
  background-color: #2c2b2b;
  color: #fff;
  -webkit-box-shadow: none;
  box-shadow: none;
}

/* Separator */

.wp-block-separator:not(.is-style-wide):not(.is-style-dots) {
  max-width: 100px;
}

/* Media & Text */

.wp-block-media-text {
  margin-bottom: 1.5em;
}
.wp-block-media-text *:last-child {
  margin-bottom: 0;
}

/*--------------------------------------------------------------
5.0 Blocks - Widgets
--------------------------------------------------------------*/

.wp-block-archives.aligncenter,
.wp-block-categories.aligncenter,
.wp-block-latest-posts.aligncenter,
.wp-block-categories.aligncenter ul {
  list-style-position: inside;
  text-align: center;
}

/* Comments */

.wp-block-latest-comments article {
  margin-bottom: 4em;
}
.blog:not(.has-sidebar) #primary .wp-block-latest-comments article,
.archive:not(.page-one-column):not(.has-sidebar) #primary .wp-block-latest-comments article,
.search:not(.has-sidebar) #primary .wp-block-latest-comments article {
  float: none;
  width: 100%;
}
.wp-block-latest-comments .avatar,
.wp-block-latest-comments__comment-avatar {
  border-radius: 0;
}
.wp-block-latest-comments a {
  -webkit-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 1);
  box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 1);

}
.wp-block-latest-comments__comment-meta {
  font-size: 16px;
  font-size: 1rem;
  margin-bottom: 0.4em;
}
.wp-block-latest-comments__comment-author,
.wp-block-latest-comments__comment-link {
  font-weight: 700;
  text-decoration: none;
}
.wp-block-latest-comments__comment-date {
  color: #767676;
  font-size: 10px;
  font-size: 0.625rem;
  font-weight: 800;
  letter-spacing: 0.1818em;
  margin-top: 0.4em;
  text-transform: uppercase;
}
.editor-block-list__block .wp-block-latest-comments__comment-excerpt p {
  font-size: 14px;
  font-size: 0.875rem;
}

/*--------------------------------------------------------------
5.0 Blocks - Color Settings
--------------------------------------------------------------*/

/* Background Color */

.has-tan-background-color {
  background-color: #E6DBAD;
}
.has-yellow-background-color {
  background-color: #FDE64B;
}
.has-orange-background-color {
  background-color: #ED7014;
}
.has-red-background-color {
  background-color: #D0312D;
}
.has-pink-background-color {
  background-color: #b565a7;
}
.has-purple-background-color {
  background-color: #A32CC4;
}
.has-blue-background-color {
  background-color: #3A43BA;
}
.has-green-background-color {
  background-color: #3BB143;
}
.has-brown-background-color {
  background-color: #231709;
}
.has-grey-background-color {
  background-color: #6C626D;
}
.has-black-background-color {
  background-color: #000000;
}

/* Text Color */

.has-tan-color {
  color: #E6DBAD;
}
.has-yellow-color {
  color: #FDE64B;
}
.has-orange-color {
  color: #ED7014;
}
.has-red-color {
  color: #D0312D;
}
.has-pink-color {
  color: #b565a7;
}
.has-purple-color {
  color: #A32CC4;
}
.has-blue-color {
  color: #3A43BA;
}
.has-green-color {
  color: #3BB143;
}
.has-brown-color {
  color: #231709;
}
.has-grey-color {
  color: #6C626D;
}
.has-black-color {
  color: #000000;
}

/*--------------------------------------------------------------
6.0 Blocks - Text Settings
--------------------------------------------------------------*/
.has-larger-font-size {
  font-size: 36px;
}
.has-huge-font-size {
  font-size: 48px;
}
.has-larger-font-size,
.has-huge-font-size {
  line-height: 1.5;
}

@media screen and (max-width: 992px) {
  .has-larger-font-size {
    font-size: 30px;
  }
  .has-huge-font-size {
    font-size: 38px;
  }
}
@media screen and (max-width: 767px) {
  .has-larger-font-size {
    font-size: 24px;
  }
  .has-huge-font-size {
    font-size: 28px;
  }
}
@media screen and (max-width: 567px) {
  .has-larger-font-size,
  .has-huge-font-size {
    font-size: 16px;
    line-height: 28px;
  }
}

.wp-block {
  max-width: 710px;
}
.editor-block-list__layout {
  max-width: 776px;
  margin-left: auto;
  margin-right: auto;
}

/* alignment */
.alignright {
  float: right;
}
.alignleft {
  float: left;
}
.aligncenter {
  text-align: center;
}
.wp-block-latest-comments {
  margin: 0;
  padding: 0;
}