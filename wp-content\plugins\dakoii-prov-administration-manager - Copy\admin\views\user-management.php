<?php
/**
 * Provincial Administration Manager - User Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get user management instance
$user_management = Provincial_User_Management::get_instance();
$users = $user_management->get_provincial_users();
$districts = $user_management->get_all_districts();
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('User Management', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage Provincial and District users, assign districts, and control access permissions', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <!-- Create New User Section -->
    <div class="esp-form-section">
        <h3><?php _e('Create New User', 'esp-admin-manager'); ?></h3>
        
        <form method="post" action="" class="esp-form">
            <?php wp_nonce_field('provincial_user_management', 'provincial_user_nonce'); ?>
            <input type="hidden" name="action" value="create_user">
            
            <div class="esp-form-row">
                <div class="esp-form-group">
                    <label for="username"><?php _e('Username', 'esp-admin-manager'); ?> *</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="esp-form-group">
                    <label for="email"><?php _e('Email', 'esp-admin-manager'); ?> *</label>
                    <input type="email" id="email" name="email" required>
                </div>
            </div>
            
            <div class="esp-form-row">
                <div class="esp-form-group">
                    <label for="first_name"><?php _e('First Name', 'esp-admin-manager'); ?></label>
                    <input type="text" id="first_name" name="first_name">
                </div>
                
                <div class="esp-form-group">
                    <label for="last_name"><?php _e('Last Name', 'esp-admin-manager'); ?></label>
                    <input type="text" id="last_name" name="last_name">
                </div>
            </div>
            
            <div class="esp-form-row">
                <div class="esp-form-group">
                    <label for="password"><?php _e('Password', 'esp-admin-manager'); ?> *</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <div class="esp-form-group">
                    <label for="user_role"><?php _e('User Role', 'esp-admin-manager'); ?> *</label>
                    <select id="user_role" name="user_role" required onchange="toggleDistrictAssignment()">
                        <option value=""><?php _e('Select Role', 'esp-admin-manager'); ?></option>
                        <option value="provincial_user"><?php _e('Provincial User', 'esp-admin-manager'); ?></option>
                        <option value="district_user"><?php _e('District User', 'esp-admin-manager'); ?></option>
                    </select>
                </div>
            </div>
            
            <div id="district_assignment" class="esp-form-group" style="display: none;">
                <label><?php _e('Assign Districts', 'esp-admin-manager'); ?></label>
                <div class="esp-checkbox-group">
                    <?php foreach ($districts as $district): ?>
                        <label class="esp-checkbox-label">
                            <input type="checkbox" name="assigned_districts[]" value="<?php echo $district->ID; ?>">
                            <?php echo esc_html($district->post_title); ?>
                        </label>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <div class="esp-form-actions">
                <button type="submit" class="esp-button"><?php _e('Create User', 'esp-admin-manager'); ?></button>
            </div>
        </form>
    </div>

    <!-- Existing Users Section -->
    <div class="esp-form-section">
        <h3><?php _e('Existing Users', 'esp-admin-manager'); ?></h3>
        
        <?php if (!empty($users)): ?>
        <table class="esp-list-table">
            <thead>
                <tr>
                    <th><?php _e('Name', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Username', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Email', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Role', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Assigned Districts', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users as $user): 
                    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user->ID);
                    $user_role = $user->roles[0] ?? '';
                ?>
                <tr>
                    <td><?php echo esc_html($user->display_name); ?></td>
                    <td><?php echo esc_html($user->user_login); ?></td>
                    <td><?php echo esc_html($user->user_email); ?></td>
                    <td>
                        <?php 
                        if ($user_role === 'provincial_user') {
                            echo __('Provincial User', 'esp-admin-manager');
                        } elseif ($user_role === 'district_user') {
                            echo __('District User', 'esp-admin-manager');
                        }
                        ?>
                    </td>
                    <td>
                        <?php if ($user_role === 'district_user' && !empty($assigned_districts)): ?>
                            <?php
                            $district_names = array();
                            foreach ($assigned_districts as $district_id) {
                                $district = get_post($district_id);
                                if ($district) {
                                    $district_names[] = $district->post_title;
                                }
                            }
                            echo esc_html(implode(', ', $district_names));
                            ?>
                        <?php elseif ($user_role === 'provincial_user'): ?>
                            <em><?php _e('All Districts', 'esp-admin-manager'); ?></em>
                        <?php else: ?>
                            <em><?php _e('None', 'esp-admin-manager'); ?></em>
                        <?php endif; ?>
                    </td>
                    <td>
                        <button type="button" class="esp-button secondary small" onclick="editUser(<?php echo $user->ID; ?>)">
                            <?php _e('Edit', 'esp-admin-manager'); ?>
                        </button>
                        <button type="button" class="esp-button danger small" onclick="deleteUser(<?php echo $user->ID; ?>)">
                            <?php _e('Delete', 'esp-admin-manager'); ?>
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <p><?php _e('No provincial or district users found.', 'esp-admin-manager'); ?></p>
        <?php endif; ?>
    </div>
</div>

<!-- Edit User Modal -->
<div id="edit-user-modal" class="esp-modal" style="display: none;">
    <div class="esp-modal-content">
        <div class="esp-modal-header">
            <h3><?php _e('Edit User Districts', 'esp-admin-manager'); ?></h3>
            <span class="esp-modal-close" onclick="closeEditModal()">&times;</span>
        </div>
        <div class="esp-modal-body">
            <form id="edit-user-form" method="post" action="">
                <?php wp_nonce_field('provincial_user_management', 'provincial_user_nonce'); ?>
                <input type="hidden" name="action" value="update_districts">
                <input type="hidden" id="edit_user_id" name="user_id" value="">
                
                <div class="esp-form-group">
                    <label><?php _e('Assign Districts', 'esp-admin-manager'); ?></label>
                    <div class="esp-checkbox-group" id="edit-districts">
                        <?php foreach ($districts as $district): ?>
                            <label class="esp-checkbox-label">
                                <input type="checkbox" name="assigned_districts[]" value="<?php echo $district->ID; ?>">
                                <?php echo esc_html($district->post_title); ?>
                            </label>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="esp-form-actions">
                    <button type="submit" class="esp-button"><?php _e('Update Districts', 'esp-admin-manager'); ?></button>
                    <button type="button" class="esp-button secondary" onclick="closeEditModal()"><?php _e('Cancel', 'esp-admin-manager'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleDistrictAssignment() {
    const roleSelect = document.getElementById('user_role');
    const districtAssignment = document.getElementById('district_assignment');
    
    if (roleSelect.value === 'district_user') {
        districtAssignment.style.display = 'block';
    } else {
        districtAssignment.style.display = 'none';
    }
}

function editUser(userId) {
    // Get user data via AJAX and populate modal
    const modal = document.getElementById('edit-user-modal');
    const userIdField = document.getElementById('edit_user_id');
    
    userIdField.value = userId;
    
    // Get current assignments and check appropriate checkboxes
    // This would typically be done via AJAX, but for simplicity, we'll show the modal
    modal.style.display = 'block';
}

function closeEditModal() {
    document.getElementById('edit-user-modal').style.display = 'none';
}

function deleteUser(userId) {
    if (confirm('<?php _e('Are you sure you want to delete this user?', 'esp-admin-manager'); ?>')) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'post';
        form.action = '';
        
        const nonceField = document.createElement('input');
        nonceField.type = 'hidden';
        nonceField.name = 'provincial_user_nonce';
        nonceField.value = '<?php echo wp_create_nonce('provincial_user_management'); ?>';
        
        const actionField = document.createElement('input');
        actionField.type = 'hidden';
        actionField.name = 'action';
        actionField.value = 'delete_user';
        
        const userIdField = document.createElement('input');
        userIdField.type = 'hidden';
        userIdField.name = 'user_id';
        userIdField.value = userId;
        
        form.appendChild(nonceField);
        form.appendChild(actionField);
        form.appendChild(userIdField);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('edit-user-modal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}
</script>
