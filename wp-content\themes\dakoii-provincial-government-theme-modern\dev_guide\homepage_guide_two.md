<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>East Sepik Provincial Administration - Papua New Guinea</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --png-red: #CE1126;
            --png-green: #006A4E;
            --png-yellow: #FFD700;
            --dark-green: #004d3a;
            --light-green: #00a86b;
            --cream: #FFF8DC;
            --dark-brown: #8B4513;
            --official-blue: #1e3a8a;
            --light-gray: #f8fafc;
            --medium-gray: #64748b;
        }

        body {
            font-family: 'Georgia', serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, var(--png-green) 0%, var(--dark-green) 100%);
            overflow-x: hidden;
        }



        /* Header */
        .site-header {
            background: linear-gradient(45deg, var(--png-red) 0%, var(--png-red) 50%, var(--png-green) 50%, var(--png-green) 100%);
            color: white;
            padding: 1rem 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
        }

        .site-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,20 Q50,5 80,20 Q65,50 80,80 Q50,65 20,80 Q35,50 20,20 Z" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="2"/></svg>') repeat;
            opacity: 0.3;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .gov-logo {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo-shield {
            width: 70px;
            height: 70px;
            background: var(--png-yellow);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: var(--png-red);
            border: 3px solid white;
            position: relative;
        }

        .logo-shield::before {
            content: '🏛️';
            position: absolute;
        }

        .gov-title {
            font-size: 2.2rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.2;
        }

        .gov-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            font-style: italic;
        }

        .emergency-contact {
            text-align: right;
            font-size: 0.9rem;
        }

        .emergency-number {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--png-yellow);
        }

        /* Navigation */
        .main-nav {
            background: rgba(0, 106, 78, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 3px solid var(--png-yellow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            padding: 1rem 0;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-item a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1.2rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .nav-item a:hover {
            background: var(--png-red);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(206, 17, 38, 0.4);
        }

        /* Slideshow Section */
        .slideshow-section {
            position: relative;
            height: 400px;
            overflow: hidden;
        }

        .slideshow-container {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .slide {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 1s ease-in-out;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide.active {
            opacity: 1;
        }

        .slide-1 {
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
                        linear-gradient(45deg, var(--png-green), var(--dark-green));
        }

        .slide-2 {
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
                        linear-gradient(135deg, var(--png-red), var(--png-yellow));
        }

        .slide-3 {
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
                        linear-gradient(90deg, var(--png-yellow), var(--png-green));
        }

        .slide-content {
            text-align: center;
            color: white;
            max-width: 800px;
            padding: 0 2rem;
        }

        .slide-title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
        }

        .slide-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
        }

        .slideshow-nav {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }

        .slide-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .slide-dot.active {
            background: var(--png-yellow);
            transform: scale(1.2);
        }

        /* Governor Section */
        .governor-section {
            padding: 4rem 0;
            background: white;
        }

        .section-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .governor-grid {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 3rem;
            align-items: center;
        }

        .governor-photo {
            width: 100%;
            height: 350px;
            background: linear-gradient(45deg, var(--png-green), var(--png-red));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }

        .governor-photo::before {
            content: '👤';
            position: absolute;
        }

        .governor-content h2 {
            font-size: 2.5rem;
            color: var(--png-green);
            margin-bottom: 0.5rem;
        }

        .governor-title {
            font-size: 1.2rem;
            color: var(--png-red);
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .governor-message {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            font-style: italic;
            border-left: 4px solid var(--png-yellow);
            padding-left: 1.5rem;
        }

        /* Parliament Members Section */
        .parliament-section {
            padding: 4rem 0;
            background: var(--light-gray);
        }

        .section-title {
            text-align: center;
            font-size: 2.8rem;
            color: var(--png-green);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.1rem;
            color: var(--medium-gray);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .mp-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .mp-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-top: 4px solid var(--png-red);
        }

        .mp-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .mp-photo {
            width: 80px;
            height: 80px;
            background: var(--png-green);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1rem;
        }

        .mp-name {
            font-size: 1.2rem;
            color: var(--png-green);
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .mp-electorate {
            color: var(--png-red);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .mp-party {
            color: var(--medium-gray);
            font-size: 0.9rem;
        }

        /* Provincial Map Section */
        .map-section {
            padding: 4rem 0;
            background: white;
        }

        .map-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
        }

        .map-visual {
            background: linear-gradient(135deg, var(--png-green), var(--dark-green));
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .map-visual::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 200"><path d="M50,50 Q100,30 150,50 Q200,70 250,50 Q200,100 150,120 Q100,140 50,120 Q30,80 50,50 Z" fill="none" stroke="rgba(255,215,0,0.3)" stroke-width="3"/><circle cx="100" cy="80" r="5" fill="rgba(255,215,0,0.6)"/><circle cx="200" cy="90" r="5" fill="rgba(255,215,0,0.6)"/><circle cx="150" cy="130" r="5" fill="rgba(255,215,0,0.6)"/></svg>') no-repeat center;
            opacity: 0.8;
        }

        .map-placeholder {
            background: rgba(255, 215, 0, 0.2);
            border: 3px dashed var(--png-yellow);
            border-radius: 15px;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }

        .map-info {
            color: var(--png-green);
        }

        .map-info h3 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }

        .map-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .map-stat {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid var(--png-yellow);
        }

        .map-stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--png-red);
        }

        .map-stat-label {
            color: var(--medium-gray);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Districts Section */
        .districts-section {
            padding: 4rem 0;
            background: var(--light-gray);
        }

        .districts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .district-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border-left: 5px solid var(--png-green);
            transition: all 0.3s ease;
        }

        .district-card:hover {
            transform: translateY(-5px);
            border-left-color: var(--png-red);
        }

        .district-name {
            font-size: 1.4rem;
            color: var(--png-green);
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .district-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .district-stat {
            text-align: center;
            padding: 0.8rem;
            background: var(--light-gray);
            border-radius: 8px;
        }

        .district-stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--png-red);
        }

        .district-stat-label {
            font-size: 0.8rem;
            color: var(--medium-gray);
            text-transform: uppercase;
        }

        .district-description {
            color: #666;
            line-height: 1.6;
        }

        /* Administration Structure */
        .admin-structure {
            padding: 4rem 0;
            background: white;
        }

        .structure-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
        }

        .structure-section h3 {
            font-size: 1.8rem;
            color: var(--png-green);
            margin-bottom: 1.5rem;
            border-bottom: 3px solid var(--png-yellow);
            padding-bottom: 0.5rem;
        }

        .structure-list {
            list-style: none;
        }

        .structure-list li {
            padding: 0.8rem 0;
            position: relative;
            padding-left: 2rem;
            border-bottom: 1px solid #eee;
            transition: all 0.3s ease;
        }

        .structure-list li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: var(--png-red);
            font-size: 0.8rem;
        }

        .structure-list li:hover {
            background: var(--light-gray);
            padding-left: 2.5rem;
        }

        /* Featured Events & News */
        .featured-section {
            padding: 4rem 0;
            background: var(--light-gray);
        }

        .featured-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
        }

        .featured-events,
        .featured-news {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .featured-title {
            font-size: 1.8rem;
            color: var(--png-green);
            margin-bottom: 1.5rem;
            text-align: center;
            border-bottom: 2px solid var(--png-yellow);
            padding-bottom: 0.5rem;
        }

        .event-item,
        .news-item {
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
        }

        .event-item:last-child,
        .news-item:last-child {
            border-bottom: none;
        }

        .event-date,
        .news-date {
            font-size: 0.9rem;
            color: var(--png-red);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .event-title,
        .news-title {
            font-size: 1.1rem;
            color: var(--png-green);
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .event-description,
        .news-excerpt {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        /* Contact Information */
        .contact-section {
            padding: 4rem 0;
            background: var(--png-green);
            color: white;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--png-yellow);
        }

        .contact-title {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--png-yellow);
        }

        .contact-info {
            line-height: 1.6;
            opacity: 0.9;
        }

        /* Footer */
        .site-footer {
            background: var(--dark-green);
            color: white;
            padding: 3rem 0 1rem;
            position: relative;
        }

        .site-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--png-red), var(--png-yellow), var(--png-green));
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: var(--png-yellow);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .footer-section p,
        .footer-section a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            line-height: 1.6;
            display: block;
            margin-bottom: 0.5rem;
        }

        .footer-section a:hover {
            color: var(--png-yellow);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0.8;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .mp-card,
        .district-card,
        .event-item,
        .news-item {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .governor-grid,
            .map-container,
            .structure-grid,
            .featured-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .mp-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .gov-title {
                font-size: 1.8rem;
            }

            .emergency-contact {
                text-align: center;
            }

            .nav-menu {
                gap: 0.5rem;
            }

            .nav-item a {
                font-size: 0.85rem;
                padding: 0.4rem 1rem;
            }

            .slide-title {
                font-size: 2rem;
            }

            .slide-subtitle {
                font-size: 1.1rem;
            }

            .section-title {
                font-size: 2.2rem;
            }

            .map-stats {
                grid-template-columns: 1fr;
            }

            .districts-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Accessibility */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--png-yellow);
            color: var(--dark-green);
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 1000;
        }

        .skip-link:focus {
            top: 6px;
        }

        /* Print styles */
        @media print {
            .slideshow-section,
            .nav-menu,
            .contact-section {
                display: none;
            }
            
            body {
                background: white;
                color: black;
            }
        }
    </style>
</head>
<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>



    <!-- Header -->
    <header class="site-header">
        <div class="header-content">
            <div class="gov-logo">
                <div class="logo-shield"></div>
                <div>
                    <h1 class="gov-title">East Sepik Provincial Administration</h1>
                    <p class="gov-subtitle">Papua New Guinea Government</p>
                </div>
            </div>
            <div class="emergency-contact">
                <div>Emergency Services</div>
                <div class="emergency-number">000</div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="main-nav">
        <div class="nav-container">
            <ul class="nav-menu">
                <li class="nav-item"><a href="#home">Home</a></li>
                <li class="nav-item"><a href="#governor">Governor</a></li>
                <li class="nav-item"><a href="#parliament">Parliament</a></li>
                <li class="nav-item"><a href="#administration">Administration</a></li>
                <li class="nav-item"><a href="#districts">Districts</a></li>
                <li class="nav-item"><a href="#services">Services</a></li>
                <li class="nav-item"><a href="#news">News</a></li>
                <li class="nav-item"><a href="#events">Events</a></li>
                <li class="nav-item"><a href="#contact">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Featured Images Slideshow -->
    <section class="slideshow-section" id="home">
        <div class="slideshow-container">
            <div class="slide slide-1 active">
                <div class="slide-content">
                    <h1 class="slide-title">Welcome to East Sepik Province</h1>
                    <p class="slide-subtitle">The Heart of Papua New Guinea's Cultural Heritage</p>
                </div>
            </div>
            <div class="slide slide-2">
                <div class="slide-content">
                    <h1 class="slide-title">Serving Our Communities</h1>
                    <p class="slide-subtitle">Committed to Development and Progress</p>
                </div>
            </div>
            <div class="slide slide-3">
                <div class="slide-content">
                    <h1 class="slide-title">Building a Better Future</h1>
                    <p class="slide-subtitle">Together We Grow Stronger</p>
                </div>
            </div>
        </div>
        <div class="slideshow-nav">
            <span class="slide-dot active" onclick="currentSlide(1)"></span>
            <span class="slide-dot" onclick="currentSlide(2)"></span>
            <span class="slide-dot" onclick="currentSlide(3)"></span>
        </div>
    </section>

    <!-- Governor Section -->
    <section class="governor-section" id="governor">
        <div class="section-container">
            <div class="governor-grid">
                <div class="governor-photo"></div>
                <div class="governor-content">
                    <h2>Hon. Allan Bird</h2>
                    <div class="governor-title">Governor of East Sepik Province</div>
                    <div class="governor-message">
                        "As we work together to build a prosperous East Sepik Province, I am committed to ensuring that our government serves every citizen with integrity, transparency, and dedication. Our rich cultural heritage and natural resources are the foundation upon which we will build a sustainable future for our children and generations to come. Through unity, hard work, and good governance, we will achieve our vision of a progressive and self-reliant East Sepik Province."
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Members of Parliament -->
    <section class="parliament-section" id="parliament">
        <div class="section-container">
            <h2 class="section-title">Members of Parliament</h2>
            <p class="section-subtitle">Representing the people of East Sepik Province in the National Parliament</p>
            
            <div class="mp-grid">
                <div class="mp-card">
                    <div class="mp-photo">👤</div>
                    <div class="mp-name">Hon. Allan Bird</div>
                    <div class="mp-electorate">East Sepik Provincial</div>
                    <div class="mp-party">Pangu Party</div>
                </div>
                
                <div class="mp-card">
                    <div class="mp-photo">👤</div>
                    <div class="mp-name">Hon. Gabriel Kapris</div>
                    <div class="mp-electorate">Wewak Open</div>
                    <div class="mp-party">PNC</div>
                </div>
                
                <div class="mp-card">
                    <div class="mp-photo">👤</div>
                    <div class="mp-name">Hon. Johnson Wamp</div>
                    <div class="mp-electorate">Angoram Open</div>
                    <div class="mp-party">NAP</div>
                </div>
                
                <div class="mp-card">
                    <div class="mp-photo">👤</div>
                    <div class="mp-name">Hon. Samson Knos</div>
                    <div class="mp-electorate">Maprik Open</div>
                    <div class="mp-party">PLP</div>
                </div>
                
                <div class="mp-card">
                    <div class="mp-photo">👤</div>
                    <div class="mp-name">Hon. Joseph Yopyyopy</div>
                    <div class="mp-electorate">Ambunti-Drekikier Open</div>
                    <div class="mp-party">PANGU</div>
                </div>
                
                <div class="mp-card">
                    <div class="mp-photo">👤</div>
                    <div class="mp-name">Hon. Bernard Hagoria</div>
                    <div class="mp-electorate">Yangoru-Saussia Open</div>
                    <div class="mp-party">URP</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Provincial Map & Statistics -->
    <section class="map-section" id="main-content">
        <div class="section-container">
            <h2 class="section-title">East Sepik Province Map & Statistics</h2>
            
            <div class="map-container">
                <div class="map-visual">
                    <div class="map-placeholder">
                        🗺️ East Sepik Province Map
                        <br><br>
                        Interactive Provincial Boundary Map
                        <br>
                        Districts • LLGs • Wards
                    </div>
                </div>
                
                <div class="map-info">
                    <h3>Provincial Overview</h3>
                    <div class="map-stats">
                        <div class="map-stat">
                            <div class="map-stat-number">450,530</div>
                            <div class="map-stat-label">Population</div>
                        </div>
                        <div class="map-stat">
                            <div class="map-stat-number">43,426</div>
                            <div class="map-stat-label">Area (km²)</div>
                        </div>
                        <div class="map-stat">
                            <div class="map-stat-number">6</div>
                            <div class="map-stat-label">Districts</div>
                        </div>
                        <div class="map-stat">
                            <div class="map-stat-number">41</div>
                            <div class="map-stat-label">LLGs</div>
                        </div>
                        <div class="map-stat">
                            <div class="map-stat-number">1,287</div>
                            <div class="map-stat-label">Wards</div>
                        </div>
                        <div class="map-stat">
                            <div class="map-stat-number">4</div>
                            <div class="map-stat-label">Urban LLGs</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Districts -->
    <section class="districts-section" id="districts">
        <div class="section-container">
            <h2 class="section-title">Districts of East Sepik Province</h2>
            <p class="section-subtitle">Six administrative districts serving our diverse communities</p>
            
            <div class="districts-grid">
                <div class="district-card">
                    <div class="district-name">Wewak District</div>
                    <div class="district-stats">
                        <div class="district-stat">
                            <div class="district-stat-number">8</div>
                            <div class="district-stat-label">LLGs</div>
                        </div>
                        <div class="district-stat">
                            <div class="district-stat-number">156</div>
                            <div class="district-stat-label">Wards</div>
                        </div>
                    </div>
                    <div class="district-description">
                        Provincial capital and administrative center. Home to the provincial government headquarters and major commercial activities.
                    </div>
                </div>
                
                <div class="district-card">
                    <div class="district-name">Maprik District</div>
                    <div class="district-stats">
                        <div class="district-stat">
                            <div class="district-stat-number">7</div>
                            <div class="district-stat-label">LLGs</div>
                        </div>
                        <div class="district-stat">
                            <div class="district-stat-number">198</div>
                            <div class="district-stat-label">Wards</div>
                        </div>
                    </div>
                    <div class="district-description">
                        Known for its rich cultural heritage and traditional art. Major agricultural area with significant yam and sweet potato production.
                    </div>
                </div>
                
                <div class="district-card">
                    <div class="district-name">Angoram District</div>
                    <div class="district-stats">
                        <div class="district-stat">
                            <div class="district-stat-number">6</div>
                            <div class="district-stat-label">LLGs</div>
                        </div>
                        <div class="district-stat">
                            <div class="district-stat-number">287</div>
                            <div class="district-stat-label">Wards</div>
                        </div>
                    </div>
                    <div class="district-description">
                        Located along the Sepik River. Famous for traditional crocodile initiation ceremonies and wood carving artistry.
                    </div>
                </div>
                
                <div class="district-card">
                    <div class="district-name">Ambunti-Drekikier District</div>
                    <div class="district-stats">
                        <div class="district-stat">
                            <div class="district-stat-number">7</div>
                            <div class="district-stat-label">LLGs</div>
                        </div>
                        <div class="district-stat">
                            <div class="district-stat-number">195</div>
                            <div class="district-stat-label">Wards</div>
                        </div>
                    </div>
                    <div class="district-description">
                        Remote district along the upper Sepik River. Rich in biodiversity and traditional customs of river communities.
                    </div>
                </div>
                
                <div class="district-card">
                    <div class="district-name">Yangoru-Saussia District</div>
                    <div class="district-stats">
                        <div class="district-stat">
                            <div class="district-stat-number">6</div>
                            <div class="district-stat-label">LLGs</div>
                        </div>
                        <div class="district-stat">
                            <div class="district-stat-number">234</div>
                            <div class="district-stat-label">Wards</div>
                        </div>
                    </div>
                    <div class="district-description">
                        Highland district known for its cool climate and coffee production. Important educational center with several schools.
                    </div>
                </div>
                
                <div class="district-card">
                    <div class="district-name">Dreikikir District</div>
                    <div class="district-stats">
                        <div class="district-stat">
                            <div class="district-stat-number">7</div>
                            <div class="district-stat-label">LLGs</div>
                        </div>
                        <div class="district-stat">
                            <div class="district-stat-number">217</div>
                            <div class="district-stat-label">Wards</div>
                        </div>
                    </div>
                    <div class="district-description">
                        Mountainous district with diverse ethnic groups. Important for mining activities and timber resources.
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Provincial Administration Structure -->
    <section class="admin-structure" id="administration">
        <div class="section-container">
            <h2 class="section-title">Provincial Administration</h2>
            <p class="section-subtitle">Organizational structure of East Sepik Provincial Government</p>
            
            <div class="structure-grid">
                <div class="structure-section">
                    <h3>Government Sectors</h3>
                    <ul class="structure-list">
                        <li>Health & Medical Services</li>
                        <li>Education & Training</li>
                        <li>Infrastructure & Transport</li>
                        <li>Agriculture & Livestock</li>
                        <li>Commerce & Industry</li>
                        <li>Tourism & Culture</li>
                        <li>Environment & Conservation</li>
                        <li>Community Development</li>
                        <li>Youth & Sports</li>
                        <li>Women & Family Affairs</li>
                        <li>Law & Justice</li>
                        <li>Finance & Treasury</li>
                    </ul>
                </div>
                
                <div class="structure-section">
                    <h3>Administrative Divisions</h3>
                    <ul class="structure-list">
                        <li>Office of the Governor</li>
                        <li>Provincial Administrator</li>
                        <li>Department of Finance</li>
                        <li>Department of Works</li>
                        <li>Department of Health</li>
                        <li>Department of Education</li>
                        <li>Department of Agriculture</li>
                        <li>Department of Commerce</li>
                        <li>Department of Lands</li>
                        <li>Department of Environment</li>
                        <li>Provincial Planning Office</li>
                        <li>Internal Audit Division</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Events & News -->
    <section class="featured-section" id="events">
        <div class="section-container">
            <h2 class="section-title">Featured Events & Latest News</h2>
            
            <div class="featured-grid">
                <div class="featured-events">
                    <h3 class="featured-title">🎉 Upcoming Events</h3>
                    
                    <div class="event-item">
                        <div class="event-date">September 16-18, 2025</div>
                        <div class="event-title">East Sepik Cultural Festival</div>
                        <div class="event-description">Annual celebration of traditional arts, music, and dance featuring all six districts. Venue: Wewak Town.</div>
                    </div>
                    
                    <div class="event-item">
                        <div class="event-date">September 25, 2025</div>
                        <div class="event-title">Provincial Assembly Meeting</div>
                        <div class="event-description">Quarterly assembly session to discuss budget allocations and development projects.</div>
                    </div>
                    
                    <div class="event-item">
                        <div class="event-date">October 5-7, 2025</div>
                        <div class="event-title">Agriculture & Trade Fair</div>
                        <div class="event-description">Showcasing local products, farming innovations, and business opportunities.</div>
                    </div>
                    
                    <div class="event-item">
                        <div class="event-date">October 15, 2025</div>
                        <div class="event-title">Youth Leadership Summit</div>
                        <div class="event-description">Empowering young leaders across all districts with training and networking opportunities.</div>
                    </div>
                </div>
                
                <div class="featured-news">
                    <h3 class="featured-title">📰 Latest News</h3>
                    
                    <div class="news-item">
                        <div class="news-date">August 19, 2025</div>
                        <div class="news-title">K850M Provincial Budget Approved</div>
                        <div class="news-excerpt">Provincial Assembly approves record budget with focus on infrastructure, health, and education development...</div>
                    </div>
                    
                    <div class="news-item">
                        <div class="news-date">August 18, 2025</div>
                        <div class="news-title">Wewak-Maprik Highway 65% Complete</div>
                        <div class="news-excerpt">Major road reconstruction project ahead of schedule, expected completion by December 2025...</div>
                    </div>
                    
                    <div class="news-item">
                        <div class="news-date">August 16, 2025</div>
                        <div class="news-title">New Health Centers Opened in Remote Areas</div>
                        <div class="news-excerpt">Three new aid posts established in Ambunti-Drekikier and Angoram districts improving healthcare access...</div>
                    </div>
                    
                    <div class="news-item">
                        <div class="news-date">August 15, 2025</div>
                        <div class="news-title">Provincial Development Grants Now Open</div>
                        <div class="news-excerpt">Applications accepted until September 15 for community projects, small business support, and infrastructure...</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Information -->
    <section class="contact-section" id="contact">
        <div class="section-container">
            <h2 class="section-title">Contact Provincial Government</h2>
            
            <div class="contact-grid">
                <div class="contact-card">
                    <div class="contact-icon">🏛️</div>
                    <h3 class="contact-title">Provincial Headquarters</h3>
                    <div class="contact-info">
                        Wewak, East Sepik Province<br>
                        Papua New Guinea<br>
                        PO Box 280, Wewak
                    </div>
                </div>
                
                <div class="contact-card">
                    <div class="contact-icon">📞</div>
                    <h3 class="contact-title">Phone & Fax</h3>
                    <div class="contact-info">
                        Phone: (+*************<br>
                        Fax: (+*************<br>
                        Emergency: 000
                    </div>
                </div>
                
                <div class="contact-card">
                    <div class="contact-icon">✉️</div>
                    <h3 class="contact-title">Email & Web</h3>
                    <div class="contact-info">
                        <EMAIL><br>
                        <EMAIL><br>
                        www.eastsepik.gov.pg
                    </div>
                </div>
                
                <div class="contact-card">
                    <div class="contact-icon">🕐</div>
                    <h3 class="contact-title">Office Hours</h3>
                    <div class="contact-info">
                        Monday - Friday<br>
                        8:00 AM - 4:30 PM<br>
                        Closed Weekends & Public Holidays
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="section-container">
            <div class="footer-grid">
                <div class="footer-section">
                    <h3>East Sepik Provincial Government</h3>
                    <p>Committed to serving the people of East Sepik Province through transparent governance, sustainable development, and preservation of our cultural heritage.</p>
                    <p>© 2025 East Sepik Provincial Administration. All rights reserved.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Government Services</h3>
                    <a href="#services">Online Services</a>
                    <a href="#administration">Government Departments</a>
                    <a href="#documents">Official Documents</a>
                    <a href="#budget">Budget & Finance</a>
                    <a href="#projects">Development Projects</a>
                </div>
                
                <div class="footer-section">
                    <h3>Information</h3>
                    <a href="#news">News & Updates</a>
                    <a href="#announcements">Public Announcements</a>
                    <a href="#events">Government Events</a>
                    <a href="#policies">Policies & Regulations</a>
                    <a href="#reports">Annual Reports</a>
                </div>
                
                <div class="footer-section">
                    <h3>Connect With Us</h3>
                    <a href="#facebook">Facebook</a>
                    <a href="#twitter">Twitter</a>
                    <a href="#newsletter">Newsletter Signup</a>
                    <a href="#feedback">Citizen Feedback</a>
                    <a href="#contact">Contact Information</a>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>East Sepik Provincial Administration | Papua New Guinea Government</p>
                <p>Building a better future for all citizens of East Sepik Province</p>
            </div>
        </div>
    </footer>

    <script>
        // Slideshow functionality
        let slideIndex = 1;
        showSlides(slideIndex);

        function currentSlide(n) {
            showSlides(slideIndex = n);
        }

        function showSlides(n) {
            let slides = document.getElementsByClassName("slide");
            let dots = document.getElementsByClassName("slide-dot");
            
            if (n > slides.length) {slideIndex = 1}
            if (n < 1) {slideIndex = slides.length}
            
            for (let i = 0; i < slides.length; i++) {
                slides[i].classList.remove("active");
            }
            
            for (let i = 0; i < dots.length; i++) {
                dots[i].classList.remove("active");
            }
            
            slides[slideIndex-1].classList.add("active");
            dots[slideIndex-1].classList.add("active");
        }

        // Auto slideshow
        function autoSlides() {
            slideIndex++;
            showSlides(slideIndex);
        }

        setInterval(autoSlides, 5000); // Change slide every 5 seconds

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animations
        window.addEventListener('load', function() {
            const animatedElements = document.querySelectorAll('.mp-card, .district-card, .event-item, .news-item');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });
        });

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.mp-card, .district-card, .contact-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Accessibility improvements
        document.addEventListener('keydown', function(e) {
            // Escape key closes any open modals/dropdowns
            if (e.key === 'Escape') {
                document.querySelectorAll('.dropdown-open, .modal-open').forEach(el => {
                    el.classList.remove('dropdown-open', 'modal-open');
                });
            }
        });

        // Add focus indicators for keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });

        // Interactive district cards
        document.querySelectorAll('.district-card').forEach(card => {
            card.addEventListener('click', function() {
                const districtName = this.querySelector('.district-name').textContent;
                alert(`Learn more about ${districtName} - this would open a detailed page about the district.`);
            });
        });

        // MP card interactions
        document.querySelectorAll('.mp-card').forEach(card => {
            card.addEventListener('click', function() {
                const mpName = this.querySelector('.mp-name').textContent;
                alert(`Contact ${mpName} - this would open the MP's contact information and profile.`);
            });
        });
    </script>

    <!-- Additional CSS for enhanced interactions -->
    <style>
        .keyboard-navigation *:focus {
            outline: 3px solid var(--png-yellow) !important;
            outline-offset: 2px !important;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            :root {
                --png-red: #FF0000;
                --png-green: #00AA00;
                --png-yellow: #FFFF00;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Interactive cursors */
        .mp-card,
        .district-card {
            cursor: pointer;
        }

        .mp-card:hover,
        .district-card:hover {
            cursor: pointer;
        }

        /* Print styles */
        @media print {
            .slideshow-section,
            .nav-menu,
            .contact-section {
                display: none;
            }
            
            body {
                background: white;
                color: black;
            }
            
            .mp-card,
            .district-card,
            .event-item,
            .news-item {
                box-shadow: none;
                border: 1px solid #ccc;
                break-inside: avoid;
            }
        }
    </style>
</body>
</html>