<?php
/**
 * Debug Customizer Functionality
 * 
 * This file helps debug customizer issues.
 * Add this to your functions.php temporarily if you need to debug further.
 */

// Uncomment the lines below and add to functions.php if you need additional debugging

/*
// Debug function to check customizer scripts
function nols_espa_debug_customizer() {
    if (is_customize_preview()) {
        echo '<script>console.log("Customizer preview mode active");</script>';
    }
}
add_action('wp_footer', 'nols_espa_debug_customizer');

// Debug function to check media scripts
function nols_espa_debug_media_scripts() {
    global $wp_scripts;
    
    if (is_admin() && isset($_GET['customize_theme'])) {
        echo '<script>';
        echo 'console.log("Customizer mode detected");';
        echo 'console.log("Registered scripts:", ' . json_encode(array_keys($wp_scripts->registered)) . ');';
        echo '</script>';
    }
}
add_action('admin_footer', 'nols_espa_debug_media_scripts');

// Force media scripts to load
function nols_espa_force_media_scripts() {
    if (is_admin()) {
        wp_enqueue_media();
        wp_enqueue_script('media-upload');
        wp_enqueue_script('thickbox');
        wp_enqueue_style('thickbox');
    }
}
add_action('admin_enqueue_scripts', 'nols_espa_force_media_scripts');
*/

// Instructions for testing:
// 1. Go to Appearance > Customize
// 2. Open browser developer tools (F12)
// 3. Look for console messages
// 4. Try clicking the header image upload button
// 5. Check for any JavaScript errors in the console

?>
