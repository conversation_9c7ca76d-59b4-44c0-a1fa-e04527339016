.sidr {
    background: #fff;
    display: block;
    position: fixed;
    top: 0;
    height: 100%;
    z-index: 999999;
    width: 320px;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidr .sidr-inner {
    padding: 0 0 15px;
}

.sidr.right {
    left: auto;
    right: -320px;
}

.sidr.left {
    left: -320px;
    right: auto;
}

.sidr {
    padding-left: 15px;
    padding-right: 15px;
}

@media only screen and (min-width: 992px) {
    body.sidr-open:before {
        content: "";
        left: 0;
        top: 0;
        z-index: 102;
        width: 100%;
        height: 100%;
        position: fixed;
        cursor: pointer;
        -webkit-transition: visibility 0s, opacity 0.4s linear;
        transition: visibility 0s, opacity 0.4s linear;
        visibility: visible;
        opacity: 1;
        background-color: #fff;
        background-color: rgba(255, 255, 255, .54);
    }
}

.sidr-class-sidr-button-close {
    font-size: 30px;
    text-align: right;
    display: block;
}