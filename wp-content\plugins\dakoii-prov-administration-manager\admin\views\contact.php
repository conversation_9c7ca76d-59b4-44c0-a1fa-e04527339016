<?php
/**
 * Provincial Administration Manager - Contact Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get all contacts
$contacts = get_posts(array(
    'post_type' => 'esp_contact',
    'numberposts' => -1,
    'post_status' => array('publish', 'draft', 'private'),
    'orderby' => 'title',
    'order' => 'ASC'
));

// Debug: Check if post type exists
$post_types = get_post_types();
$esp_contact_exists = in_array('esp_contact', $post_types);

// Debug: Count contacts
$contact_count = count($contacts);
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Contact Management', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage contact information for different departments, offices, and services', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <!-- Quick Actions -->
    <div class="esp-quick-actions">
        <h3><?php _e('Quick Actions', 'esp-admin-manager'); ?></h3>
        <div class="actions">
            <a href="<?php echo admin_url('post-new.php?post_type=esp_contact'); ?>" class="esp-button">
                <?php _e('Add New Contact', 'esp-admin-manager'); ?>
            </a>
            <a href="<?php echo admin_url('edit.php?post_type=esp_contact'); ?>" class="esp-button secondary">
                <?php _e('Manage All Contacts', 'esp-admin-manager'); ?>
            </a>
        </div>
    </div>

    <!-- Debug Information -->
    <?php if (current_user_can('manage_options')): ?>
        <div class="notice notice-info" style="margin: 20px 0;">
            <p><strong>Debug Info:</strong></p>
            <ul>
                <li>Post type 'esp_contact' exists: <?php echo $esp_contact_exists ? 'Yes' : 'No'; ?></li>
                <li>Contacts found: <?php echo $contact_count; ?></li>
                <li>Available post types: <?php echo implode(', ', array_slice($post_types, 0, 10)); ?>...</li>
            </ul>

            <?php if (!$esp_contact_exists): ?>
                <p><strong>Issue:</strong> The esp_contact post type is not registered. Try refreshing the page or check if the plugin is properly activated.</p>
                <form method="post" style="display: inline;">
                    <input type="hidden" name="flush_rewrite_rules" value="1">
                    <input type="submit" class="button" value="Flush Rewrite Rules">
                </form>
            <?php endif; ?>

            <?php if ($esp_contact_exists && $contact_count === 0): ?>
                <p><strong>Note:</strong> No contacts found. The post type is registered but no contacts have been created yet.</p>
                <form method="post" style="display: inline;">
                    <input type="hidden" name="create_test_contact" value="1">
                    <input type="submit" class="button" value="Create Test Contact">
                </form>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Contacts List -->
    <div class="esp-form-section">
        <h3><?php _e('Existing Contacts', 'esp-admin-manager'); ?> (<?php echo $contact_count; ?> found)</h3>

        <?php if (!empty($contacts)): ?>
        <table class="esp-list-table">
            <thead>
                <tr>
                    <th><?php _e('Contact Name', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Phone', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Email', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Shortcode', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($contacts as $contact):
                    $phone = get_post_meta($contact->ID, '_esp_contact_phone', true);
                    $email = get_post_meta($contact->ID, '_esp_contact_email', true);
                ?>
                <tr>
                    <td>
                        <strong><?php echo esc_html($contact->post_title); ?></strong>
                        <div style="font-size: 12px; color: #666;">
                            <?php echo esc_html(wp_trim_words($contact->post_content, 10)); ?>
                        </div>
                    </td>
                    <td>
                        <?php echo esc_html($phone ?: '—'); ?>
                    </td>
                    <td>
                        <?php echo esc_html($email ?: '—'); ?>
                    </td>
                    <td>
                        <code>[dakoii_contact id="<?php echo esc_attr($contact->ID); ?>"]</code>
                        <button type="button" class="button-link copy-shortcode" data-shortcode='[dakoii_contact id="<?php echo esc_attr($contact->ID); ?>"]'>
                            <?php _e('Copy', 'esp-admin-manager'); ?>
                        </button>
                    </td>
                    <td>
                        <a href="<?php echo get_edit_post_link($contact->ID); ?>" class="button button-small">
                            <?php _e('Edit', 'esp-admin-manager'); ?>
                        </a>
                        <a href="<?php echo get_delete_post_link($contact->ID); ?>" class="button button-small button-link-delete"
                           onclick="return confirm('<?php _e('Are you sure you want to delete this contact?', 'esp-admin-manager'); ?>');">
                            <?php _e('Delete', 'esp-admin-manager'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
            <div class="esp-no-items">
                <p><?php _e('No contacts found. Create your first contact to get started.', 'esp-admin-manager'); ?></p>
                <a href="<?php echo admin_url('post-new.php?post_type=esp_contact'); ?>" class="button button-primary">
                    <?php _e('Add Your First Contact', 'esp-admin-manager'); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Usage Instructions -->
    <div class="esp-form-section" style="margin-top: 40px; border-top: 2px solid #ddd; padding-top: 30px;">
        <h2><?php _e('How to Use Contacts', 'esp-admin-manager'); ?></h2>
        <p><?php _e('Contacts work like custom posts. Create contacts for different departments, offices, or services, then use shortcodes to display them.', 'esp-admin-manager'); ?></p>

        <div class="esp-usage-info" style="background: #e7f3ff; padding: 15px; border-radius: 8px; border-left: 4px solid #0073aa; margin-bottom: 20px;">
            <h4><?php _e('How to Create and Use Contacts', 'esp-admin-manager'); ?></h4>
            <ol style="margin: 10px 0 0 20px;">
                <li><strong><?php _e('Create a Contact:', 'esp-admin-manager'); ?></strong> <?php _e('Click "Add New Contact" above or go to Posts → Contacts', 'esp-admin-manager'); ?></li>
                <li><strong><?php _e('Fill in Details:', 'esp-admin-manager'); ?></strong> <?php _e('Add contact name, description, and contact information in the meta box', 'esp-admin-manager'); ?></li>
                <li><strong><?php _e('Use Shortcode:', 'esp-admin-manager'); ?></strong> <?php _e('Copy the shortcode from the list above and paste it anywhere on your website', 'esp-admin-manager'); ?></li>
            </ol>

            <h4 style="margin-top: 20px;"><?php _e('Shortcode Examples', 'esp-admin-manager'); ?></h4>
            <ul style="margin: 10px 0 0 20px;">
                <li><strong><?php _e('Basic usage:', 'esp-admin-manager'); ?></strong> <code>[dakoii_contact id="123"]</code> <?php _e('(where 123 is the contact ID)', 'esp-admin-manager'); ?></li>
                <li><strong><?php _e('With layout:', 'esp-admin-manager'); ?></strong> <code>[dakoii_contact id="123" layout="list"]</code></li>
                <li><strong><?php _e('District contacts:', 'esp-admin-manager'); ?></strong> <code>[dakoii_contact district_id="456"]</code> <?php _e('(where 456 is the district ID)', 'esp-admin-manager'); ?></li>
            </ul>
        </div>
    </div>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Contact Management Tips', 'esp-admin-manager'); ?></h4>
        <p><?php _e('Best practices for managing contact information:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Use descriptive contact names (e.g., "Emergency Services", "Tourism Office")', 'esp-admin-manager'); ?></li>
            <li><?php _e('Keep contact information current and verify regularly', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use international format for phone numbers: (+675) XXX-XXXX', 'esp-admin-manager'); ?></li>
            <li><?php _e('Include clear office hours to manage citizen expectations', 'esp-admin-manager'); ?></li>
            <li><?php _e('Test email addresses and phone numbers periodically', 'esp-admin-manager'); ?></li>
        </ul>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Copy shortcode functionality
    $('.copy-shortcode').on('click', function() {
        let shortcode = $(this).data('shortcode');
        navigator.clipboard.writeText(shortcode).then(function() {
            // Show success message
            let button = $(this);
            let originalText = button.text();
            button.text('Copied!').css('color', '#46b450');
            setTimeout(function() {
                button.text(originalText).css('color', '');
            }, 2000);
        }.bind(this));
    });
});
</script>
