# 🗺️ Maps Feature Implementation Guide

This guide provides complete instructions for implementing the interactive maps feature in the Provincial Administration Manager plugin. The maps feature allows users to upload GeoJSON boundary files and display them as interactive maps using Leaflet.js.

## ✅ **Features Implemented:**

- **Interactive Map Display** with Leaflet.js integration
- **GeoJSON Boundary Support** for province/district boundaries
- **File Upload & Management** with replace functionality
- **Admin Interface** for map management
- **Shortcode System** for frontend display
- **PNG Flag Color Scheme** (Green: #006A4E, Red: #CE1126, Yellow: #FFD700)
- **Responsive Design** for mobile devices
- **Error Handling** and debugging features

---

## 1. 📋 **Implementation Steps**

### Step 1: Add Maps Post Type

**File:** `includes/class-provincial-post-types.php`

Add the map post type registration to the `register_post_types()` method:

```php
// Add this call to register_post_types() method
$this->register_map_post_type();
```

Add the new method:

```php
/**
 * Register Map post type
 */
private function register_map_post_type() {
    register_post_type('esp_map', array(
        'public'             => true,
        'show_ui'            => true,
        'show_in_menu'       => false, // Managed through custom admin interface
        'supports'           => array('title', 'editor', 'thumbnail'),
        'labels'             => array(
            'name'               => __('Maps', 'esp-admin-manager'),
            'singular_name'      => __('Map', 'esp-admin-manager'),
            'add_new_item'       => __('Add New Map', 'esp-admin-manager'),
            'edit_item'          => __('Edit Map', 'esp-admin-manager'),
            'all_items'          => __('All Maps', 'esp-admin-manager'),
            'view_item'          => __('View Map', 'esp-admin-manager'),
            'search_items'       => __('Search Maps', 'esp-admin-manager'),
            'not_found'          => __('No maps found', 'esp-admin-manager'),
            'not_found_in_trash' => __('No maps found in trash', 'esp-admin-manager'),
        ),
        'menu_icon'          => 'dashicons-location-alt',
        'has_archive'        => true,
        'rewrite'            => array('slug' => 'maps'),
    ));
}
```

### Step 2: Add Maps Admin Menu

**File:** `includes/class-provincial-admin.php`

Add the maps submenu to the `add_admin_menu()` method:

```php
// Maps submenu
if (current_user_can('edit_posts') || current_user_can('manage_options')) {
    $maps_capability = current_user_can('manage_options') ? 'manage_options' : 'edit_posts';
    add_submenu_page(
        'provincial-admin-dashboard',
        __('Maps', 'esp-admin-manager'),
        __('Maps', 'esp-admin-manager'),
        $maps_capability,
        'provincial-admin-maps',
        array($this, 'maps_page')
    );
}
```

### Step 3: Add Maps Page Methods

**File:** `includes/class-provincial-admin.php`

Add these methods to handle the maps admin page:

```php
/**
 * Maps page
 */
public function maps_page() {
    // Handle JSON file upload
    if (isset($_POST['upload_json']) && wp_verify_nonce($_POST['provincial_nonce'], 'provincial_maps_nonce')) {
        $this->handle_map_json_upload();
    }

    include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/maps.php';
}

/**
 * Handle map JSON file upload
 */
private function handle_map_json_upload() {
    if (!isset($_FILES['map_json_file']) || $_FILES['map_json_file']['error'] !== UPLOAD_ERR_OK) {
        add_settings_error('esp_messages', 'esp_message', __('Please select a valid JSON file.', 'esp-admin-manager'), 'error');
        return;
    }

    $file = $_FILES['map_json_file'];
    $map_title = sanitize_text_field($_POST['map_title']);
    $map_description = sanitize_textarea_field($_POST['map_description']);

    // Validate file type
    $file_info = pathinfo($file['name']);
    if (strtolower($file_info['extension']) !== 'json') {
        add_settings_error('esp_messages', 'esp_message', __('Only JSON files are allowed.', 'esp-admin-manager'), 'error');
        return;
    }

    // Create upload directory
    $upload_dir = wp_upload_dir();
    $maps_dir = $upload_dir['basedir'] . '/provincial-maps/';
    if (!file_exists($maps_dir)) {
        wp_mkdir_p($maps_dir);
    }

    // Generate unique filename
    $filename = time() . '_' . sanitize_file_name($file['name']);
    $file_path = $maps_dir . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        // Create map post
        $map_id = wp_insert_post(array(
            'post_title' => $map_title,
            'post_content' => $map_description,
            'post_status' => 'publish',
            'post_type' => 'esp_map'
        ));

        if ($map_id) {
            update_post_meta($map_id, '_esp_map_json_file', $filename);
            update_post_meta($map_id, '_esp_map_json_path', $file_path);
            add_settings_error('esp_messages', 'esp_message', __('Map uploaded successfully.', 'esp-admin-manager'), 'updated');
        }
    } else {
        add_settings_error('esp_messages', 'esp_message', __('Failed to upload file.', 'esp-admin-manager'), 'error');
    }
}
```

### Step 4: Create Maps Admin View

**File:** `admin/views/maps.php`

Create the complete admin interface for map management. This file is already implemented and includes:

- Upload form for new maps
- Table showing existing maps with edit/delete actions
- Copy shortcode functionality
- Usage instructions
- Professional styling with PNG flag colors

### Step 5: Add Map Meta Boxes

**File:** `includes/class-provincial-meta-boxes.php`

Add map meta box support to allow file uploads when editing maps:

```php
// Add to add_meta_boxes() method
add_meta_box(
    'provincial_map_details',
    __('Map File Management', 'esp-admin-manager'),
    array($this, 'map_meta_box_callback'),
    'esp_map',
    'normal',
    'high'
);
```

Add the meta box callback and file upload handling methods (already implemented).

### Step 6: Add Leaflet.js Integration

**File:** `dakoii-provincial-admin-manager.php`

Add Leaflet.js library to the public script enqueuing:

```php
/**
 * Enqueue public scripts and styles
 */
public function public_enqueue_scripts() {
    wp_enqueue_style(
        'provincial-public-style',
        PROVINCIAL_ADMIN_MANAGER_PLUGIN_URL . 'public/css/public-style.css',
        array(),
        PROVINCIAL_ADMIN_MANAGER_VERSION
    );

    // Enqueue Leaflet.js for maps
    wp_enqueue_style(
        'leaflet-css',
        'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css',
        array(),
        '1.9.4'
    );

    wp_enqueue_script(
        'leaflet-js',
        'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js',
        array(),
        '1.9.4',
        false // Load in header to ensure availability
    );

    wp_enqueue_script(
        'provincial-public-script',
        PROVINCIAL_ADMIN_MANAGER_PLUGIN_URL . 'public/js/public-script.js',
        array('jquery', 'leaflet-js'),
        PROVINCIAL_ADMIN_MANAGER_VERSION,
        true
    );
}
```

### Step 7: Add Map Shortcodes

**File:** `includes/class-provincial-shortcodes.php`

Add shortcode registration to the constructor:

```php
add_shortcode('dakoii_map', array($this, 'map_shortcode'));
add_shortcode('dakoii_maps', array($this, 'maps_shortcode'));
```

The shortcode methods are already implemented with:
- Interactive Leaflet.js map rendering
- GeoJSON boundary display
- PNG flag color scheme
- Hover effects and popups
- Responsive design
- Error handling and debugging

### Step 8: Add Map JavaScript Functionality

**File:** `public/js/public-script.js`

Add map initialization to the document ready function:

```javascript
$(document).ready(function() {
    // ... existing initialization
    initMaps();
});
```

The `initMaps()` function is already implemented with:
- Leaflet.js map creation
- GeoJSON data loading and rendering
- Interactive features (hover, click, zoom)
- PNG flag color styling
- Error handling and debugging

### Step 9: Add Map Styling

**File:** `public/css/public-style.css`

Map styles are already implemented including:
- Container styling with PNG flag colors
- Loading animations
- Error state styling
- Responsive design
- Leaflet control customization

---

## 🧪 **Testing the Implementation**

### 1. **Plugin Activation**
- Deactivate and reactivate the plugin to register the new post type
- The `esp_map` post type will be created automatically

### 2. **Upload a Map**
- Go to **Provincial Administration → Maps**
- Upload a GeoJSON file (like the East Sepik boundaries)
- Add title and description
- Save the map

### 3. **Display the Map**
- Use shortcode: `[dakoii_map id="86"]` (replace with actual map ID)
- The map will display with interactive boundaries
- Features include hover effects, popups, and zoom controls

### 4. **Edit Maps**
- Click "Edit" on any map in the admin list
- Upload replacement JSON files via the meta box
- Update title and description as needed

---

## 🎯 **Features Included**

✅ **Interactive Maps** - Leaflet.js powered boundary display
✅ **File Management** - Upload, replace, and delete JSON files
✅ **Admin Interface** - Professional management dashboard
✅ **Meta Boxes** - Edit maps with file upload capability
✅ **Shortcode System** - Easy frontend integration
✅ **PNG Flag Colors** - Official Papua New Guinea color scheme
✅ **Responsive Design** - Mobile-friendly display
✅ **Error Handling** - Comprehensive debugging and fallbacks
✅ **Security** - Nonce verification and file validation

The maps feature is now fully implemented and working! 🗺️✨
