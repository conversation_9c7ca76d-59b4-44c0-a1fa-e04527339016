# User Management and Leadership System Guide
## Dakoii Provincial Administration Manager - Multi-Level User Access Control

### Overview
This guide outlines the comprehensive user management system that allows both Provincial and District users to manage their respective content while maintaining proper access controls and leadership hierarchies.

### System Architecture

#### User Types and Access Levels

**1. Provincial Users**
- **Full Access**: Can manage all content across the entire province
- **Capabilities**: Create, edit, delete all posts, manage all districts, oversee all events/news
- **Role**: `provincial_admin`, `provincial_editor`

**2. District Users**  
- **Limited Access**: Can only manage content for their assigned district(s)
- **Capabilities**: Create, edit district-specific content, manage district events/news
- **Role**: `district_admin`, `district_editor`

#### Leadership Hierarchy System

**Political Leadership** (Elected Officials)
- **Governor**: Provincial political leader
- **Members of Parliament (MPs)**: District/electorate representatives
- **Characteristics**: Elected positions, political messages, party affiliations

**Administrative Leadership** (Appointed Officials)
- **Provincial Administrator**: Chief administrative officer
- **Deputy Provincial Administrators**: Senior administrative staff
- **Executive Managers**: Department heads and senior managers
- **District Administrators**: District-level administrative heads
- **Characteristics**: Appointed positions, administrative messages, operational focus

---

## User Management Implementation

### 1. Custom User Roles and Capabilities

**Provincial Roles:**
```php
// Provincial Administrator - Full access
'provincial_admin' => array(
    'read' => true,
    'edit_posts' => true,
    'delete_posts' => true,
    'publish_posts' => true,
    'edit_others_posts' => true,
    'delete_others_posts' => true,
    'manage_esp_content' => true,
    'manage_all_districts' => true,
    'manage_provincial_settings' => true,
)

// Provincial Editor - Content management
'provincial_editor' => array(
    'read' => true,
    'edit_posts' => true,
    'delete_posts' => true,
    'publish_posts' => true,
    'edit_esp_content' => true,
    'view_all_districts' => true,
)
```

**District Roles:**
```php
// District Administrator - District-specific full access
'district_admin' => array(
    'read' => true,
    'edit_posts' => true,
    'delete_posts' => true,
    'publish_posts' => true,
    'manage_district_content' => true,
    'edit_assigned_district' => true,
)

// District Editor - District content editing
'district_editor' => array(
    'read' => true,
    'edit_posts' => true,
    'publish_posts' => true,
    'edit_district_content' => true,
    'view_assigned_district' => true,
)
```

### 2. User-District Assignment System

**User Meta Fields:**
```php
// Store district assignments for users
'_esp_assigned_districts' => array(1, 3, 5) // District post IDs
'_esp_user_level' => 'district' // 'provincial' or 'district'
'_esp_user_role_type' => 'admin' // 'admin' or 'editor'
```

**Access Control Functions:**
```php
// Check if user can access specific district
function esp_user_can_access_district($user_id, $district_id) {
    $user_level = get_user_meta($user_id, '_esp_user_level', true);
    
    if ($user_level === 'provincial') {
        return true; // Provincial users can access all districts
    }
    
    $assigned_districts = get_user_meta($user_id, '_esp_assigned_districts', true);
    return in_array($district_id, $assigned_districts);
}
```

### 3. Content Access Control

**Post Query Filtering:**
```php
// Filter posts based on user access level
add_action('pre_get_posts', 'esp_filter_posts_by_user_access');

function esp_filter_posts_by_user_access($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }
    
    $current_user = wp_get_current_user();
    $user_level = get_user_meta($current_user->ID, '_esp_user_level', true);
    
    if ($user_level === 'district') {
        $assigned_districts = get_user_meta($current_user->ID, '_esp_assigned_districts', true);
        
        // Filter district-related content
        if (in_array($query->get('post_type'), array('esp_district', 'esp_event', 'esp_news'))) {
            $meta_query = array(
                array(
                    'key' => '_esp_district_id',
                    'value' => $assigned_districts,
                    'compare' => 'IN'
                )
            );
            $query->set('meta_query', $meta_query);
        }
    }
}
```

---

## Leadership Management System

### 1. Political Leadership Structure

**Governor Post Type** (Already exists - enhance):
```php
// Additional meta fields for Governor
'_esp_governor_type' => 'political' // Always political
'_esp_governor_election_year' => '2022'
'_esp_governor_term_end' => '2027'
'_esp_governor_political_message' => 'Political leadership message'
'_esp_governor_achievements' => 'Key political achievements'
```

**MPs Post Type** (Already exists - enhance):
```php
// Additional meta fields for MPs
'_esp_mp_type' => 'political' // Always political
'_esp_mp_election_year' => '2022'
'_esp_mp_term_end' => '2027'
'_esp_mp_political_message' => 'Political message to constituents'
'_esp_mp_district_id' => 123 // Link to district
```

### 2. Administrative Leadership Structure

**New Post Types for Administrative Leadership:**

**Provincial Administrator:**
```php
register_post_type('esp_prov_admin', array(
    'public' => true,
    'show_ui' => true,
    'show_in_menu' => true,
    'supports' => array('title', 'editor', 'thumbnail'),
    'labels' => array(
        'name' => 'Provincial Administrators',
        'singular_name' => 'Provincial Administrator',
    ),
));

// Meta fields
'_esp_prov_admin_type' => 'administrative'
'_esp_prov_admin_appointment_date' => '2023-01-15'
'_esp_prov_admin_department' => 'General Administration'
'_esp_prov_admin_message' => 'Administrative leadership message'
'_esp_prov_admin_qualifications' => 'Educational and professional background'
```

**Deputy Provincial Administrators:**
```php
register_post_type('esp_deputy_admin', array(
    'public' => true,
    'show_ui' => true,
    'show_in_menu' => true,
    'supports' => array('title', 'editor', 'thumbnail'),
    'labels' => array(
        'name' => 'Deputy Provincial Administrators',
        'singular_name' => 'Deputy Provincial Administrator',
    ),
));

// Meta fields
'_esp_deputy_admin_type' => 'administrative'
'_esp_deputy_admin_department' => 'Finance' // Department they oversee
'_esp_deputy_admin_reports_to' => 123 // Provincial Admin post ID
'_esp_deputy_admin_message' => 'Administrative message'
```

**Executive Managers:**
```php
register_post_type('esp_exec_manager', array(
    'public' => true,
    'show_ui' => true,
    'show_in_menu' => true,
    'supports' => array('title', 'editor', 'thumbnail'),
    'labels' => array(
        'name' => 'Executive Managers',
        'singular_name' => 'Executive Manager',
    ),
));

// Meta fields
'_esp_exec_manager_type' => 'administrative'
'_esp_exec_manager_department' => 'Health Services'
'_esp_exec_manager_level' => 'senior' // senior, mid, junior
'_esp_exec_manager_message' => 'Department message'
```

**District Administrators:**
```php
register_post_type('esp_district_admin', array(
    'public' => true,
    'show_ui' => true,
    'show_in_menu' => true,
    'supports' => array('title', 'editor', 'thumbnail'),
    'labels' => array(
        'name' => 'District Administrators',
        'singular_name' => 'District Administrator',
    ),
));

// Meta fields
'_esp_district_admin_type' => 'administrative'
'_esp_district_admin_district_id' => 123 // Link to district
'_esp_district_admin_appointment_date' => '2023-06-01'
'_esp_district_admin_message' => 'District administrative message'
```

### 3. Leadership Message System

**Message Types and Display:**
```php
// Political messages - focus on vision, policies, community
function get_political_message($leader_id, $leader_type) {
    $message_key = "_esp_{$leader_type}_political_message";
    return get_post_meta($leader_id, $message_key, true);
}

// Administrative messages - focus on operations, services, efficiency
function get_administrative_message($leader_id, $leader_type) {
    $message_key = "_esp_{$leader_type}_message";
    return get_post_meta($leader_id, $message_key, true);
}
```

---

## User Interface and Experience

### 1. Admin Dashboard Customization

**Provincial User Dashboard:**
```php
// Show all districts, full statistics, system-wide controls
function esp_provincial_dashboard() {
    // Display all districts
    // Show province-wide statistics
    // Access to all management functions
    // User management capabilities
}
```

**District User Dashboard:**
```php
// Show only assigned districts, limited statistics, district controls
function esp_district_dashboard() {
    $user_id = get_current_user_id();
    $assigned_districts = get_user_meta($user_id, '_esp_assigned_districts', true);
    
    // Display only assigned districts
    // Show district-specific statistics
    // Limited management functions
    // No user management access
}
```

### 2. Content Management Restrictions

**Post Editing Restrictions:**
```php
// Restrict post editing based on user level and district assignment
add_filter('user_can_edit_post', 'esp_restrict_post_editing', 10, 3);

function esp_restrict_post_editing($can_edit, $post_id, $user_id) {
    $post_type = get_post_type($post_id);
    $user_level = get_user_meta($user_id, '_esp_user_level', true);
    
    if ($user_level === 'provincial') {
        return $can_edit; // Provincial users can edit everything
    }
    
    if ($user_level === 'district') {
        // Check if post belongs to user's assigned district
        $post_district = get_post_meta($post_id, '_esp_district_id', true);
        $assigned_districts = get_user_meta($user_id, '_esp_assigned_districts', true);
        
        return in_array($post_district, $assigned_districts);
    }
    
    return $can_edit;
}
```

### 3. Menu and Navigation Customization

**Dynamic Admin Menu:**
```php
// Customize admin menu based on user level
add_action('admin_menu', 'esp_customize_admin_menu_by_user_level');

function esp_customize_admin_menu_by_user_level() {
    $user_level = get_user_meta(get_current_user_id(), '_esp_user_level', true);
    
    if ($user_level === 'district') {
        // Remove provincial-only menu items
        remove_menu_page('provincial-admin-statistics');
        remove_menu_page('provincial-admin-structure');
        remove_menu_page('provincial-admin-users');
        
        // Add district-specific menu items
        add_menu_page(
            'My Districts',
            'My Districts',
            'edit_posts',
            'my-districts',
            'esp_my_districts_page'
        );
    }
}
```

---

## Implementation Workflow

### 1. User Registration and Assignment

**New User Setup Process:**
1. Admin creates new user account
2. Assigns user level (Provincial/District)
3. Assigns specific districts (for district users)
4. Sets role type (Admin/Editor)
5. User receives login credentials and orientation

**User Assignment Interface:**
```php
// Admin interface for user-district assignment
function esp_user_assignment_interface($user_id) {
    $districts = get_posts(array('post_type' => 'esp_district', 'numberposts' => -1));
    $assigned = get_user_meta($user_id, '_esp_assigned_districts', true);
    
    echo '<h3>District Assignments</h3>';
    foreach ($districts as $district) {
        $checked = in_array($district->ID, $assigned) ? 'checked' : '';
        echo "<label><input type='checkbox' name='assigned_districts[]' value='{$district->ID}' {$checked}> {$district->post_title}</label><br>";
    }
}
```

### 2. Content Creation Workflow

**District User Content Creation:**
1. User logs in and sees only their assigned districts
2. Creates content (events, news) with automatic district association
3. Content is automatically tagged with district ID
4. Approval workflow (optional) for content publishing

**Provincial User Content Creation:**
1. User has access to all districts and provincial content
2. Can create content for any district or province-wide
3. Can manage and approve district user content
4. Full administrative capabilities

### 3. Leadership Profile Management

**Political Leadership Profiles:**
- Managed by Provincial users only
- Focus on political messages, achievements, vision
- Election information and term details
- Public-facing political content

**Administrative Leadership Profiles:**
- Can be managed by appropriate level users
- Focus on operational messages, department info
- Appointment details and qualifications
- Service-oriented content

---

## Shortcodes for User-Specific Content

### 1. User-Aware Shortcodes

**District-Specific Content Display:**
```php
// Show content based on current user's district assignments
add_shortcode('dakoii_my_districts', array($this, 'my_districts_shortcode'));
add_shortcode('dakoii_my_events', array($this, 'my_events_shortcode'));
add_shortcode('dakoii_my_news', array($this, 'my_news_shortcode'));

// Leadership shortcodes with user context
add_shortcode('dakoii_political_leadership', array($this, 'political_leadership_shortcode'));
add_shortcode('dakoii_administrative_leadership', array($this, 'administrative_leadership_shortcode'));
```

**Example Implementation:**
```php
public function my_districts_shortcode($atts) {
    $user_id = get_current_user_id();
    $user_level = get_user_meta($user_id, '_esp_user_level', true);

    if ($user_level === 'provincial') {
        // Show all districts for provincial users
        return $this->districts_shortcode($atts);
    } else {
        // Show only assigned districts for district users
        $assigned_districts = get_user_meta($user_id, '_esp_assigned_districts', true);
        $atts['district_ids'] = implode(',', $assigned_districts);
        return $this->districts_shortcode($atts);
    }
}
```

### 2. Leadership Display Shortcodes

**Political Leadership Shortcodes:**
```php
// Governor with political focus
[dakoii_governor type="political" show_message="true" show_achievements="true"]

// MPs with political context
[dakoii_mps type="political" show_party="true" show_electorate="true"]

// Combined political leadership
[dakoii_political_leadership show_governor="true" show_mps="true"]
```

**Administrative Leadership Shortcodes:**
```php
// Provincial Administrator
[dakoii_provincial_admin show_message="true" show_department="true"]

// Deputy Administrators
[dakoii_deputy_admins layout="grid" show_departments="true"]

// Executive Managers by department
[dakoii_executive_managers department="health" layout="list"]

// District Administrators
[dakoii_district_admins district_id="123" show_message="true"]

// Combined administrative leadership
[dakoii_administrative_leadership level="provincial" show_hierarchy="true"]
```

### 3. User Dashboard Shortcodes

**Dashboard Components:**
```php
// User-specific dashboard
[dakoii_user_dashboard]

// Quick stats for user's assigned content
[dakoii_my_stats show_districts="true" show_events="true" show_news="true"]

// Recent activity in user's districts
[dakoii_recent_activity limit="10" days="30"]
```

---

## Database Schema for User Management

### 1. User Meta Fields

**Core User Assignment Fields:**
```php
// User level and assignments
'_esp_user_level'           => 'provincial' | 'district'
'_esp_user_role_type'       => 'admin' | 'editor'
'_esp_assigned_districts'   => array(1, 3, 5) // District post IDs
'_esp_user_permissions'     => array('manage_events', 'manage_news')
'_esp_user_created_by'      => 123 // User ID who created this account
'_esp_user_last_district_access' => '2024-01-15 10:30:00'
```

**User Activity Tracking:**
```php
'_esp_user_login_count'     => 45
'_esp_user_last_login'      => '2024-01-15 09:15:00'
'_esp_user_content_created' => 23 // Number of posts created
'_esp_user_content_edited'  => 67 // Number of posts edited
```

### 2. Content Association Fields

**District Content Linking:**
```php
// Link content to districts
'_esp_district_id'          => 123 // Primary district
'_esp_related_districts'    => array(1, 3) // Additional related districts
'_esp_content_scope'        => 'district' | 'provincial'
'_esp_created_by_user_level' => 'district' | 'provincial'
```

**Leadership Content Fields:**
```php
// Leadership type identification
'_esp_leadership_type'      => 'political' | 'administrative'
'_esp_leadership_level'     => 'provincial' | 'district' | 'department'
'_esp_leadership_hierarchy' => 1 // 1=highest, 5=lowest
'_esp_reports_to'          => 123 // Post ID of superior
```

### 3. Access Control Tables

**User Access Log:**
```sql
CREATE TABLE esp_user_access_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    post_id INT,
    action VARCHAR(50), -- 'view', 'edit', 'create', 'delete'
    district_id INT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45)
);
```

---

## Security and Permissions

### 1. Content Security

**Data Validation:**
```php
// Validate user can access district before saving content
function esp_validate_district_access_on_save($post_id) {
    $user_id = get_current_user_id();
    $user_level = get_user_meta($user_id, '_esp_user_level', true);

    if ($user_level === 'district') {
        $post_district = get_post_meta($post_id, '_esp_district_id', true);
        $assigned_districts = get_user_meta($user_id, '_esp_assigned_districts', true);

        if (!in_array($post_district, $assigned_districts)) {
            wp_die('You do not have permission to edit content for this district.');
        }
    }
}
add_action('save_post', 'esp_validate_district_access_on_save');
```

**API Security:**
```php
// Secure REST API endpoints
add_filter('rest_pre_dispatch', 'esp_secure_rest_api', 10, 3);

function esp_secure_rest_api($result, $server, $request) {
    $route = $request->get_route();

    if (strpos($route, '/esp/v1/') === 0) {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return new WP_Error('rest_forbidden', 'Authentication required', array('status' => 401));
        }

        // Additional permission checks based on endpoint
        if (strpos($route, '/districts/') !== false) {
            $district_id = $request->get_param('district_id');
            if (!esp_user_can_access_district($user_id, $district_id)) {
                return new WP_Error('rest_forbidden', 'Insufficient permissions', array('status' => 403));
            }
        }
    }

    return $result;
}
```

### 2. User Session Management

**Session Tracking:**
```php
// Track user sessions and district access
function esp_track_user_session() {
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();
        $current_time = current_time('mysql');

        update_user_meta($user_id, '_esp_user_last_activity', $current_time);

        // Track district page visits
        if (is_singular('esp_district')) {
            global $post;
            update_user_meta($user_id, '_esp_user_last_district_access', $current_time);

            // Log district access
            $access_log = get_user_meta($user_id, '_esp_district_access_log', true) ?: array();
            $access_log[] = array(
                'district_id' => $post->ID,
                'timestamp' => $current_time,
                'action' => 'view'
            );

            // Keep only last 100 entries
            if (count($access_log) > 100) {
                $access_log = array_slice($access_log, -100);
            }

            update_user_meta($user_id, '_esp_district_access_log', $access_log);
        }
    }
}
add_action('wp', 'esp_track_user_session');
```

---

## Admin Interface Enhancements

### 1. User Management Interface

**Enhanced User Profile Page:**
```php
// Add custom fields to user profile
add_action('show_user_profile', 'esp_show_user_profile_fields');
add_action('edit_user_profile', 'esp_show_user_profile_fields');

function esp_show_user_profile_fields($user) {
    $user_level = get_user_meta($user->ID, '_esp_user_level', true);
    $assigned_districts = get_user_meta($user->ID, '_esp_assigned_districts', true);
    ?>
    <h3>Provincial Administration Settings</h3>
    <table class="form-table">
        <tr>
            <th><label for="esp_user_level">User Level</label></th>
            <td>
                <select name="esp_user_level" id="esp_user_level">
                    <option value="provincial" <?php selected($user_level, 'provincial'); ?>>Provincial User</option>
                    <option value="district" <?php selected($user_level, 'district'); ?>>District User</option>
                </select>
            </td>
        </tr>
        <tr id="district_assignments" style="<?php echo $user_level === 'district' ? '' : 'display:none;'; ?>">
            <th><label>Assigned Districts</label></th>
            <td>
                <?php
                $districts = get_posts(array('post_type' => 'esp_district', 'numberposts' => -1));
                foreach ($districts as $district) {
                    $checked = in_array($district->ID, (array)$assigned_districts) ? 'checked' : '';
                    echo "<label><input type='checkbox' name='esp_assigned_districts[]' value='{$district->ID}' {$checked}> {$district->post_title}</label><br>";
                }
                ?>
            </td>
        </tr>
    </table>

    <script>
    document.getElementById('esp_user_level').addEventListener('change', function() {
        document.getElementById('district_assignments').style.display =
            this.value === 'district' ? '' : 'none';
    });
    </script>
    <?php
}
```

### 2. Content Management Interface

**District-Filtered Content Lists:**
```php
// Filter admin post lists based on user access
add_filter('parse_query', 'esp_filter_admin_posts_by_user');

function esp_filter_admin_posts_by_user($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    $user_id = get_current_user_id();
    $user_level = get_user_meta($user_id, '_esp_user_level', true);

    if ($user_level === 'district') {
        $assigned_districts = get_user_meta($user_id, '_esp_assigned_districts', true);
        $post_type = $query->get('post_type');

        if (in_array($post_type, array('esp_event', 'esp_news', 'esp_district'))) {
            $meta_query = $query->get('meta_query') ?: array();
            $meta_query[] = array(
                'key' => '_esp_district_id',
                'value' => $assigned_districts,
                'compare' => 'IN'
            );
            $query->set('meta_query', $meta_query);
        }
    }
}
```

### 3. Dashboard Widgets

**User-Specific Dashboard Widgets:**
```php
// Add custom dashboard widgets based on user level
add_action('wp_dashboard_setup', 'esp_add_dashboard_widgets');

function esp_add_dashboard_widgets() {
    $user_level = get_user_meta(get_current_user_id(), '_esp_user_level', true);

    if ($user_level === 'provincial') {
        wp_add_dashboard_widget(
            'esp_provincial_overview',
            'Provincial Overview',
            'esp_provincial_dashboard_widget'
        );
    } else {
        wp_add_dashboard_widget(
            'esp_district_overview',
            'My Districts Overview',
            'esp_district_dashboard_widget'
        );
    }
}

function esp_district_dashboard_widget() {
    $user_id = get_current_user_id();
    $assigned_districts = get_user_meta($user_id, '_esp_assigned_districts', true);

    echo '<div class="esp-dashboard-widget">';
    echo '<h4>Your Assigned Districts</h4>';

    foreach ($assigned_districts as $district_id) {
        $district = get_post($district_id);
        if ($district) {
            $event_count = wp_count_posts('esp_event')->publish ?? 0;
            $news_count = wp_count_posts('esp_news')->publish ?? 0;

            echo "<div class='district-summary'>";
            echo "<h5>{$district->post_title}</h5>";
            echo "<p>Events: {$event_count} | News: {$news_count}</p>";
            echo "<a href='" . get_edit_post_link($district_id) . "'>Edit District</a>";
            echo "</div>";
        }
    }

    echo '</div>';
}
```

---

## Implementation Checklist

### Phase 1: User System Foundation
- [ ] Create custom user roles and capabilities
- [ ] Implement user-district assignment system
- [ ] Add user meta fields for access control
- [ ] Create user management interface

### Phase 2: Leadership Structure
- [ ] Create administrative leadership post types
- [ ] Enhance political leadership post types
- [ ] Implement leadership hierarchy system
- [ ] Add leadership message functionality

### Phase 3: Access Control
- [ ] Implement content filtering by user level
- [ ] Add post editing restrictions
- [ ] Create user-aware shortcodes
- [ ] Implement security validation

### Phase 4: Admin Interface
- [ ] Customize admin menus by user level
- [ ] Create user-specific dashboard widgets
- [ ] Add content management restrictions
- [ ] Implement user activity tracking

### Phase 5: Testing and Refinement
- [ ] Test all user access scenarios
- [ ] Validate security measures
- [ ] Optimize performance
- [ ] Create user documentation

---

## Conclusion

This user management and leadership system provides:

1. **Clear Separation**: Distinct roles for Provincial and District users
2. **Flexible Access Control**: Granular permissions based on district assignments
3. **Leadership Hierarchy**: Proper distinction between political and administrative roles
4. **Security**: Comprehensive access validation and content protection
5. **User Experience**: Intuitive interfaces tailored to user levels
6. **Scalability**: Easy to add new districts, users, and leadership roles

The system maintains WordPress standards while providing enterprise-level user management capabilities specifically designed for provincial administration needs.
