# Slideshow System Troubleshooting Guide

## Issue: "Failed to create slideshow group"

This error typically occurs when the slideshow database tables are missing or there's a database connectivity issue.

### Quick Fix Steps:

1. **Check Database Tables**
   - Go to WordPress Admin → Provincial Administration → Add Slideshow
   - If you see a warning about missing database tables, click "Create Database Tables"
   - This will manually create the required tables

2. **Verify Plugin Activation**
   - Go to WordPress Admin → Plugins
   - Deactivate the "Dakoii Provincial Administration Manager" plugin
   - Reactivate it (this will trigger table creation)

3. **Check User Permissions**
   - Ensure you're logged in as an Administrator
   - The slideshow system requires administrator privileges

### Detailed Troubleshooting:

#### Step 1: Enable WordPress Debug Mode
Add these lines to your `wp-config.php` file:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

#### Step 2: Check Debug Information
1. Go to Provincial Administration → Add Slideshow
2. If you're an administrator and debug mode is enabled, you'll see debug information
3. Look for:
   - `tables['groups']` and `tables['slides']` should both be `true`
   - `user_can_manage_options` should be `true`
   - `last_error` should be empty

#### Step 3: Manual Database Check
If you have database access (phpMyAdmin, etc.), check if these tables exist:
- `wp_esp_slideshow_groups`
- `wp_esp_slides`

(Replace `wp_` with your actual WordPress table prefix)

#### Step 4: Manual Table Creation
If tables don't exist, you can create them manually:

```sql
CREATE TABLE wp_esp_slideshow_groups (
    id int(11) NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    description text,
    tags text,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY name_idx (name(191)),
    KEY tags_idx (tags(191))
);

CREATE TABLE wp_esp_slides (
    id int(11) NOT NULL AUTO_INCREMENT,
    group_id int(11) NOT NULL,
    title varchar(255),
    description text,
    image_url varchar(500) NOT NULL,
    link_url varchar(500),
    slide_order int(11) DEFAULT 0,
    is_active tinyint(1) DEFAULT 1,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY group_id_idx (group_id),
    KEY order_idx (slide_order),
    KEY active_idx (is_active)
);
```

### Common Issues and Solutions:

#### Issue: "You do not have sufficient permissions"
**Solution:** Ensure you're logged in as an Administrator or have the `manage_provincial_slideshows` capability.

#### Issue: Database tables not created during activation
**Possible Causes:**
- Database user doesn't have CREATE TABLE permissions
- WordPress database connection issues
- Plugin activation didn't complete properly

**Solutions:**
1. Use the manual "Create Database Tables" button
2. Contact your hosting provider about database permissions
3. Try deactivating and reactivating the plugin

#### Issue: Tables exist but still getting errors
**Possible Causes:**
- Database corruption
- Incorrect table structure
- Character encoding issues

**Solutions:**
1. Drop the existing tables and recreate them
2. Check database character encoding (should be utf8mb4)
3. Contact your hosting provider

### Testing the Fix:

After implementing any fix:

1. Go to Provincial Administration → Add Slideshow
2. Try creating a test slideshow group:
   - Name: "Test Slideshow"
   - Description: "Testing slideshow functionality"
   - Tags: "test"
3. Click "Create Slideshow Group"
4. If successful, you should be redirected to the slideshows list
5. You can then add slides to your test slideshow

### Getting Additional Help:

If the issue persists:

1. **Check WordPress Error Logs**
   - Look in `/wp-content/debug.log` for PHP errors
   - Check your hosting control panel for error logs

2. **Gather Information**
   - WordPress version
   - PHP version
   - Database version (MySQL/MariaDB)
   - Hosting provider
   - Any error messages from debug information

3. **Test Environment**
   - Try on a staging site if available
   - Test with default WordPress theme
   - Deactivate other plugins temporarily

### Prevention:

To avoid future issues:
- Keep WordPress and plugins updated
- Regular database backups
- Monitor error logs
- Test changes on staging environment first

---

**Note:** This slideshow system is integrated into the Provincial Administration Manager plugin. All troubleshooting should be done with administrator privileges and proper backups in place.
