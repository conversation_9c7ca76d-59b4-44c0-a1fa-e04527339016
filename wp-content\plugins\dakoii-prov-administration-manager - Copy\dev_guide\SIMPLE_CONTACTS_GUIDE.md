# Simple Contacts System - Implementation Guide

## Overview
The contacts system has been simplified to work like custom posts (similar to MPs, Governors, etc.). Users can easily create multiple contacts and use shortcodes to display them.

## ✅ **What's Implemented**

### **Contact Custom Post Type**
- **Location**: WordPress Admin → Posts → Contacts
- **Features**: Title, content, thumbnail, contact meta fields
- **Management**: Just like managing MPs or other posts

### **Contact Meta Fields**
Each contact includes:
- **Address**: Physical office address
- **Phone Number**: Main contact phone
- **Fax Number**: Fax number
- **Emergency Contact**: Emergency contact number
- **Email Address**: General inquiries email
- **Administrative Email**: Official business email
- **Website**: Website URL
- **Office Hours**: Operating hours

### **Shortcode System**
- **Contact Posts**: `[dakoii_contact id="123"]` (where 123 is the contact post ID)
- **District Contacts**: `[dakoii_contact district_id="456"]` (where 456 is the district post ID)
- **Layout Options**: Add `layout="list"` or `layout="grid"`

## 🚀 **How to Use**

### Step 1: Create a Contact
1. Go to WordPress Admin
2. Navigate to **Posts → Contacts** (or click "Add New Contact" from Contact Management page)
3. Fill in:
   - **Title**: Contact name (e.g., "Emergency Services", "Tourism Office")
   - **Content**: Optional description
   - **Featured Image**: Optional contact photo/logo
   - **Contact Details**: Fill in the contact meta box with address, phone, email, etc.
4. **Publish** the contact

### Step 2: Get the Contact ID
- After publishing, note the contact ID from the URL: `post.php?post=123` (123 is the ID)
- Or check the Contact Management page for a list with IDs

### Step 3: Use the Shortcode
```
[dakoii_contact id="123"]
```
Replace 123 with your actual contact ID.

## 📋 **Available Shortcodes**

### Contact Posts
```
[dakoii_contact id="123"]                    // Basic contact display
[dakoii_contact id="123" layout="list"]      // List layout
[dakoii_contact id="123" layout="grid"]      // Grid layout (default)
```

### District Contacts (Unchanged)
```
[dakoii_contact district_id="456"]           // District contact
[dakoii_district_contact id="456"]           // Alternative syntax
```

### Alternative Naming Conventions
All these work identically:
- **Primary**: `dakoii_contact`, `dakoii_district_contact`, `dakoii_provincial_contact`
- **Generic**: `provincial_contact`, `provincial_district_contact`
- **Legacy**: `esp_contact`, `esp_district_contact`

## 💡 **Example Use Cases**

### Emergency Services Contact
1. Create contact post titled "Emergency Services"
2. Fill in emergency contact details
3. Use: `[dakoii_contact id="123"]`

### Tourism Office Contact
1. Create contact post titled "Tourism Information Office"
2. Add tourism office details and hours
3. Use: `[dakoii_contact id="124"]`

### Health Department Contact
1. Create contact post titled "Provincial Health Department"
2. Include health department contact information
3. Use: `[dakoii_contact id="125"]`

## 🔧 **Management Features**

### Contact Management Page
- **Location**: WordPress Admin → Contact Information
- **Features**:
  - List of all existing contacts
  - Quick links to add/edit contacts
  - Shortcode display with copy functionality
  - Usage instructions

### WordPress Posts Interface
- **Location**: WordPress Admin → Posts → Contacts
- **Features**:
  - Standard WordPress post management
  - Bulk actions (delete, edit, etc.)
  - Search and filter contacts
  - Quick edit functionality

## 🎨 **Layout Options**

### Grid Layout (Default)
```
[dakoii_contact id="123" layout="grid"]
```
Displays contact information in responsive cards.

### List Layout
```
[dakoii_contact id="123" layout="list"]
```
Displays contact information in a vertical list format.

## 🔒 **Permissions & Security**

- Same permission system as other custom posts
- Users can only edit contacts they have permission for
- All data is properly sanitized and validated
- Standard WordPress security practices

## 📊 **Database Structure**

### Contact Posts
- **Post Type**: `esp_contact`
- **Post Title**: Contact name
- **Post Content**: Optional description
- **Post Meta**: Contact information fields

### Meta Fields
- `_esp_contact_address`
- `_esp_contact_phone`
- `_esp_contact_fax`
- `_esp_contact_emergency`
- `_esp_contact_email`
- `_esp_contact_admin_email`
- `_esp_contact_website`
- `_esp_contact_office_hours`

## 🧪 **Testing Your Implementation**

### Test 1: Create a Contact
1. Go to Posts → Contacts → Add New
2. Title: "Test Department"
3. Fill in some contact information
4. Publish the contact
5. Note the contact ID from the URL

### Test 2: Use the Shortcode
1. Create a new page or post
2. Add shortcode: `[dakoii_contact id="YOUR_CONTACT_ID"]`
3. Preview/publish the page
4. Verify contact information displays correctly

### Test 3: Try Different Layouts
1. Test grid: `[dakoii_contact id="123" layout="grid"]`
2. Test list: `[dakoii_contact id="123" layout="list"]`

## 🆘 **Troubleshooting**

### Contact Not Displaying
- ✅ Check the contact ID is correct
- ✅ Ensure the contact is published
- ✅ Verify contact has some information filled in
- ✅ Check shortcode syntax

### Shortcode Not Working
- ✅ Check for typos in the shortcode
- ✅ Ensure the contact post exists
- ✅ Try with a different contact ID
- ✅ Clear any caching plugins

### Permission Issues
- ✅ Check user has permission to view contacts
- ✅ Verify contact is published (not draft)
- ✅ Test with admin user

## 🎉 **Benefits of the New System**

✅ **Simple Management**: Works like any WordPress post  
✅ **Familiar Interface**: Uses standard WordPress editing  
✅ **Unlimited Contacts**: Create as many as needed  
✅ **Easy Shortcodes**: Simple ID-based shortcodes  
✅ **Consistent Experience**: Matches other post types in the plugin  
✅ **Flexible Content**: Support for descriptions and images  
✅ **Standard Features**: Search, bulk actions, quick edit, etc.  

## 🔄 **Migration Notes**

- District contacts continue to work unchanged
- No breaking changes to existing shortcodes
- New contact system is completely separate and simple
- Users can gradually migrate to the new system

The simplified contact system provides a much cleaner, more intuitive experience that follows WordPress best practices and matches the existing plugin architecture!
