<?php
/**
 * Provincial MPs Controller
 * 
 * Handles CRUD operations for MPs with role-based access control
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_MPs_Controller {
    
    private static $instance = null;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_ajax_esp_mp_create', array($this, 'handle_create'));
        add_action('wp_ajax_esp_mp_update', array($this, 'handle_update'));
        add_action('wp_ajax_esp_mp_delete', array($this, 'handle_delete'));
        add_action('wp_ajax_esp_mp_get', array($this, 'handle_get'));
    }
    
    /**
     * Get MPs based on user permissions
     */
    public function get_mps_for_user($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $user_type = get_user_meta($user_id, 'provincial_user_type', true);
        $is_district_user = ($user_type === 'district');
        
        // Check if user is admin or has provincial access
        if (current_user_can('manage_options') || current_user_can('administrator') || 
            Provincial_User_Roles::user_has_provincial_access($user_id)) {
            // Admin/Provincial users see all MPs
            return get_posts(array(
                'post_type' => 'esp_mp',
                'numberposts' => -1,
                'post_status' => 'any',
                'orderby' => 'title',
                'order' => 'ASC'
            ));
        } elseif ($is_district_user) {
            // District users see only MPs from their assigned districts
            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user_id);

            // Debug: Log the assigned districts for troubleshooting
            if (isset($_GET['debug'])) {
                error_log('ESP Debug - District User ID: ' . $user_id);
                error_log('ESP Debug - Assigned Districts: ' . print_r($assigned_districts, true));
            }

            if (!empty($assigned_districts)) {
                // First, let's get all MPs to see what we have
                $all_mps = get_posts(array(
                    'post_type' => 'esp_mp',
                    'numberposts' => -1,
                    'post_status' => 'any',
                    'orderby' => 'title',
                    'order' => 'ASC'
                ));

                // Debug: Log all MPs and their district assignments
                if (isset($_GET['debug'])) {
                    error_log('ESP Debug - Total MPs found: ' . count($all_mps));
                    foreach ($all_mps as $mp) {
                        $mp_district_id = get_post_meta($mp->ID, '_esp_mp_district_id', true);
                        error_log('ESP Debug - MP: ' . $mp->post_title . ' (ID: ' . $mp->ID . ') - District ID: ' . ($mp_district_id ?: 'None'));
                    }
                }

                // Filter MPs manually to ensure we catch any data type issues
                $filtered_mps = array();
                foreach ($all_mps as $mp) {
                    $mp_district_id = get_post_meta($mp->ID, '_esp_mp_district_id', true);

                    // Check both string and integer comparisons
                    if (!empty($mp_district_id) &&
                        (in_array($mp_district_id, $assigned_districts) ||
                         in_array(strval($mp_district_id), array_map('strval', $assigned_districts)) ||
                         in_array(intval($mp_district_id), array_map('intval', $assigned_districts)))) {
                        $filtered_mps[] = $mp;
                    }
                }

                // Debug: Log filtered results
                if (isset($_GET['debug'])) {
                    error_log('ESP Debug - Filtered MPs count: ' . count($filtered_mps));
                    foreach ($filtered_mps as $mp) {
                        error_log('ESP Debug - Filtered MP: ' . $mp->post_title);
                    }
                }

                return $filtered_mps;
            }
        }
        
        return array();
    }
    
    /**
     * Check if user can manage MP
     */
    public function user_can_manage_mp($mp_id, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        // Admin users can manage all MPs
        if (current_user_can('manage_options') || current_user_can('administrator') || 
            Provincial_User_Roles::user_has_provincial_access($user_id)) {
            return true;
        }
        
        $user_type = get_user_meta($user_id, 'provincial_user_type', true);
        
        // District users can only manage MPs from their assigned districts
        if ($user_type === 'district') {
            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user_id);
            $mp_district_id = get_post_meta($mp_id, '_esp_mp_district_id', true);
            
            return !empty($mp_district_id) && in_array($mp_district_id, $assigned_districts);
        }
        
        return false;
    }
    
    /**
     * Create new MP
     */
    public function create_mp($data) {
        // Verify nonce
        if (!wp_verify_nonce($data['nonce'], 'esp_mp_nonce')) {
            return new WP_Error('invalid_nonce', __('Security check failed.', 'esp-admin-manager'));
        }
        
        // Check permissions
        if (!current_user_can('edit_posts')) {
            return new WP_Error('insufficient_permissions', __('You do not have permission to create MPs.', 'esp-admin-manager'));
        }
        
        // Validate required fields
        if (empty($data['name'])) {
            return new WP_Error('missing_name', __('MP name is required.', 'esp-admin-manager'));
        }
        
        // For district users, validate district assignment
        $user_id = get_current_user_id();
        $user_type = get_user_meta($user_id, 'provincial_user_type', true);
        
        if ($user_type === 'district' && !current_user_can('manage_options') && !current_user_can('administrator')) {
            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user_id);
            
            if (empty($data['district_id']) || !in_array($data['district_id'], $assigned_districts)) {
                return new WP_Error('invalid_district', __('You can only create MPs for your assigned districts.', 'esp-admin-manager'));
            }
        }
        
        // Create MP post
        $mp_data = array(
            'post_title' => sanitize_text_field($data['name']),
            'post_content' => wp_kses_post($data['message']),
            'post_status' => 'publish',
            'post_type' => 'esp_mp'
        );
        
        $mp_id = wp_insert_post($mp_data);
        
        if (is_wp_error($mp_id)) {
            return $mp_id;
        }
        
        // Save meta data
        if (!empty($data['electorate'])) {
            update_post_meta($mp_id, '_esp_mp_electorate', sanitize_text_field($data['electorate']));
        }
        
        if (!empty($data['party'])) {
            update_post_meta($mp_id, '_esp_mp_party', sanitize_text_field($data['party']));
        }
        
        if (!empty($data['district_id'])) {
            update_post_meta($mp_id, '_esp_mp_district_id', intval($data['district_id']));
        }
        
        if (!empty($data['bio'])) {
            update_post_meta($mp_id, '_esp_mp_message', sanitize_textarea_field($data['bio']));
        }
        
        // Handle photo upload
        if (!empty($data['photo_id'])) {
            set_post_thumbnail($mp_id, intval($data['photo_id']));
        }
        
        return $mp_id;
    }
    
    /**
     * Update MP
     */
    public function update_mp($mp_id, $data) {
        // Verify nonce
        if (!wp_verify_nonce($data['nonce'], 'esp_mp_nonce')) {
            return new WP_Error('invalid_nonce', __('Security check failed.', 'esp-admin-manager'));
        }
        
        // Check if MP exists
        $mp = get_post($mp_id);
        if (!$mp || $mp->post_type !== 'esp_mp') {
            return new WP_Error('mp_not_found', __('MP not found.', 'esp-admin-manager'));
        }
        
        // Check permissions
        if (!$this->user_can_manage_mp($mp_id)) {
            return new WP_Error('insufficient_permissions', __('You do not have permission to edit this MP.', 'esp-admin-manager'));
        }
        
        // Validate required fields
        if (empty($data['name'])) {
            return new WP_Error('missing_name', __('MP name is required.', 'esp-admin-manager'));
        }
        
        // Update MP post
        $mp_data = array(
            'ID' => $mp_id,
            'post_title' => sanitize_text_field($data['name']),
            'post_content' => wp_kses_post($data['message'])
        );
        
        $result = wp_update_post($mp_data);
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        // Update meta data
        if (isset($data['electorate'])) {
            update_post_meta($mp_id, '_esp_mp_electorate', sanitize_text_field($data['electorate']));
        }
        
        if (isset($data['party'])) {
            update_post_meta($mp_id, '_esp_mp_party', sanitize_text_field($data['party']));
        }
        
        if (isset($data['district_id'])) {
            update_post_meta($mp_id, '_esp_mp_district_id', intval($data['district_id']));
        }
        
        if (isset($data['bio'])) {
            update_post_meta($mp_id, '_esp_mp_message', sanitize_textarea_field($data['bio']));
        }
        
        // Handle photo upload
        if (isset($data['photo_id'])) {
            if (!empty($data['photo_id'])) {
                set_post_thumbnail($mp_id, intval($data['photo_id']));
            } else {
                delete_post_thumbnail($mp_id);
            }
        }
        
        return $mp_id;
    }
    
    /**
     * Delete MP
     */
    public function delete_mp($mp_id) {
        // Check if MP exists
        $mp = get_post($mp_id);
        if (!$mp || $mp->post_type !== 'esp_mp') {
            return new WP_Error('mp_not_found', __('MP not found.', 'esp-admin-manager'));
        }
        
        // Check permissions - only admin users can delete MPs
        if (!current_user_can('manage_options') && !current_user_can('administrator') && 
            !Provincial_User_Roles::user_has_provincial_access()) {
            return new WP_Error('insufficient_permissions', __('You do not have permission to delete MPs.', 'esp-admin-manager'));
        }
        
        // Move to trash
        $result = wp_trash_post($mp_id);
        
        if (!$result) {
            return new WP_Error('delete_failed', __('Failed to delete MP.', 'esp-admin-manager'));
        }
        
        return true;
    }
    
    /**
     * Get all districts for dropdown
     */
    public function get_districts_for_user($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $user_type = get_user_meta($user_id, 'provincial_user_type', true);
        
        // Admin/Provincial users see all districts
        if (current_user_can('manage_options') || current_user_can('administrator') || 
            Provincial_User_Roles::user_has_provincial_access($user_id)) {
            return get_posts(array(
                'post_type' => 'esp_district',
                'numberposts' => -1,
                'post_status' => 'publish',
                'orderby' => 'title',
                'order' => 'ASC'
            ));
        } elseif ($user_type === 'district') {
            // District users see only their assigned districts
            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($user_id);
            
            if (!empty($assigned_districts)) {
                return get_posts(array(
                    'post_type' => 'esp_district',
                    'numberposts' => -1,
                    'post_status' => 'publish',
                    'post__in' => $assigned_districts,
                    'orderby' => 'title',
                    'order' => 'ASC'
                ));
            }
        }
        
        return array();
    }
    
    /**
     * AJAX handler for create
     */
    public function handle_create() {
        $result = $this->create_mp($_POST);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array('mp_id' => $result));
        }
    }
    
    /**
     * AJAX handler for update
     */
    public function handle_update() {
        $mp_id = intval($_POST['mp_id']);
        $result = $this->update_mp($mp_id, $_POST);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array('mp_id' => $result));
        }
    }
    
    /**
     * AJAX handler for delete
     */
    public function handle_delete() {
        if (!wp_verify_nonce($_POST['nonce'], 'esp_mp_nonce')) {
            wp_send_json_error(__('Security check failed.', 'esp-admin-manager'));
        }
        
        $mp_id = intval($_POST['mp_id']);
        $result = $this->delete_mp($mp_id);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success();
        }
    }
    
    /**
     * AJAX handler for get
     */
    public function handle_get() {
        if (!wp_verify_nonce($_POST['nonce'], 'esp_mp_nonce')) {
            wp_send_json_error(__('Security check failed.', 'esp-admin-manager'));
        }
        
        $mp_id = intval($_POST['mp_id']);
        $mp = get_post($mp_id);
        
        if (!$mp || $mp->post_type !== 'esp_mp') {
            wp_send_json_error(__('MP not found.', 'esp-admin-manager'));
        }
        
        if (!$this->user_can_manage_mp($mp_id)) {
            wp_send_json_error(__('You do not have permission to view this MP.', 'esp-admin-manager'));
        }
        
        $mp_data = array(
            'id' => $mp->ID,
            'name' => $mp->post_title,
            'message' => $mp->post_content,
            'electorate' => get_post_meta($mp->ID, '_esp_mp_electorate', true),
            'party' => get_post_meta($mp->ID, '_esp_mp_party', true),
            'district_id' => get_post_meta($mp->ID, '_esp_mp_district_id', true),
            'bio' => get_post_meta($mp->ID, '_esp_mp_message', true),
            'photo_url' => get_the_post_thumbnail_url($mp->ID, 'medium'),
            'photo_id' => get_post_thumbnail_id($mp->ID)
        );
        
        wp_send_json_success($mp_data);
    }
}
