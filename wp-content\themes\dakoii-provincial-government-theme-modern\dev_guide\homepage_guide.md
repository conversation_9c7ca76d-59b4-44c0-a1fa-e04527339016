<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>East Sepik Provincial Administration - Papua New Guinea</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --png-red: #CE1126;
            --png-green: #006A4E;
            --png-yellow: #FFD700;
            --dark-green: #004d3a;
            --light-green: #00a86b;
            --cream: #FFF8DC;
            --dark-brown: #8B4513;
            --official-blue: #1e3a8a;
            --light-gray: #f8fafc;
            --medium-gray: #64748b;
        }

        body {
            font-family: 'Georgia', serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, var(--png-green) 0%, var(--dark-green) 100%);
            overflow-x: hidden;
        }

        /* Header */
        .site-header {
            background: linear-gradient(45deg, var(--png-red) 0%, var(--png-red) 50%, var(--png-green) 50%, var(--png-green) 100%);
            color: white;
            padding: 1rem 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
        }

        .site-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,20 Q50,5 80,20 Q65,50 80,80 Q50,65 20,80 Q35,50 20,20 Z" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="2"/></svg>') repeat;
            opacity: 0.3;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .gov-logo {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo-shield {
            width: 70px;
            height: 70px;
            background: var(--png-yellow);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: var(--png-red);
            border: 3px solid white;
            position: relative;
        }

        .logo-shield::before {
            content: '🏛️';
            position: absolute;
        }

        .gov-title {
            font-size: 2.2rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.2;
        }

        .gov-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            font-style: italic;
        }

        .emergency-contact {
            text-align: right;
            font-size: 0.9rem;
        }

        .emergency-number {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--png-yellow);
        }

        /* Navigation */
        .main-nav {
            background: rgba(0, 106, 78, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 3px solid var(--png-yellow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            padding: 1rem 0;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-item a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1.2rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .nav-item a:hover {
            background: var(--png-red);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(206, 17, 38, 0.4);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect width="1200" height="600" fill="%23006A4E"/><path d="M0,300 Q300,200 600,300 T1200,300 L1200,600 L0,600 Z" fill="%23004d3a"/><circle cx="200" cy="150" r="80" fill="%23FFD700" opacity="0.3"/><circle cx="1000" cy="400" r="120" fill="%23CE1126" opacity="0.2"/></svg>');
            background-size: cover;
            background-position: center;
            padding: 5rem 0;
            text-align: center;
            color: white;
            position: relative;
        }

        .hero-content {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
            animation: fadeInUp 1s ease-out;
        }

        .hero-subtitle {
            font-size: 1.4rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .hero-stat {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--png-yellow);
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Quick Services Section */
        .quick-services {
            padding: 4rem 0;
            background: var(--light-gray);
        }

        .section-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            text-align: center;
            font-size: 2.8rem;
            color: var(--png-green);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.1rem;
            color: var(--medium-gray);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .service-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-top: 4px solid var(--png-yellow);
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
            transition: left 0.5s;
        }

        .service-card:hover::before {
            left: 100%;
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .service-icon {
            width: 70px;
            height: 70px;
            background: var(--png-red);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1.5rem;
            position: relative;
            z-index: 2;
        }

        .service-title {
            font-size: 1.4rem;
            color: var(--png-green);
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .service-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .service-link {
            display: inline-block;
            color: var(--png-red);
            text-decoration: none;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            border: 2px solid var(--png-red);
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .service-link:hover {
            background: var(--png-red);
            color: white;
            transform: translateY(-2px);
        }

        /* Government Information */
        .gov-info-section {
            padding: 5rem 0;
            background: white;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .info-content h2 {
            font-size: 2.5rem;
            color: var(--png-green);
            margin-bottom: 1.5rem;
        }

        .info-content p {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 1.5rem;
        }

        .gov-highlights {
            list-style: none;
            margin: 2rem 0;
        }

        .gov-highlights li {
            padding: 0.8rem 0;
            position: relative;
            padding-left: 2.5rem;
            border-bottom: 1px solid #eee;
        }

        .gov-highlights li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--png-green);
            font-weight: bold;
            font-size: 1.2rem;
        }

        .info-visual {
            background: linear-gradient(135deg, var(--png-red), var(--png-yellow));
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .info-visual::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><rect x="50" y="50" width="100" height="100" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/><circle cx="100" cy="100" r="30" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/></svg>') repeat;
            opacity: 0.3;
        }

        .province-highlight {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }

        .province-highlight h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
        }

        /* News & Announcements */
        .news-section {
            padding: 5rem 0;
            background: var(--light-gray);
        }

        .news-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
        }

        .news-main {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .news-featured {
            height: 250px;
            background: linear-gradient(45deg, var(--png-green), var(--png-red));
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }

        .news-content {
            padding: 2rem;
        }

        .news-date {
            color: var(--png-red);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .news-title {
            font-size: 1.8rem;
            color: var(--png-green);
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        .news-excerpt {
            color: #666;
            line-height: 1.7;
            margin-bottom: 1.5rem;
        }

        .news-sidebar {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .sidebar-item {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid var(--png-yellow);
        }

        .sidebar-title {
            font-size: 1.1rem;
            color: var(--png-green);
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .sidebar-content {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        /* Contact Information */
        .contact-section {
            padding: 4rem 0;
            background: var(--png-green);
            color: white;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--png-yellow);
        }

        .contact-title {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--png-yellow);
        }

        .contact-info {
            line-height: 1.6;
            opacity: 0.9;
        }

        /* Footer */
        .site-footer {
            background: var(--dark-green);
            color: white;
            padding: 3rem 0 1rem;
            position: relative;
        }

        .site-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--png-red), var(--png-yellow), var(--png-green));
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: var(--png-yellow);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .footer-section p,
        .footer-section a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            line-height: 1.6;
            display: block;
            margin-bottom: 0.5rem;
        }

        .footer-section a:hover {
            color: var(--png-yellow);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0.8;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .service-card,
        .sidebar-item {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .gov-title {
                font-size: 1.8rem;
            }

            .emergency-contact {
                text-align: center;
            }

            .nav-menu {
                gap: 0.5rem;
            }

            .nav-item a {
                font-size: 0.85rem;
                padding: 0.4rem 1rem;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .info-grid,
            .news-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .section-title {
                font-size: 2.2rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Accessibility */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--png-yellow);
            color: var(--dark-green);
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 1000;
        }

        .skip-link:focus {
            top: 6px;
        }
    </style>
</head>
<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Header -->
    <header class="site-header">
        <div class="header-content">
            <div class="gov-logo">
                <div class="logo-shield"></div>
                <div>
                    <h1 class="gov-title">East Sepik Provincial Administration</h1>
                    <p class="gov-subtitle">Papua New Guinea Government</p>
                </div>
            </div>
            <div class="emergency-contact">
                <div>Emergency Services</div>
                <div class="emergency-number">000</div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="main-nav">
        <div class="nav-container">
            <ul class="nav-menu">
                <li class="nav-item"><a href="#home">Home</a></li>
                <li class="nav-item"><a href="#services">Services</a></li>
                <li class="nav-item"><a href="#departments">Departments</a></li>
                <li class="nav-item"><a href="#projects">Projects</a></li>
                <li class="nav-item"><a href="#budget">Budget</a></li>
                <li class="nav-item"><a href="#news">News</a></li>
                <li class="nav-item"><a href="#documents">Documents</a></li>
                <li class="nav-item"><a href="#contact">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="hero-content">
            <h1 class="hero-title">Serving East Sepik Province</h1>
            <p class="hero-subtitle">Committed to transparent governance, sustainable development, and improving the lives of all citizens</p>
            
            <div class="hero-stats">
                <div class="hero-stat">
                    <div class="stat-number">450K+</div>
                    <div class="stat-label">Population</div>
                </div>
                <div class="hero-stat">
                    <div class="stat-number">43,426</div>
                    <div class="stat-label">Area (km²)</div>
                </div>
                <div class="hero-stat">
                    <div class="stat-number">6</div>
                    <div class="stat-label">Districts</div>
                </div>
                <div class="hero-stat">
                    <div class="stat-number">41</div>
                    <div class="stat-label">LLGs</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Services Section -->
    <section class="quick-services" id="services">
        <div class="section-container">
            <h2 class="section-title">Government Services</h2>
            <p class="section-subtitle">Access essential provincial government services and information</p>
            
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">📋</div>
                    <h3 class="service-title">Business Registration</h3>
                    <p class="service-description">Register your business, obtain licenses, and access regulatory information for commercial activities in East Sepik Province.</p>
                    <a href="#" class="service-link">Apply Online →</a>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">🏥</div>
                    <h3 class="service-title">Health Services</h3>
                    <p class="service-description">Find information about provincial health facilities, programs, and access health services across our districts.</p>
                    <a href="#" class="service-link">Learn More →</a>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">🎓</div>
                    <h3 class="service-title">Education & Training</h3>
                    <p class="service-description">Access educational resources, school information, and professional development opportunities.</p>
                    <a href="#" class="service-link">Explore →</a>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">🚧</div>
                    <h3 class="service-title">Infrastructure Projects</h3>
                    <p class="service-description">Stay updated on provincial infrastructure development, road maintenance, and public works projects.</p>
                    <a href="#" class="service-link">View Projects →</a>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">💰</div>
                    <h3 class="service-title">Grants & Funding</h3>
                    <p class="service-description">Apply for provincial grants, community funding, and development assistance programs.</p>
                    <a href="#" class="service-link">Apply Now →</a>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">📄</div>
                    <h3 class="service-title">Official Documents</h3>
                    <p class="service-description">Request certificates, permits, and official documents from provincial government offices.</p>
                    <a href="#" class="service-link">Request →</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Government Information -->
    <section class="gov-info-section" id="main-content">
        <div class="section-container">
            <div class="info-grid">
                <div class="info-content">
                    <h2>About East Sepik Provincial Government</h2>
                    <p>The East Sepik Provincial Administration is committed to delivering quality services to our citizens while preserving our rich cultural heritage and promoting sustainable economic development.</p>
                    
                    <p>Our province encompasses the mighty Sepik River basin and is home to diverse communities with unique traditions, languages, and customs. We work to balance modern governance with traditional values.</p>
                    
                    <ul class="gov-highlights">
                        <li>Transparent and accountable governance</li>
                        <li>Community-centered development programs</li>
                        <li>Cultural preservation initiatives</li>
                        <li>Sustainable environmental management</li>
                        <li>Economic opportunities for all citizens</li>
                        <li>Quality healthcare and education services</li>
                    </ul>
                </div>
                
                <div class="info-visual">
                    <div class="province-highlight">
                        <h3>Provincial Vision</h3>
                        <p>"A progressive, self-reliant East Sepik Province where all citizens enjoy quality life through good governance, sustainable development, and cultural preservation."</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- News & Announcements -->
    <section class="news-section" id="news">
        <div class="section-container">
            <h2 class="section-title">Latest News & Announcements</h2>
            
            <div class="news-grid">
                <article class="news-main">
                    <div class="news-featured">📰</div>
                    <div class="news-content">
                        <div class="news-date">August 19, 2025</div>
                        <h3 class="news-title">Provincial Budget 2025-2026 Approved</h3>
                        <p class="news-excerpt">The East Sepik Provincial Assembly has approved the K850 million budget for the 2025-2026 financial year, with major allocations for infrastructure development, healthcare, and education. The budget focuses on rural development and improving access to essential services...</p>
                        <a href="#" class="service-link">Read Full Story →</a>
                    </div>
                </article>
                
                <aside class="news-sidebar">
                    <div class="sidebar-item">
                        <h4 class="sidebar-title">Public Notice</h4>
                        <p class="sidebar-content">Applications for the 2025 Provincial Development Grants are now open. Deadline: September 15, 2025.</p>
                    </div>
                    
                    <div class="sidebar-item">
                        <h4 class="sidebar-title">Infrastructure Update</h4>
                        <p class="sidebar-content">Wewak-Maprik Highway reconstruction project is 65% complete and on schedule for December 2025.</p>
                    </div>
                    
                    <div class="sidebar-item">
                        <h4 class="sidebar-title">Health Alert</h4>
                        <p class="sidebar-content">Free vaccination program continues at all district health centers. Check schedules at your local facility.</p>
                    </div>
                    
                    <div class="sidebar-item">
                        <h4 class="sidebar-title">Cultural Event</h4>
                        <p class="sidebar-content">East Sepik Cultural Festival scheduled for September 16-18, 2025 in Wewak.</p>
                    </div>
                </aside>
            </div>
        </div>
    </section>

    <!-- Contact Information -->
    <section class="contact-section" id="contact">
        <div class="section-container">
            <h2 class="section-title">Contact Provincial Government</h2>
            
            <div class="contact-grid">
                <div class="contact-card">
                    <div class="contact-icon">🏛️</div>
                    <h3 class="contact-title">Provincial Headquarters</h3>
                    <div class="contact-info">
                        Wewak, East Sepik Province<br>
                        Papua New Guinea<br>
                        PO Box 280, Wewak
                    </div>
                </div>
                
                <div class="contact-card">
                    <div class="contact-icon">📞</div>
                    <h3 class="contact-title">Phone & Fax</h3>
                    <div class="contact-info">
                        Phone: (+*************<br>
                        Fax: (+*************<br>
                        Emergency: 000
                    </div>
                </div>
                
                <div class="contact-card">
                    <div class="contact-icon">✉️</div>
                    <h3 class="contact-title">Email & Web</h3>
                    <div class="contact-info">
                        <EMAIL><br>
                        <EMAIL><br>
                        www.eastsepik.gov.pg
                    </div>
                </div>
                
                <div class="contact-card">
                    <div class="contact-icon">🕐</div>
                    <h3 class="contact-title">Office Hours</h3>
                    <div class="contact-info">
                        Monday - Friday<br>
                        8:00 AM - 4:30 PM<br>
                        Closed Weekends & Public Holidays
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="section-container">
            <div class="footer-grid">
                <div class="footer-section">
                    <h3>East Sepik Provincial Government</h3>
                    <p>Committed to serving the people of East Sepik Province through transparent governance, sustainable development, and preservation of our cultural heritage.</p>
                    <p>© 2025 East Sepik Provincial Administration. All rights reserved.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Government Services</h3>
                    <a href="#services">Online Services</a>
                    <a href="#departments">Government Departments</a>
                    <a href="#documents">Official Documents</a>
                    <a href="#budget">Budget & Finance</a>
                    <a href="#projects">Development Projects</a>
                </div>
                
                <div class="footer-section">
                    <h3>Information</h3>
                    <a href="#news">News & Updates</a>
                    <a href="#announcements">Public Announcements</a>
                    <a href="#events">Government Events</a>
                    <a href="#policies">Policies & Regulations</a>
                    <a href="#reports">Annual Reports</a>
                </div>
                
                <div class="footer-section">
                    <h3>Connect With Us</h3>
                    <a href="#facebook">Facebook</a>
                    <a href="#twitter">Twitter</a>
                    <a href="#newsletter">Newsletter Signup</a>
                    <a href="#feedback">Citizen Feedback</a>
                    <a href="#contact">Contact Information</a>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>East Sepik Provincial Administration | Papua New Guinea Government</p>
                <p>Building a better future for all citizens of East Sepik Province</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animations
        window.addEventListener('load', function() {
            const animatedElements = document.querySelectorAll('.service-card, .sidebar-item, .contact-card');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });
        });

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.service-card, .sidebar-item, .contact-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Mobile menu toggle (if needed for responsive design)
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle) {
            navToggle.addEventListener('click', function() {
                navMenu.classList.toggle('active');
            });
        }

        // Form validation for newsletter (if present)
        const newsletterForm = document.querySelector('.newsletter-form');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const email = this.querySelector('input[type="email"]').value;
                if (email) {
                    alert('Thank you for subscribing to East Sepik Provincial updates!');
                    this.reset();
                }
            });
        }

        // Accessibility improvements
        document.addEventListener('keydown', function(e) {
            // Escape key closes any open modals/dropdowns
            if (e.key === 'Escape') {
                document.querySelectorAll('.dropdown-open, .modal-open').forEach(el => {
                    el.classList.remove('dropdown-open', 'modal-open');
                });
            }
        });

        // Add focus indicators for keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
    </script>

    <!-- Additional CSS for keyboard navigation -->
    <style>
        .keyboard-navigation *:focus {
            outline: 3px solid var(--png-yellow) !important;
            outline-offset: 2px !important;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            :root {
                --png-red: #FF0000;
                --png-green: #00AA00;
                --png-yellow: #FFFF00;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Print styles */
        @media print {
            .nav-menu,
            .hero-section,
            .contact-section {
                display: none;
            }
            
            body {
                background: white;
                color: black;
            }
            
            .service-card,
            .news-main,
            .sidebar-item {
                box-shadow: none;
                border: 1px solid #ccc;
            }
        }
    </style>
</body>
</html>