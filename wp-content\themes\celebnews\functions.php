<?php
if (!function_exists('celebnews_theme_enqueue_styles')) {
    add_action('wp_enqueue_scripts', 'celebnews_theme_enqueue_styles');

    function celebnews_theme_enqueue_styles()
    {
        $celebnews_version = wp_get_theme()->get('Version');
        $min = defined('SCRIPT_DEBUG') && SCRIPT_DEBUG ? '' : '.min';
        $parent_style = 'morenews-style';
        wp_enqueue_style('bootstrap', get_template_directory_uri() . '/assets/bootstrap/css/bootstrap' . $min . '.css');
        wp_enqueue_style($parent_style, get_template_directory_uri() . '/style.css');
        wp_enqueue_style(
            'celebnews',
            get_stylesheet_directory_uri() . '/style.css',
            array('bootstrap', $parent_style),
            wp_get_theme()->get('Version')
        );

        // Enqueue RTL Styles if the site is in RTL mode
        if (is_rtl()) {
            wp_enqueue_style(
                'morenews-rtl',
                get_template_directory_uri() . '/rtl.css',
                array($parent_style),
                $celebnews_version
            );
        }

        // Add custom background styles
        celebnews_add_background_styles();
    }
}

function celebnews_custom_header_setup($default_custom_header)
{
    $default_custom_header['default-text-color'] = 'ffffff';
    return $default_custom_header;
}
add_filter('morenews_custom_header_args', 'celebnews_custom_header_setup', 1);

function celebnews_filter_default_theme_options($defaults)
{
    $defaults['global_site_mode_setting']    = 'aft-dark-mode';
    $defaults['dark_background_color']     = '#000000';
    $defaults['show_popular_tags_section']  = 1;
    $defaults['select_popular_tags_mode']  = 'category';
    $defaults['site_title_font_size'] = 38;
    $defaults['site_title_uppercase']  = 0;
    $defaults['show_primary_menu_desc']  = 0;
    $defaults['select_header_image_mode']  = 'above';
    $defaults['disable_header_image_tint_overlay']  = 1;
    $defaults['flash_news_title'] = __('Breaking News', 'celebnews');
    $defaults['aft_custom_title']           = __('Subscribe', 'celebnews');
    $defaults['select_main_banner_layout_section'] = 'layout-1';
    $defaults['select_main_banner_order'] = 'order-1';
    $defaults['select_update_post_filterby'] = 'cat';
    $defaults['secondary_color'] = '#FFCC00';
    $defaults['global_show_min_read'] = 'no';
    $defaults['frontpage_content_type']  = 'frontpage-widgets-and-content';
    $defaults['featured_news_section_title'] = __('Featured News', 'celebnews');
    $defaults['show_featured_post_list_section']  = 1;
    $defaults['featured_post_list_section_title_1']           = __('General News', 'celebnews');
    $defaults['featured_post_list_section_title_2']           = __('Global News', 'celebnews');
    $defaults['featured_post_list_section_title_3']           = __('More News', 'celebnews');

    // Add background image options
    $defaults['celebnews_background_image'] = '';
    $defaults['celebnews_background_repeat'] = 'no-repeat';
    $defaults['celebnews_background_position'] = 'center center';
    $defaults['celebnews_background_size'] = 'cover';
    $defaults['celebnews_background_attachment'] = 'scroll';
    $defaults['celebnews_enable_background_image'] = false;

    return $defaults;
}
add_filter('morenews_filter_default_theme_options', 'celebnews_filter_default_theme_options', 1);

/**
 * Add background image customizer settings
 */
function celebnews_customize_register($wp_customize) {
    // Add Background Image Section
    $wp_customize->add_section('celebnews_background_image_section', array(
        'title'    => __('Background Image Settings', 'celebnews'),
        'priority' => 30,
        'panel'    => 'morenews_theme_option_panel', // Use parent theme panel if available
    ));

    // Enable Background Image
    $wp_customize->add_setting('celebnews_theme_options[celebnews_enable_background_image]', array(
        'default'           => false,
        'sanitize_callback' => 'morenews_sanitize_checkbox',
        'type'              => 'option',
    ));

    $wp_customize->add_control('celebnews_theme_options[celebnews_enable_background_image]', array(
        'label'    => __('Enable Background Image', 'celebnews'),
        'section'  => 'celebnews_background_image_section',
        'type'     => 'checkbox',
        'priority' => 10,
    ));

    // Background Image Upload
    $wp_customize->add_setting('celebnews_theme_options[celebnews_background_image]', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
        'type'              => 'option',
    ));

    $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'celebnews_theme_options[celebnews_background_image]', array(
        'label'    => __('Background Image', 'celebnews'),
        'section'  => 'celebnews_background_image_section',
        'priority' => 20,
    )));

    // Background Repeat
    $wp_customize->add_setting('celebnews_theme_options[celebnews_background_repeat]', array(
        'default'           => 'no-repeat',
        'sanitize_callback' => 'celebnews_sanitize_background_repeat',
        'type'              => 'option',
    ));

    $wp_customize->add_control('celebnews_theme_options[celebnews_background_repeat]', array(
        'label'    => __('Background Repeat', 'celebnews'),
        'section'  => 'celebnews_background_image_section',
        'type'     => 'select',
        'choices'  => array(
            'no-repeat' => __('No Repeat', 'celebnews'),
            'repeat'    => __('Repeat', 'celebnews'),
            'repeat-x'  => __('Repeat Horizontally', 'celebnews'),
            'repeat-y'  => __('Repeat Vertically', 'celebnews'),
        ),
        'priority' => 30,
    ));

    // Background Position
    $wp_customize->add_setting('celebnews_theme_options[celebnews_background_position]', array(
        'default'           => 'center center',
        'sanitize_callback' => 'celebnews_sanitize_background_position',
        'type'              => 'option',
    ));

    $wp_customize->add_control('celebnews_theme_options[celebnews_background_position]', array(
        'label'    => __('Background Position', 'celebnews'),
        'section'  => 'celebnews_background_image_section',
        'type'     => 'select',
        'choices'  => array(
            'left top'      => __('Left Top', 'celebnews'),
            'left center'   => __('Left Center', 'celebnews'),
            'left bottom'   => __('Left Bottom', 'celebnews'),
            'center top'    => __('Center Top', 'celebnews'),
            'center center' => __('Center Center', 'celebnews'),
            'center bottom' => __('Center Bottom', 'celebnews'),
            'right top'     => __('Right Top', 'celebnews'),
            'right center'  => __('Right Center', 'celebnews'),
            'right bottom'  => __('Right Bottom', 'celebnews'),
        ),
        'priority' => 40,
    ));

    // Background Size
    $wp_customize->add_setting('celebnews_theme_options[celebnews_background_size]', array(
        'default'           => 'cover',
        'sanitize_callback' => 'celebnews_sanitize_background_size',
        'type'              => 'option',
    ));

    $wp_customize->add_control('celebnews_theme_options[celebnews_background_size]', array(
        'label'    => __('Background Size', 'celebnews'),
        'section'  => 'celebnews_background_image_section',
        'type'     => 'select',
        'choices'  => array(
            'auto'    => __('Auto', 'celebnews'),
            'cover'   => __('Cover', 'celebnews'),
            'contain' => __('Contain', 'celebnews'),
        ),
        'priority' => 50,
    ));

    // Background Attachment
    $wp_customize->add_setting('celebnews_theme_options[celebnews_background_attachment]', array(
        'default'           => 'scroll',
        'sanitize_callback' => 'celebnews_sanitize_background_attachment',
        'type'              => 'option',
    ));

    $wp_customize->add_control('celebnews_theme_options[celebnews_background_attachment]', array(
        'label'    => __('Background Attachment', 'celebnews'),
        'section'  => 'celebnews_background_image_section',
        'type'     => 'select',
        'choices'  => array(
            'scroll' => __('Scroll', 'celebnews'),
            'fixed'  => __('Fixed', 'celebnews'),
        ),
        'priority' => 60,
    ));
}
add_action('customize_register', 'celebnews_customize_register');

/**
 * Sanitization functions for background image options
 */
function celebnews_sanitize_background_repeat($input) {
    $valid = array('no-repeat', 'repeat', 'repeat-x', 'repeat-y');
    return in_array($input, $valid) ? $input : 'no-repeat';
}

function celebnews_sanitize_background_position($input) {
    $valid = array(
        'left top', 'left center', 'left bottom',
        'center top', 'center center', 'center bottom',
        'right top', 'right center', 'right bottom'
    );
    return in_array($input, $valid) ? $input : 'center center';
}

function celebnews_sanitize_background_size($input) {
    $valid = array('auto', 'cover', 'contain');
    return in_array($input, $valid) ? $input : 'cover';
}

function celebnews_sanitize_background_attachment($input) {
    $valid = array('scroll', 'fixed');
    return in_array($input, $valid) ? $input : 'scroll';
}

/**
 * Add custom background styles to the page
 */
function celebnews_add_background_styles() {
    $theme_options = get_option('celebnews_theme_options');

    // Check if background image is enabled
    $enable_bg_image = isset($theme_options['celebnews_enable_background_image']) ? $theme_options['celebnews_enable_background_image'] : false;

    if (!$enable_bg_image) {
        return;
    }

    $bg_image = isset($theme_options['celebnews_background_image']) ? $theme_options['celebnews_background_image'] : '';

    if (empty($bg_image)) {
        return;
    }

    $bg_repeat = isset($theme_options['celebnews_background_repeat']) ? $theme_options['celebnews_background_repeat'] : 'no-repeat';
    $bg_position = isset($theme_options['celebnews_background_position']) ? $theme_options['celebnews_background_position'] : 'center center';
    $bg_size = isset($theme_options['celebnews_background_size']) ? $theme_options['celebnews_background_size'] : 'cover';
    $bg_attachment = isset($theme_options['celebnews_background_attachment']) ? $theme_options['celebnews_background_attachment'] : 'scroll';

    $custom_css = "
        body {
            background-image: url('" . esc_url($bg_image) . "');
            background-repeat: " . esc_attr($bg_repeat) . ";
            background-position: " . esc_attr($bg_position) . ";
            background-size: " . esc_attr($bg_size) . ";
            background-attachment: " . esc_attr($bg_attachment) . ";
        }

        /* Ensure content areas have proper background for readability */
        body.aft-dark-mode main.site-main,
        body.aft-dark-mode .morenews-widget,
        body.aft-dark-mode .morenews-customizer,
        body.aft-dark-mode .af-middle-header,
        body.aft-dark-mode .mid-header-wrapper {
            background-color: rgba(17, 17, 17, 0.95);
        }

        body.aft-default-mode main.site-main,
        body.aft-default-mode .morenews-widget,
        body.aft-default-mode .morenews-customizer,
        body.aft-default-mode .af-middle-header,
        body.aft-default-mode .mid-header-wrapper {
            background-color: rgba(255, 255, 255, 0.95);
        }
    ";

    wp_add_inline_style('celebnews', $custom_css);
}