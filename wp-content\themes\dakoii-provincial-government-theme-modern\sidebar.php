<?php
/**
 * The sidebar containing the main widget area
 *
 * @package Nols_ESPA_Theme_Two
 */

if (!is_active_sidebar('sidebar-1')) {
    return;
}
?>

<aside class="sidebar" id="secondary" role="complementary">
    <div class="widget-area">
        <?php if (is_active_sidebar('sidebar-1')) : ?>
            <?php dynamic_sidebar('sidebar-1'); ?>
        <?php else : ?>
            
            <!-- Default Recent Posts Widget -->
            <div class="widget">
                <h3 class="widget-title"><?php esc_html_e('Recent Posts', 'nols-espa-theme-two'); ?></h3>
                <div class="widget-content">
                    <ul>
                        <?php
                        $recent_posts = wp_get_recent_posts(array(
                            'numberposts' => 5,
                            'post_status' => 'publish'
                        ));
                        
                        foreach ($recent_posts as $post) :
                        ?>
                            <li>
                                <a href="<?php echo esc_url(get_permalink($post['ID'])); ?>">
                                    <?php echo esc_html($post['post_title']); ?>
                                </a>
                                <span class="post-date">
                                    <?php echo esc_html(get_the_date('', $post['ID'])); ?>
                                </span>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
            
            <!-- Default Categories Widget -->
            <div class="widget">
                <h3 class="widget-title"><?php esc_html_e('Cultural Categories', 'nols-espa-theme-two'); ?></h3>
                <div class="widget-content">
                    <ul>
                        <?php
                        $categories = get_categories(array(
                            'orderby' => 'name',
                            'order'   => 'ASC',
                            'number'  => 8,
                        ));
                        
                        if ($categories) :
                            foreach ($categories as $category) :
                        ?>
                                <li>
                                    <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>">
                                        <?php echo esc_html($category->name); ?>
                                        <span class="post-count">(<?php echo $category->count; ?>)</span>
                                    </a>
                                </li>
                        <?php 
                            endforeach;
                        else :
                        ?>
                            <li><a href="#"><?php esc_html_e('Traditional Arts', 'nols-espa-theme-two'); ?></a></li>
                            <li><a href="#"><?php esc_html_e('Ceremonies & Rituals', 'nols-espa-theme-two'); ?></a></li>
                            <li><a href="#"><?php esc_html_e('Oral Traditions', 'nols-espa-theme-two'); ?></a></li>
                            <li><a href="#"><?php esc_html_e('Music & Dance', 'nols-espa-theme-two'); ?></a></li>
                            <li><a href="#"><?php esc_html_e('Local History', 'nols-espa-theme-two'); ?></a></li>
                            <li><a href="#"><?php esc_html_e('Community Events', 'nols-espa-theme-two'); ?></a></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
            
            <!-- Cultural Pattern Widget -->
            <div class="widget">
                <h3 class="widget-title"><?php esc_html_e('Cultural Pattern', 'nols-espa-theme-two'); ?></h3>
                <div class="cultural-pattern"></div>
            </div>
            
            <!-- Connect With Us Widget -->
            <div class="widget">
                <h3 class="widget-title"><?php esc_html_e('Connect With Us', 'nols-espa-theme-two'); ?></h3>
                <div class="widget-content">
                    <p><?php esc_html_e('Join our community to stay updated on cultural events, workshops, and celebrations throughout East Sepik Province.', 'nols-espa-theme-two'); ?></p>
                    <ul>
                        <li><a href="#" target="_blank" rel="noopener"><?php esc_html_e('Facebook Community', 'nols-espa-theme-two'); ?></a></li>
                        <li><a href="#" target="_blank" rel="noopener"><?php esc_html_e('Newsletter Signup', 'nols-espa-theme-two'); ?></a></li>
                        <li><a href="#" target="_blank" rel="noopener"><?php esc_html_e('Event Calendar', 'nols-espa-theme-two'); ?></a></li>
                        <li><a href="mailto:<EMAIL>"><?php esc_html_e('Contact Us', 'nols-espa-theme-two'); ?></a></li>
                    </ul>
                </div>
            </div>
            
            <!-- Search Widget -->
            <div class="widget">
                <h3 class="widget-title"><?php esc_html_e('Search', 'nols-espa-theme-two'); ?></h3>
                <div class="widget-content">
                    <?php get_search_form(); ?>
                </div>
            </div>
            
            <!-- Archives Widget -->
            <div class="widget">
                <h3 class="widget-title"><?php esc_html_e('Archives', 'nols-espa-theme-two'); ?></h3>
                <div class="widget-content">
                    <ul>
                        <?php wp_get_archives(array(
                            'type'            => 'monthly',
                            'limit'           => 6,
                            'format'          => 'html',
                            'before'          => '<li>',
                            'after'           => '</li>',
                            'show_post_count' => true,
                        )); ?>
                    </ul>
                </div>
            </div>
            
            <!-- Tags Widget -->
            <?php
            $tags = get_tags(array(
                'orderby' => 'count',
                'order'   => 'DESC',
                'number'  => 15,
            ));
            
            if ($tags) :
            ?>
                <div class="widget">
                    <h3 class="widget-title"><?php esc_html_e('Popular Tags', 'nols-espa-theme-two'); ?></h3>
                    <div class="widget-content">
                        <div class="tag-cloud">
                            <?php foreach ($tags as $tag) : ?>
                                <a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>" class="tag-link">
                                    <?php echo esc_html($tag->name); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
        <?php endif; ?>
    </div>
</aside><!-- #secondary -->
