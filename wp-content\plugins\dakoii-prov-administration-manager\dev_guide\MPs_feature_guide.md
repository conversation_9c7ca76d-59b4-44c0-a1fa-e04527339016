# MPs Feature Implementation Guide

## Overview

This guide documents the complete implementation of the Members of Parliament (MPs) feature in the Dakoii Provincial Administration Manager plugin. The MPs feature allows administrators to manage MP profiles and display them on the frontend using flexible shortcodes.

## Feature Architecture

### Core Components

1. **Custom Post Type**: `esp_mp`
2. **Meta Fields**: Electorate, Party, District, Bio
3. **Content Structure**: Post content as MP message, meta field as bio
4. **District Linking**: MPs are linked to specific districts
5. **Frontend Display**: Multiple shortcode options with district filtering
6. **Admin Interface**: Management and preview interface

### Data Structure

```php
// MP Post Type: 'esp_mp'
// Post Title: MP Name
// Post Content: MP Message (public-facing message to constituents)
// Post Thumbnail: MP Photo

// Meta Fields:
'_esp_mp_electorate' => 'Wewak Open'     // Constituency
'_esp_mp_party'      => 'Pangu Party'    // Political party
'_esp_mp_district_id'=> 123               // Linked district ID
'_esp_mp_message'    => 'Bio text...'    // Biographical information
```

## Implementation Steps

### Step 1: Register Custom Post Type

**File**: `includes/class-provincial-post-types.php`

```php
private function register_mp_post_type() {
    $args = array(
        'public'             => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'supports'           => array('title', 'editor', 'thumbnail'),
        'labels'             => array(
            'name'               => 'MPs',
            'singular_name'      => 'MP',
            'add_new_item'       => 'Add New MP',
            'edit_item'          => 'Edit MP',
            'all_items'          => 'All MPs',
        ),
    );

    register_post_type('esp_mp', $args);
}
```

### Step 2: Create Meta Boxes

**File**: `includes/class-provincial-meta-boxes.php`

```php
// Add meta box
add_meta_box(
    'provincial_mp_details',
    __('MP Details', 'esp-admin-manager'),
    array($this, 'mp_meta_box_callback'),
    'esp_mp',
    'normal',
    'high'
);

// Meta box callback
public function mp_meta_box_callback($post) {
    wp_nonce_field('provincial_mp_meta_box', 'provincial_mp_meta_box_nonce');

    $electorate = get_post_meta($post->ID, '_esp_mp_electorate', true);
    $party = get_post_meta($post->ID, '_esp_mp_party', true);
    $message = get_post_meta($post->ID, '_esp_mp_message', true);
    ?>
    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="esp_mp_electorate"><?php _e('Electorate', 'esp-admin-manager'); ?></label>
            </th>
            <td>
                <input type="text" id="esp_mp_electorate" name="esp_mp_electorate"
                       value="<?php echo esc_attr($electorate); ?>" class="regular-text" />
                <p class="description"><?php _e('e.g., Wewak Open, Provincial', 'esp-admin-manager'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="esp_mp_party"><?php _e('Political Party', 'esp-admin-manager'); ?></label>
            </th>
            <td>
                <input type="text" id="esp_mp_party" name="esp_mp_party"
                       value="<?php echo esc_attr($party); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="esp_mp_message"><?php _e('MP Bio', 'esp-admin-manager'); ?></label>
            </th>
            <td>
                <textarea id="esp_mp_message" name="esp_mp_message" rows="4"
                          class="large-text"><?php echo esc_textarea($message); ?></textarea>
                <p class="description"><?php _e('Optional biographical information about the MP', 'esp-admin-manager'); ?></p>
            </td>
        </tr>
    </table>
    <?php
}
```

### Step 3: Save Meta Data

```php
// Save MP meta
if ($post_type === 'esp_mp' && isset($_POST['provincial_mp_meta_box_nonce']) &&
    wp_verify_nonce($_POST['provincial_mp_meta_box_nonce'], 'provincial_mp_meta_box')) {

    update_post_meta($post_id, '_esp_mp_electorate', sanitize_text_field($_POST['esp_mp_electorate']));
    update_post_meta($post_id, '_esp_mp_party', sanitize_text_field($_POST['esp_mp_party']));
    update_post_meta($post_id, '_esp_mp_message', sanitize_textarea_field($_POST['esp_mp_message']));
}
```

### Step 4: Frontend Data Function

**File**: `includes/class-provincial-frontend.php`

```php
public static function get_mps($limit = -1) {
    $mps = get_posts(array(
        'post_type' => 'esp_mp',
        'numberposts' => $limit,
        'post_status' => 'publish',
        'orderby' => 'title',
        'order' => 'ASC'
    ));

    $mps_data = array();

    foreach ($mps as $mp) {
        $mps_data[] = array(
            'id' => $mp->ID,
            'name' => $mp->post_title,
            'bio' => get_post_meta($mp->ID, '_esp_mp_message', true), // Bio from meta field
            'electorate' => get_post_meta($mp->ID, '_esp_mp_electorate', true),
            'party' => get_post_meta($mp->ID, '_esp_mp_party', true),
            'message' => $mp->post_content, // Message from post content
            'photo' => get_the_post_thumbnail_url($mp->ID, 'thumbnail')
        );
    }

    return $mps_data;
}
```

## Shortcode Implementation

### Step 5: Register Shortcodes

**File**: `includes/class-provincial-shortcodes.php`

```php
public function register_shortcodes() {
    // Primary shortcodes
    add_shortcode('dakoii_prov_admin_mps', array($this, 'mps_shortcode'));
    add_shortcode('dakoii_prov_admin_mp', array($this, 'single_mp_shortcode'));
    add_shortcode('dakoii_prov_admin_mp_only', array($this, 'single_mp_shortcode'));

    // Alternative shortcodes
    add_shortcode('dakoii_mps', array($this, 'mps_shortcode'));
    add_shortcode('dakoii_mp', array($this, 'single_mp_shortcode'));
    add_shortcode('dakoii_mp_only', array($this, 'single_mp_shortcode'));

    // Legacy shortcodes
    add_shortcode('esp_mps', array($this, 'mps_shortcode'));
    add_shortcode('esp_mp', array($this, 'single_mp_shortcode'));
    add_shortcode('esp_mp_only', array($this, 'single_mp_shortcode'));
}
```

### Step 6: MPs Grid Shortcode

```php
public function mps_shortcode($atts) {
    $atts = shortcode_atts(array(
        'limit' => -1,
        'show_photos' => 'true',
        'show_messages' => 'false',
        'level' => 'district',
        'district_id' => ''
    ), $atts);

    // Permission check
    $district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
    if (!$this->check_shortcode_permission('mps', $atts['level'], $district_id)) {
        return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
    }

    $mps = get_posts(array(
        'post_type' => 'esp_mp',
        'numberposts' => intval($atts['limit']),
        'post_status' => 'publish',
        'orderby' => 'title',
        'order' => 'ASC'
    ));

    // Filter content by user access
    $mps = $this->filter_content_by_district_access($mps);

    if (empty($mps)) {
        return '<p>' . __('No MPs found.', 'esp-admin-manager') . '</p>';
    }

    ob_start();
    ?>
    <section class="esp-parliament-section">
        <div class="esp-mp-grid">
            <?php foreach ($mps as $mp):
                $electorate = get_post_meta($mp->ID, '_esp_mp_electorate', true);
                $party = get_post_meta($mp->ID, '_esp_mp_party', true);
                $message = $mp->post_content; // Message from post content
            ?>
                <div class="esp-mp-card">
                    <?php if ($atts['show_photos'] === 'true'): ?>
                        <div class="esp-mp-photo">
                            <?php if (has_post_thumbnail($mp->ID)): ?>
                                <?php echo get_the_post_thumbnail($mp->ID, 'thumbnail'); ?>
                            <?php else: ?>
                                👤
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <div class="esp-mp-name"><?php echo esc_html($mp->post_title); ?></div>

                    <?php if ($electorate): ?>
                        <div class="esp-mp-electorate"><?php echo esc_html($electorate); ?></div>
                    <?php endif; ?>

                    <?php if ($party): ?>
                        <div class="esp-mp-party"><?php echo esc_html($party); ?></div>
                    <?php endif; ?>

                    <?php if ($atts['show_messages'] === 'true' && $message): ?>
                        <div class="esp-mp-message"><?php echo wp_kses_post(wpautop($message)); ?></div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
    </section>
    <?php
    return ob_get_clean();
}
```

### Step 7: Single MP Shortcode

```php
public function single_mp_shortcode($atts, $content = null, $tag = '') {
    $atts = shortcode_atts(array(
        'id' => '',
        'show_photo' => 'true',
        'show_message' => 'true',
        'level' => 'district',
        'district_id' => ''
    ), $atts);

    // If called via *_mp_only shortcode, disable message display
    if (strpos($tag, '_mp_only') !== false) {
        $atts['show_message'] = 'false';
    }

    // Check if MP ID is provided
    if (empty($atts['id'])) {
        return '<p>' . __('MP ID is required. Usage: [dakoii_mp id="123"]', 'esp-admin-manager') . '</p>';
    }

    // Permission check
    $district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
    if (!$this->check_shortcode_permission('mps', $atts['level'], $district_id)) {
        return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
    }

    // Get the specific MP
    $mp = get_post(intval($atts['id']));

    if (!$mp || $mp->post_type !== 'esp_mp' || $mp->post_status !== 'publish') {
        return '<p>' . __('MP not found or not published.', 'esp-admin-manager') . '</p>';
    }

    // Filter content by user access
    $mps_array = array($mp);
    $filtered_mps = $this->filter_content_by_district_access($mps_array);

    if (empty($filtered_mps)) {
        return '<p>' . __('You do not have permission to view this MP.', 'esp-admin-manager') . '</p>';
    }

    // Get MP data
    $electorate = get_post_meta($mp->ID, '_esp_mp_electorate', true);
    $party = get_post_meta($mp->ID, '_esp_mp_party', true);
    $message = $mp->post_content; // Message from post content

    ob_start();
    ?>
    <section class="esp-parliament-section esp-single-mp">
        <div class="esp-mp-card esp-single-mp-card">
            <?php if ($atts['show_photo'] === 'true'): ?>
                <div class="esp-mp-photo">
                    <?php if (has_post_thumbnail($mp->ID)): ?>
                        <?php echo get_the_post_thumbnail($mp->ID, 'medium'); ?>
                    <?php else: ?>
                        👤
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <div class="esp-mp-details">
                <div class="esp-mp-name"><?php echo esc_html($mp->post_title); ?></div>

                <?php if ($electorate): ?>
                    <div class="esp-mp-electorate"><?php echo esc_html($electorate); ?></div>
                <?php endif; ?>

                <?php if ($party): ?>
                    <div class="esp-mp-party"><?php echo esc_html($party); ?></div>
                <?php endif; ?>

                <?php if ($atts['show_message'] === 'true' && $message): ?>
                    <div class="esp-mp-message"><?php echo wp_kses_post(wpautop($message)); ?></div>
                <?php endif; ?>
            </div>
        </div>
    </section>
    <?php
    return ob_get_clean();
}
```

## CSS Styling

### Step 8: Frontend Styles

**File**: `public/css/public-style.css`

```css
/* Parliament Members Section */
.esp-parliament-section {
    padding: 2rem 0;
    background: var(--light-gray);
}

.esp-mp-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-mp-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--png-red);
}

.esp-mp-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.esp-mp-photo {
    width: 80px;
    height: 80px;
    background: var(--png-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1rem;
    overflow: hidden;
}

.esp-mp-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    display: block;
}

.esp-mp-name {
    font-size: 1.2rem;
    color: var(--png-green);
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.esp-mp-electorate {
    color: var(--png-red);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.esp-mp-party {
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.esp-mp-message {
    font-size: 0.9rem;
    line-height: 1.6;
    color: #555;
    margin-top: 1rem;
    padding: 1rem;
    background: var(--cream);
    border-radius: 8px;
    border-left: 3px solid var(--png-yellow);
    font-style: italic;
}

/* Single MP Display */
.esp-single-mp {
    max-width: 800px;
    margin: 0 auto;
}

.esp-single-mp-card {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    align-items: start;
    text-align: left;
    padding: 2rem;
}

.esp-single-mp-card .esp-mp-photo {
    width: 120px;
    height: 120px;
    font-size: 3rem;
}

.esp-single-mp-card .esp-mp-details {
    flex: 1;
}

.esp-single-mp-card .esp-mp-name {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.esp-single-mp-card .esp-mp-message {
    margin-top: 1.5rem;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .esp-single-mp-card {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
        padding: 1.5rem;
    }

    .esp-single-mp-card .esp-mp-photo {
        width: 100px;
        height: 100px;
        margin: 0 auto 1rem;
    }

    .esp-single-mp-card .esp-mp-name {
        font-size: 1.3rem;
    }

    .esp-single-mp-card .esp-mp-message {
        margin-top: 1rem;
        padding: 0.8rem;
    }
}
```

## Admin Interface

### Step 9: Admin View

**File**: `admin/views/mps.php`

Key features of the admin interface:

1. **MPs List Table**: Displays all MPs with Photo, Name, Electorate, Party, Message, Status, and Actions
2. **Quick Actions**: Links to add new MP and manage all MPs
3. **Preview Section**: Shows how MPs appear on the website
4. **Shortcode Documentation**: Examples and usage instructions

```php
// Get all MPs
$mps = get_posts(array(
    'post_type' => 'esp_mp',
    'numberposts' => -1,
    'post_status' => 'any',
    'orderby' => 'title',
    'order' => 'ASC'
));

// Display table with MP information
foreach ($mps as $mp) {
    $electorate = get_post_meta($mp->ID, '_esp_mp_electorate', true);
    $party = get_post_meta($mp->ID, '_esp_mp_party', true);
    $message = $mp->post_content; // Message from post content

    // Display MP row with all information
}
```

### Step 10: Default Data Creation

**File**: `dakoii-provincial-admin-manager.php`

```php
private function create_default_mps() {
    $mps_data = array(
        array(
            'name' => 'Hon. Allan Bird',
            'electorate' => 'Provincial',
            'party' => 'Pangu Party'
        ),
        array(
            'name' => 'Hon. Gabriel Kapris',
            'electorate' => 'Wewak Open',
            'party' => 'PNC'
        ),
        // ... more MPs
    );

    foreach ($mps_data as $mp_data) {
        $mp_exists = get_posts(array(
            'post_type' => 'esp_mp',
            'title' => $mp_data['name'],
            'numberposts' => 1
        ));

        if (empty($mp_exists)) {
            $mp_id = wp_insert_post(array(
                'post_title' => $mp_data['name'],
                'post_content' => 'Committed to serving the people of ' . $mp_data['electorate'] . ' and working towards the development of our province.',
                'post_status' => 'publish',
                'post_type' => 'esp_mp'
            ));

            if ($mp_id) {
                update_post_meta($mp_id, '_esp_mp_electorate', $mp_data['electorate']);
                update_post_meta($mp_id, '_esp_mp_party', $mp_data['party']);
            }
        }
    }
}
```

## Usage Examples

### Shortcode Options

#### All MPs Display

```html
<!-- Basic MPs grid -->
[dakoii_mps]

<!-- MPs with photos hidden -->
[dakoii_mps show_photos="false"]

<!-- MPs with messages shown -->
[dakoii_mps show_messages="true"]

<!-- MPs with district names shown -->
[dakoii_mps show_districts="true"]

<!-- MPs from specific district only -->
[dakoii_mps district_id="123"]

<!-- Limited number of MPs -->
[dakoii_mps limit="6"]

<!-- Combined options -->
[dakoii_mps limit="4" show_messages="true" show_photos="true" show_districts="true"]

<!-- District-specific MPs with all info -->
[dakoii_mps district_id="456" show_messages="true" show_districts="true"]
```

#### Individual MP Display

```html
<!-- Single MP with message -->
[dakoii_mp id="123"]

<!-- Single MP without message -->
[dakoii_mp_only id="123"]

<!-- Single MP with photo hidden -->
[dakoii_mp id="123" show_photo="false"]
```

#### Alternative Shortcode Names

```html
<!-- Primary (recommended) -->
[dakoii_prov_admin_mps]
[dakoii_prov_admin_mp id="123"]

<!-- Alternative (shorter) -->
[dakoii_mps]
[dakoii_mp id="123"]

<!-- Legacy (backward compatibility) -->
[esp_mps]
[esp_mp id="123"]
```

### Finding MP IDs

MP IDs can be found in several ways:

1. **Edit URL**: When editing an MP, the ID appears in the URL: `post.php?post=123&action=edit`
2. **Admin Table**: Hover over MP name to see the ID in the status bar
3. **Database**: Query the `wp_posts` table for `post_type = 'esp_mp'`

## Content Structure Guidelines

### Post Content (MP Message)

The main WordPress editor should contain the MP's public message to constituents:

- **Purpose**: Primary communication to the public
- **Content**: Vision, priorities, commitments, policy positions
- **Tone**: Professional, accessible, engaging
- **Length**: 2-4 paragraphs recommended

**Example**:
```
As your representative in Parliament, I am committed to advancing the interests of our district and ensuring that the voices of our people are heard at the national level.

My priorities include improving healthcare access, supporting education initiatives, and promoting sustainable economic development that benefits all residents of our constituency.

I believe in transparent governance and regular communication with the community. Please feel free to reach out to my office with your concerns and suggestions.
```

### Meta Field (MP Bio)

The bio field should contain factual biographical information:

- **Purpose**: Background information about the MP
- **Content**: Education, career history, experience, achievements
- **Tone**: Factual, professional
- **Length**: 1-2 paragraphs recommended

**Example**:
```
Hon. John Smith holds a Bachelor's degree in Political Science from the University of Papua New Guinea and has served in various community leadership roles for over 15 years.

Prior to his election to Parliament, he worked as a community development officer and was actively involved in local business associations and youth programs.
```

## Best Practices

### Development Guidelines

1. **Data Sanitization**: Always sanitize user input using appropriate WordPress functions
   - `sanitize_text_field()` for text inputs
   - `sanitize_textarea_field()` for textarea content
   - `sanitize_email()` for email addresses

2. **Security**: Use nonces for form submissions and verify permissions
   - Include nonce fields in meta boxes
   - Verify nonces before saving data
   - Check user capabilities

3. **Internationalization**: Use translation functions for all user-facing text
   - `__()` for simple strings
   - `_e()` for echoed strings
   - Include text domain: `'esp-admin-manager'`

4. **Performance**: Optimize database queries
   - Use `get_posts()` with specific parameters
   - Limit results when appropriate
   - Cache results when possible

### Content Management

1. **Photo Guidelines**:
   - Use high-quality, professional photos
   - Recommended size: 300x300px minimum
   - Format: JPG or PNG
   - Consistent lighting and background

2. **Content Guidelines**:
   - Keep messages concise and engaging
   - Update content regularly
   - Use proper grammar and spelling
   - Maintain professional tone

3. **Data Accuracy**:
   - Verify electorate names and spellings
   - Keep party affiliations current
   - Update after elections
   - Review biographical information periodically

### Accessibility

1. **Image Alt Text**: Ensure all MP photos have descriptive alt text
2. **Semantic HTML**: Use proper heading structure and semantic elements
3. **Color Contrast**: Maintain sufficient contrast ratios
4. **Keyboard Navigation**: Ensure all interactive elements are keyboard accessible

## Troubleshooting

### Common Issues

#### Shortcode Not Displaying

**Problem**: Shortcode appears as text instead of rendered content

**Solutions**:
1. Check if shortcode is properly registered
2. Verify shortcode name spelling
3. Ensure plugin is activated
4. Check for PHP errors in error logs

#### MPs Not Appearing

**Problem**: No MPs shown in shortcode output

**Solutions**:
1. Verify MPs are published (not draft)
2. Check user permissions
3. Confirm post type is 'esp_mp'
4. Review district access filters

#### Styling Issues

**Problem**: MPs display without proper styling

**Solutions**:
1. Check if CSS files are properly enqueued
2. Verify CSS selectors match HTML structure
3. Clear any caching plugins
4. Check for CSS conflicts with theme

#### Permission Errors

**Problem**: "You do not have permission" messages

**Solutions**:
1. Review user role capabilities
2. Check district access settings
3. Verify shortcode level parameter
4. Confirm user is logged in if required

### Debug Mode

Enable WordPress debug mode to identify issues:

```php
// In wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Database Queries

Useful queries for troubleshooting:

```sql
-- Find all MPs
SELECT * FROM wp_posts WHERE post_type = 'esp_mp';

-- Find MP meta data
SELECT * FROM wp_postmeta WHERE meta_key LIKE '_esp_mp_%';

-- Check MP with specific ID
SELECT p.*, pm.meta_key, pm.meta_value
FROM wp_posts p
LEFT JOIN wp_postmeta pm ON p.ID = pm.post_id
WHERE p.ID = 123 AND p.post_type = 'esp_mp';
```

## File Structure Summary

```
dakoii-prov-administration-manager/
├── includes/
│   ├── class-provincial-post-types.php     # Custom post type registration
│   ├── class-provincial-meta-boxes.php     # Meta boxes and save functions
│   ├── class-provincial-frontend.php       # Frontend data functions
│   └── class-provincial-shortcodes.php     # Shortcode implementations
├── admin/
│   ├── views/mps.php                       # Admin interface
│   └── js/admin-script.js                  # Admin JavaScript
├── public/
│   ├── css/public-style.css                # Frontend styling
│   └── js/public-script.js                 # Frontend JavaScript
└── dev_guide/
    └── MPs_feature_guide.md                # This guide
```

## Conclusion

This implementation provides a complete, flexible, and maintainable MPs feature that follows WordPress best practices. The modular approach allows for easy extension and modification while maintaining backward compatibility and security standards.

Key benefits of this approach:

1. **Flexibility**: Multiple shortcode options for different display needs
2. **Security**: Proper sanitization and permission checks
3. **Performance**: Optimized queries and caching considerations
4. **Maintainability**: Clean, well-documented code structure
5. **User Experience**: Intuitive admin interface and responsive frontend
6. **Accessibility**: Semantic HTML and accessibility considerations
7. **Internationalization**: Full translation support

The feature is designed to grow with the needs of the provincial administration while maintaining simplicity for end users.