<?php
/**
 * Provincial Administration Manager - Edit District View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get district ID from URL
$district_id = isset($_GET['district_id']) ? intval($_GET['district_id']) : 0;

if (!$district_id) {
    wp_die(__('Invalid district ID.', 'esp-admin-manager'));
}

// Get district controller
$districts_controller = Provincial_Districts_Controller::get_instance();

// Get district with permission check
$district = $districts_controller->get_district($district_id);

if (!$district) {
    wp_die(__('District not found or you do not have permission to edit this district.', 'esp-admin-manager'));
}

// Get district meta data
$district_llgs = get_post_meta($district_id, '_esp_district_llgs', true);
$district_wards = get_post_meta($district_id, '_esp_district_wards', true);
$district_population = get_post_meta($district_id, '_esp_district_population', true);
$district_area = get_post_meta($district_id, '_esp_district_area', true);
$district_page_url = get_post_meta($district_id, '_esp_district_page_url', true);
$district_photo_id = get_post_thumbnail_id($district_id);
$district_photo_url = get_the_post_thumbnail_url($district_id, 'medium');

// Get current user information
$current_user_id = get_current_user_id();
$user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
$is_district_user = ($user_type === 'district');
$is_admin_user = current_user_can('manage_options') || current_user_can('administrator');

// Form submission is handled by the admin class
?>

<div class="esp-custom-admin-wrap">
    <!-- Header Section -->
    <div class="esp-admin-header">
        <div class="esp-header-content">
            <h1 class="esp-page-title">
                <span class="esp-icon">✏️</span>
                <?php _e('Edit District', 'esp-admin-manager'); ?>
            </h1>
            <p class="esp-page-description">
                <?php printf(__('Editing: %s', 'esp-admin-manager'), '<strong>' . esc_html($district->post_title) . '</strong>'); ?>
            </p>
        </div>
        <div class="esp-header-actions">
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts'); ?>" class="esp-btn esp-btn-secondary">
                <span class="esp-btn-icon">←</span>
                <?php _e('Back to Districts', 'esp-admin-manager'); ?>
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php settings_errors('esp_messages'); ?>

    <?php if ($is_district_user && !$is_admin_user): ?>
        <div class="esp-info-banner">
            <div class="esp-info-content">
                <span class="esp-info-icon">ℹ️</span>
                <div class="esp-info-text">
                    <strong><?php _e('District User Access:', 'esp-admin-manager'); ?></strong>
                    <?php _e('You can edit the information for this district as it is assigned to you.', 'esp-admin-manager'); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Edit District Form -->
    <div class="esp-content-section">
        <div class="esp-form-container">
            <form method="post" class="esp-form" id="edit-district-form">
                <?php wp_nonce_field('edit_district_' . $district_id, 'district_nonce'); ?>
                <input type="hidden" name="action" value="update_district">
                <input type="hidden" name="district_id" value="<?php echo esc_attr($district_id); ?>">

                <div class="esp-form-section">
                    <h3 class="esp-section-title"><?php _e('Basic Information', 'esp-admin-manager'); ?></h3>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-name" class="esp-form-label">
                                <?php _e('District Name', 'esp-admin-manager'); ?> <span class="esp-required">*</span>
                            </label>
                            <input type="text" id="district-name" name="district_name" class="esp-form-input"
                                   value="<?php echo isset($_POST['district_name']) ? esc_attr($_POST['district_name']) : esc_attr($district->post_title); ?>" required>
                            <p class="esp-form-help"><?php _e('Enter the official name of the district', 'esp-admin-manager'); ?></p>
                        </div>
                    </div>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-description" class="esp-form-label">
                                <?php _e('Description', 'esp-admin-manager'); ?>
                            </label>
                            <textarea id="district-description" name="district_description" class="esp-form-textarea" rows="4"><?php echo isset($_POST['district_description']) ? esc_textarea($_POST['district_description']) : esc_textarea($district->post_content); ?></textarea>
                            <p class="esp-form-help"><?php _e('Provide a brief description of the district', 'esp-admin-manager'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="esp-form-section">
                    <h3 class="esp-section-title"><?php _e('Administrative Details', 'esp-admin-manager'); ?></h3>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-llgs" class="esp-form-label">
                                <?php _e('Number of LLGs', 'esp-admin-manager'); ?>
                            </label>
                            <input type="number" id="district-llgs" name="district_llgs" class="esp-form-input" min="0"
                                   value="<?php echo isset($_POST['district_llgs']) ? esc_attr($_POST['district_llgs']) : esc_attr($district_llgs); ?>">
                            <p class="esp-form-help"><?php _e('Local Level Governments in this district', 'esp-admin-manager'); ?></p>
                        </div>

                        <div class="esp-form-group">
                            <label for="district-wards" class="esp-form-label">
                                <?php _e('Number of Wards', 'esp-admin-manager'); ?>
                            </label>
                            <input type="number" id="district-wards" name="district_wards" class="esp-form-input" min="0"
                                   value="<?php echo isset($_POST['district_wards']) ? esc_attr($_POST['district_wards']) : esc_attr($district_wards); ?>">
                            <p class="esp-form-help"><?php _e('Electoral wards in this district', 'esp-admin-manager'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="esp-form-section">
                    <h3 class="esp-section-title"><?php _e('Demographics & Geography', 'esp-admin-manager'); ?></h3>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-population" class="esp-form-label">
                                <?php _e('Population', 'esp-admin-manager'); ?>
                            </label>
                            <input type="number" id="district-population" name="district_population" class="esp-form-input" min="0"
                                   value="<?php echo isset($_POST['district_population']) ? esc_attr($_POST['district_population']) : esc_attr($district_population); ?>">
                            <p class="esp-form-help"><?php _e('Total population of the district', 'esp-admin-manager'); ?></p>
                        </div>

                        <div class="esp-form-group">
                            <label for="district-area" class="esp-form-label">
                                <?php _e('Area (km²)', 'esp-admin-manager'); ?>
                            </label>
                            <input type="number" id="district-area" name="district_area" class="esp-form-input" min="0" step="0.01"
                                   value="<?php echo isset($_POST['district_area']) ? esc_attr($_POST['district_area']) : esc_attr($district_area); ?>">
                            <p class="esp-form-help"><?php _e('Total area in square kilometers', 'esp-admin-manager'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="esp-form-section">
                    <h3 class="esp-section-title"><?php _e('District Page Link', 'esp-admin-manager'); ?></h3>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-page-url" class="esp-form-label">
                                <?php _e('District Page URL', 'esp-admin-manager'); ?>
                            </label>
                            <input type="url" id="district-page-url" name="district_page_url" class="esp-form-input"
                                   placeholder="https://example.com/district-page"
                                   value="<?php echo isset($_POST['district_page_url']) ? esc_attr($_POST['district_page_url']) : esc_attr($district_page_url); ?>">
                            <p class="esp-form-help"><?php _e('Enter the URL of the page users should visit when they click on this district. Leave empty if no link is needed.', 'esp-admin-manager'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="esp-form-section">
                    <h3 class="esp-section-title"><?php _e('District Photo', 'esp-admin-manager'); ?></h3>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-photo" class="esp-form-label">
                                <?php _e('Featured Image', 'esp-admin-manager'); ?>
                            </label>
                            <div class="esp-media-upload">
                                <button type="button" id="upload-photo-btn" class="esp-btn esp-btn-secondary">
                                    <span class="esp-btn-icon">📷</span>
                                    <?php _e('Choose Photo', 'esp-admin-manager'); ?>
                                </button>
                                <div id="photo-preview" class="esp-photo-preview" <?php echo $district_photo_url ? '' : 'style="display: none;"'; ?>>
                                    <img id="preview-image" src="<?php echo esc_url($district_photo_url); ?>" alt="Preview">
                                    <button type="button" id="remove-photo-btn" class="esp-btn esp-btn-danger esp-btn-sm">
                                        <?php _e('Remove', 'esp-admin-manager'); ?>
                                    </button>
                                </div>
                                <input type="hidden" id="district-photo-id" name="district_photo_id" value="<?php echo esc_attr($district_photo_id); ?>">
                                <p class="esp-form-help"><?php _e('Upload a representative image for this district', 'esp-admin-manager'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="esp-form-actions">
                    <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts'); ?>" class="esp-btn esp-btn-secondary">
                        <?php _e('Cancel', 'esp-admin-manager'); ?>
                    </a>
                    <button type="submit" name="submit_district" class="esp-btn esp-btn-primary">
                        <span class="esp-btn-icon">💾</span>
                        <?php _e('Update District', 'esp-admin-manager'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    let mediaUploader;

    // Media upload button
    $('#upload-photo-btn').on('click', function(e) {
        e.preventDefault();

        if (mediaUploader) {
            mediaUploader.open();
            return;
        }

        mediaUploader = wp.media({
            title: 'Choose District Photo',
            button: {
                text: 'Use this photo'
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });

        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            $('#district-photo-id').val(attachment.id);
            $('#preview-image').attr('src', attachment.url);
            $('#photo-preview').show();
        });

        mediaUploader.open();
    });

    // Remove photo button
    $('#remove-photo-btn').on('click', function(e) {
        e.preventDefault();
        $('#district-photo-id').val('');
        $('#photo-preview').hide();
    });
});
</script>
