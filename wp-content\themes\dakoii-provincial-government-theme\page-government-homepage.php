<?php
/**
 * Template Name: Government Homepage
 *
 * A custom page template for the East Sepik Provincial Administration Homepage
 *
 * This template displays the East Sepik Provincial Government homepage
 * with comprehensive government information and services.
 *
 * This can be assigned to any page through the Page Attributes meta box.
 *
 * @package Nols_ESPA_Theme_Two
 */

get_header(); ?>

<!-- Featured Images Slideshow -->
<section class="slideshow-section" id="home">
    <div class="slideshow-container">
        <div class="slide slide-1 active">
            <div class="slide-content">
                <h1 class="slide-title"><?php esc_html_e('Welcome to East Sepik Province', 'nols-espa-theme-two'); ?></h1>
                <p class="slide-subtitle"><?php esc_html_e('The Heart of Papua New Guinea\'s Cultural Heritage', 'nols-espa-theme-two'); ?></p>
            </div>
        </div>
        <div class="slide slide-2">
            <div class="slide-content">
                <h1 class="slide-title"><?php esc_html_e('Serving Our Communities', 'nols-espa-theme-two'); ?></h1>
                <p class="slide-subtitle"><?php esc_html_e('Committed to Development and Progress', 'nols-espa-theme-two'); ?></p>
            </div>
        </div>
        <div class="slide slide-3">
            <div class="slide-content">
                <h1 class="slide-title"><?php esc_html_e('Building a Better Future', 'nols-espa-theme-two'); ?></h1>
                <p class="slide-subtitle"><?php esc_html_e('Together We Grow Stronger', 'nols-espa-theme-two'); ?></p>
            </div>
        </div>
    </div>
    <div class="slideshow-nav">
        <span class="slide-dot active" onclick="currentSlide(1)"></span>
        <span class="slide-dot" onclick="currentSlide(2)"></span>
        <span class="slide-dot" onclick="currentSlide(3)"></span>
    </div>
</section>

<!-- Governor Section -->
<section class="governor-section" id="governor">
    <div class="section-container">
        <div class="governor-grid">
            <div class="governor-photo"></div>
            <div class="governor-content">
                <h2><?php esc_html_e('Hon. Allan Bird', 'nols-espa-theme-two'); ?></h2>
                <div class="governor-title"><?php esc_html_e('Governor of East Sepik Province', 'nols-espa-theme-two'); ?></div>
                <div class="governor-message">
                    <?php esc_html_e('"As we work together to build a prosperous East Sepik Province, I am committed to ensuring that our government serves every citizen with integrity, transparency, and dedication. Our rich cultural heritage and natural resources are the foundation upon which we will build a sustainable future for our children and generations to come. Through unity, hard work, and good governance, we will achieve our vision of a progressive and self-reliant East Sepik Province."', 'nols-espa-theme-two'); ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Members of Parliament -->
<section class="parliament-section" id="parliament">
    <div class="section-container">
        <h2 class="section-title"><?php esc_html_e('Members of Parliament', 'nols-espa-theme-two'); ?></h2>
        <p class="section-subtitle"><?php esc_html_e('Representing the people of East Sepik Province in the National Parliament', 'nols-espa-theme-two'); ?></p>

        <div class="mp-grid">
            <div class="mp-card">
                <div class="mp-photo">👤</div>
                <div class="mp-name"><?php esc_html_e('Hon. Allan Bird', 'nols-espa-theme-two'); ?></div>
                <div class="mp-electorate"><?php esc_html_e('East Sepik Provincial', 'nols-espa-theme-two'); ?></div>
                <div class="mp-party"><?php esc_html_e('Pangu Party', 'nols-espa-theme-two'); ?></div>
            </div>

            <div class="mp-card">
                <div class="mp-photo">👤</div>
                <div class="mp-name"><?php esc_html_e('Hon. Gabriel Kapris', 'nols-espa-theme-two'); ?></div>
                <div class="mp-electorate"><?php esc_html_e('Wewak Open', 'nols-espa-theme-two'); ?></div>
                <div class="mp-party"><?php esc_html_e('PNC', 'nols-espa-theme-two'); ?></div>
            </div>

            <div class="mp-card">
                <div class="mp-photo">👤</div>
                <div class="mp-name"><?php esc_html_e('Hon. Johnson Wamp', 'nols-espa-theme-two'); ?></div>
                <div class="mp-electorate"><?php esc_html_e('Angoram Open', 'nols-espa-theme-two'); ?></div>
                <div class="mp-party"><?php esc_html_e('NAP', 'nols-espa-theme-two'); ?></div>
            </div>

            <div class="mp-card">
                <div class="mp-photo">👤</div>
                <div class="mp-name"><?php esc_html_e('Hon. Samson Knos', 'nols-espa-theme-two'); ?></div>
                <div class="mp-electorate"><?php esc_html_e('Maprik Open', 'nols-espa-theme-two'); ?></div>
                <div class="mp-party"><?php esc_html_e('PLP', 'nols-espa-theme-two'); ?></div>
            </div>

            <div class="mp-card">
                <div class="mp-photo">👤</div>
                <div class="mp-name"><?php esc_html_e('Hon. Joseph Yopyyopy', 'nols-espa-theme-two'); ?></div>
                <div class="mp-electorate"><?php esc_html_e('Ambunti-Drekikier Open', 'nols-espa-theme-two'); ?></div>
                <div class="mp-party"><?php esc_html_e('PANGU', 'nols-espa-theme-two'); ?></div>
            </div>

            <div class="mp-card">
                <div class="mp-photo">👤</div>
                <div class="mp-name"><?php esc_html_e('Hon. Bernard Hagoria', 'nols-espa-theme-two'); ?></div>
                <div class="mp-electorate"><?php esc_html_e('Yangoru-Saussia Open', 'nols-espa-theme-two'); ?></div>
                <div class="mp-party"><?php esc_html_e('URP', 'nols-espa-theme-two'); ?></div>
            </div>
        </div>
    </div>
</section>

<!-- Provincial Map & Statistics -->
<section class="map-section" id="main-content">
    <div class="section-container">
        <h2 class="section-title"><?php esc_html_e('East Sepik Province Map & Statistics', 'nols-espa-theme-two'); ?></h2>

        <div class="map-container">
            <div class="map-visual">
                <div class="map-placeholder">
                    🗺️ <?php esc_html_e('East Sepik Province Map', 'nols-espa-theme-two'); ?>
                    <br><br>
                    <?php esc_html_e('Interactive Provincial Boundary Map', 'nols-espa-theme-two'); ?>
                    <br>
                    <?php esc_html_e('Districts • LLGs • Wards', 'nols-espa-theme-two'); ?>
                </div>
            </div>

            <div class="map-info">
                <h3><?php esc_html_e('Provincial Overview', 'nols-espa-theme-two'); ?></h3>
                <div class="map-stats">
                    <div class="map-stat">
                        <div class="map-stat-number">450,530</div>
                        <div class="map-stat-label"><?php esc_html_e('Population', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="map-stat">
                        <div class="map-stat-number">43,426</div>
                        <div class="map-stat-label"><?php esc_html_e('Area (km²)', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="map-stat">
                        <div class="map-stat-number">6</div>
                        <div class="map-stat-label"><?php esc_html_e('Districts', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="map-stat">
                        <div class="map-stat-number">41</div>
                        <div class="map-stat-label"><?php esc_html_e('LLGs', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="map-stat">
                        <div class="map-stat-number">1,287</div>
                        <div class="map-stat-label"><?php esc_html_e('Wards', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="map-stat">
                        <div class="map-stat-number">4</div>
                        <div class="map-stat-label"><?php esc_html_e('Urban LLGs', 'nols-espa-theme-two'); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Districts -->
<section class="districts-section" id="districts">
    <div class="section-container">
        <h2 class="section-title"><?php esc_html_e('Districts of East Sepik Province', 'nols-espa-theme-two'); ?></h2>
        <p class="section-subtitle"><?php esc_html_e('Six administrative districts serving our diverse communities', 'nols-espa-theme-two'); ?></p>

        <div class="districts-grid">
            <div class="district-card">
                <div class="district-name"><?php esc_html_e('Wewak District', 'nols-espa-theme-two'); ?></div>
                <div class="district-stats">
                    <div class="district-stat">
                        <div class="district-stat-number">8</div>
                        <div class="district-stat-label"><?php esc_html_e('LLGs', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="district-stat">
                        <div class="district-stat-number">156</div>
                        <div class="district-stat-label"><?php esc_html_e('Wards', 'nols-espa-theme-two'); ?></div>
                    </div>
                </div>
                <div class="district-description">
                    <?php esc_html_e('Provincial capital and administrative center. Home to the provincial government headquarters and major commercial activities.', 'nols-espa-theme-two'); ?>
                </div>
            </div>

            <div class="district-card">
                <div class="district-name"><?php esc_html_e('Maprik District', 'nols-espa-theme-two'); ?></div>
                <div class="district-stats">
                    <div class="district-stat">
                        <div class="district-stat-number">7</div>
                        <div class="district-stat-label"><?php esc_html_e('LLGs', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="district-stat">
                        <div class="district-stat-number">198</div>
                        <div class="district-stat-label"><?php esc_html_e('Wards', 'nols-espa-theme-two'); ?></div>
                    </div>
                </div>
                <div class="district-description">
                    <?php esc_html_e('Known for its rich cultural heritage and traditional art. Major agricultural area with significant yam and sweet potato production.', 'nols-espa-theme-two'); ?>
                </div>
            </div>

            <div class="district-card">
                <div class="district-name"><?php esc_html_e('Angoram District', 'nols-espa-theme-two'); ?></div>
                <div class="district-stats">
                    <div class="district-stat">
                        <div class="district-stat-number">6</div>
                        <div class="district-stat-label"><?php esc_html_e('LLGs', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="district-stat">
                        <div class="district-stat-number">287</div>
                        <div class="district-stat-label"><?php esc_html_e('Wards', 'nols-espa-theme-two'); ?></div>
                    </div>
                </div>
                <div class="district-description">
                    <?php esc_html_e('Located along the Sepik River. Famous for traditional crocodile initiation ceremonies and wood carving artistry.', 'nols-espa-theme-two'); ?>
                </div>
            </div>

            <div class="district-card">
                <div class="district-name"><?php esc_html_e('Ambunti-Drekikier District', 'nols-espa-theme-two'); ?></div>
                <div class="district-stats">
                    <div class="district-stat">
                        <div class="district-stat-number">7</div>
                        <div class="district-stat-label"><?php esc_html_e('LLGs', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="district-stat">
                        <div class="district-stat-number">195</div>
                        <div class="district-stat-label"><?php esc_html_e('Wards', 'nols-espa-theme-two'); ?></div>
                    </div>
                </div>
                <div class="district-description">
                    <?php esc_html_e('Remote district along the upper Sepik River. Rich in biodiversity and traditional customs of river communities.', 'nols-espa-theme-two'); ?>
                </div>
            </div>

            <div class="district-card">
                <div class="district-name"><?php esc_html_e('Yangoru-Saussia District', 'nols-espa-theme-two'); ?></div>
                <div class="district-stats">
                    <div class="district-stat">
                        <div class="district-stat-number">6</div>
                        <div class="district-stat-label"><?php esc_html_e('LLGs', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="district-stat">
                        <div class="district-stat-number">234</div>
                        <div class="district-stat-label"><?php esc_html_e('Wards', 'nols-espa-theme-two'); ?></div>
                    </div>
                </div>
                <div class="district-description">
                    <?php esc_html_e('Highland district known for its cool climate and coffee production. Important educational center with several schools.', 'nols-espa-theme-two'); ?>
                </div>
            </div>

            <div class="district-card">
                <div class="district-name"><?php esc_html_e('Dreikikir District', 'nols-espa-theme-two'); ?></div>
                <div class="district-stats">
                    <div class="district-stat">
                        <div class="district-stat-number">7</div>
                        <div class="district-stat-label"><?php esc_html_e('LLGs', 'nols-espa-theme-two'); ?></div>
                    </div>
                    <div class="district-stat">
                        <div class="district-stat-number">217</div>
                        <div class="district-stat-label"><?php esc_html_e('Wards', 'nols-espa-theme-two'); ?></div>
                    </div>
                </div>
                <div class="district-description">
                    <?php esc_html_e('Mountainous district with diverse ethnic groups. Important for mining activities and timber resources.', 'nols-espa-theme-two'); ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Provincial Administration Structure -->
<section class="admin-structure" id="administration">
    <div class="section-container">
        <h2 class="section-title"><?php esc_html_e('Provincial Administration', 'nols-espa-theme-two'); ?></h2>
        <p class="section-subtitle"><?php esc_html_e('Organizational structure of East Sepik Provincial Government', 'nols-espa-theme-two'); ?></p>

        <div class="structure-grid">
            <div class="structure-section">
                <h3><?php esc_html_e('Government Sectors', 'nols-espa-theme-two'); ?></h3>
                <ul class="structure-list">
                    <li><?php esc_html_e('Health & Medical Services', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Education & Training', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Infrastructure & Transport', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Agriculture & Livestock', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Commerce & Industry', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Tourism & Culture', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Environment & Conservation', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Community Development', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Youth & Sports', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Women & Family Affairs', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Law & Justice', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Finance & Treasury', 'nols-espa-theme-two'); ?></li>
                </ul>
            </div>

            <div class="structure-section">
                <h3><?php esc_html_e('Administrative Divisions', 'nols-espa-theme-two'); ?></h3>
                <ul class="structure-list">
                    <li><?php esc_html_e('Office of the Governor', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Provincial Administrator', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Department of Finance', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Department of Works', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Department of Health', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Department of Education', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Department of Agriculture', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Department of Commerce', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Department of Lands', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Department of Environment', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Provincial Planning Office', 'nols-espa-theme-two'); ?></li>
                    <li><?php esc_html_e('Internal Audit Division', 'nols-espa-theme-two'); ?></li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Featured Events & News -->
<section class="featured-section" id="events">
    <div class="section-container">
        <h2 class="section-title"><?php esc_html_e('Featured Events & Latest News', 'nols-espa-theme-two'); ?></h2>

        <div class="featured-grid">
            <div class="featured-events">
                <h3 class="featured-title">🎉 <?php esc_html_e('Upcoming Events', 'nols-espa-theme-two'); ?></h3>

                <?php
                // Get events from posts with 'event' category
                $events_query = new WP_Query(array(
                    'posts_per_page' => 4,
                    'category_name' => 'events',
                    'post_status' => 'publish',
                    'meta_key' => 'event_date',
                    'orderby' => 'meta_value',
                    'order' => 'ASC'
                ));

                if ($events_query->have_posts()) :
                    while ($events_query->have_posts()) : $events_query->the_post();
                        $event_date = get_post_meta(get_the_ID(), 'event_date', true);
                        if (!$event_date) {
                            $event_date = get_the_date('F j, Y');
                        }
                ?>
                    <div class="event-item">
                        <div class="event-date"><?php echo esc_html($event_date); ?></div>
                        <div class="event-title"><?php the_title(); ?></div>
                        <div class="event-description"><?php echo wp_trim_words(get_the_excerpt(), 20); ?></div>
                    </div>
                <?php
                    endwhile;
                    wp_reset_postdata();
                else :
                    // Default events if no posts found
                ?>
                    <div class="event-item">
                        <div class="event-date"><?php esc_html_e('September 16-18, 2025', 'nols-espa-theme-two'); ?></div>
                        <div class="event-title"><?php esc_html_e('East Sepik Cultural Festival', 'nols-espa-theme-two'); ?></div>
                        <div class="event-description"><?php esc_html_e('Annual celebration of traditional arts, music, and dance featuring all six districts. Venue: Wewak Town.', 'nols-espa-theme-two'); ?></div>
                    </div>

                    <div class="event-item">
                        <div class="event-date"><?php esc_html_e('September 25, 2025', 'nols-espa-theme-two'); ?></div>
                        <div class="event-title"><?php esc_html_e('Provincial Assembly Meeting', 'nols-espa-theme-two'); ?></div>
                        <div class="event-description"><?php esc_html_e('Quarterly assembly session to discuss budget allocations and development projects.', 'nols-espa-theme-two'); ?></div>
                    </div>

                    <div class="event-item">
                        <div class="event-date"><?php esc_html_e('October 5-7, 2025', 'nols-espa-theme-two'); ?></div>
                        <div class="event-title"><?php esc_html_e('Agriculture & Trade Fair', 'nols-espa-theme-two'); ?></div>
                        <div class="event-description"><?php esc_html_e('Showcasing local products, farming innovations, and business opportunities.', 'nols-espa-theme-two'); ?></div>
                    </div>

                    <div class="event-item">
                        <div class="event-date"><?php esc_html_e('October 15, 2025', 'nols-espa-theme-two'); ?></div>
                        <div class="event-title"><?php esc_html_e('Youth Leadership Summit', 'nols-espa-theme-two'); ?></div>
                        <div class="event-description"><?php esc_html_e('Empowering young leaders across all districts with training and networking opportunities.', 'nols-espa-theme-two'); ?></div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="featured-news">
                <h3 class="featured-title">📰 <?php esc_html_e('Latest News', 'nols-espa-theme-two'); ?></h3>

                <?php
                // Get latest news posts
                $news_query = new WP_Query(array(
                    'posts_per_page' => 4,
                    'category_name' => 'news',
                    'post_status' => 'publish'
                ));

                if ($news_query->have_posts()) :
                    while ($news_query->have_posts()) : $news_query->the_post();
                ?>
                    <div class="news-item">
                        <div class="news-date"><?php echo esc_html(get_the_date()); ?></div>
                        <div class="news-title"><?php the_title(); ?></div>
                        <div class="news-excerpt"><?php echo wp_trim_words(get_the_excerpt(), 15); ?></div>
                    </div>
                <?php
                    endwhile;
                    wp_reset_postdata();
                else :
                    // Default news if no posts found
                ?>
                    <div class="news-item">
                        <div class="news-date"><?php echo esc_html(current_time('F j, Y')); ?></div>
                        <div class="news-title"><?php esc_html_e('K850M Provincial Budget Approved', 'nols-espa-theme-two'); ?></div>
                        <div class="news-excerpt"><?php esc_html_e('Provincial Assembly approves record budget with focus on infrastructure, health, and education development...', 'nols-espa-theme-two'); ?></div>
                    </div>

                    <div class="news-item">
                        <div class="news-date"><?php echo esc_html(date('F j, Y', strtotime('-1 day'))); ?></div>
                        <div class="news-title"><?php esc_html_e('Wewak-Maprik Highway 65% Complete', 'nols-espa-theme-two'); ?></div>
                        <div class="news-excerpt"><?php esc_html_e('Major road reconstruction project ahead of schedule, expected completion by December 2025...', 'nols-espa-theme-two'); ?></div>
                    </div>

                    <div class="news-item">
                        <div class="news-date"><?php echo esc_html(date('F j, Y', strtotime('-3 days'))); ?></div>
                        <div class="news-title"><?php esc_html_e('New Health Centers Opened in Remote Areas', 'nols-espa-theme-two'); ?></div>
                        <div class="news-excerpt"><?php esc_html_e('Three new aid posts established in Ambunti-Drekikier and Angoram districts improving healthcare access...', 'nols-espa-theme-two'); ?></div>
                    </div>

                    <div class="news-item">
                        <div class="news-date"><?php echo esc_html(date('F j, Y', strtotime('-4 days'))); ?></div>
                        <div class="news-title"><?php esc_html_e('Provincial Development Grants Now Open', 'nols-espa-theme-two'); ?></div>
                        <div class="news-excerpt"><?php esc_html_e('Applications accepted until September 15 for community projects, small business support, and infrastructure...', 'nols-espa-theme-two'); ?></div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section class="contact-section" id="contact">
    <div class="section-container">
        <h2 class="section-title"><?php esc_html_e('Contact Provincial Government', 'nols-espa-theme-two'); ?></h2>

        <div class="contact-grid">
            <div class="contact-card">
                <div class="contact-icon">🏛️</div>
                <h3 class="contact-title"><?php esc_html_e('Provincial Headquarters', 'nols-espa-theme-two'); ?></h3>
                <div class="contact-info">
                    <?php esc_html_e('Wewak, East Sepik Province', 'nols-espa-theme-two'); ?><br>
                    <?php esc_html_e('Papua New Guinea', 'nols-espa-theme-two'); ?><br>
                    <?php esc_html_e('PO Box 280, Wewak', 'nols-espa-theme-two'); ?>
                </div>
            </div>

            <div class="contact-card">
                <div class="contact-icon">📞</div>
                <h3 class="contact-title"><?php esc_html_e('Phone & Fax', 'nols-espa-theme-two'); ?></h3>
                <div class="contact-info">
                    <?php esc_html_e('Phone: (+*************', 'nols-espa-theme-two'); ?><br>
                    <?php esc_html_e('Fax: (+*************', 'nols-espa-theme-two'); ?><br>
                    <?php esc_html_e('Emergency: 000', 'nols-espa-theme-two'); ?>
                </div>
            </div>

            <div class="contact-card">
                <div class="contact-icon">✉️</div>
                <h3 class="contact-title"><?php esc_html_e('Email & Web', 'nols-espa-theme-two'); ?></h3>
                <div class="contact-info">
                    <EMAIL><br>
                    <EMAIL><br>
                    www.eastsepik.gov.pg
                </div>
            </div>

            <div class="contact-card">
                <div class="contact-icon">🕐</div>
                <h3 class="contact-title"><?php esc_html_e('Office Hours', 'nols-espa-theme-two'); ?></h3>
                <div class="contact-info">
                    <?php esc_html_e('Monday - Friday', 'nols-espa-theme-two'); ?><br>
                    <?php esc_html_e('8:00 AM - 4:30 PM', 'nols-espa-theme-two'); ?><br>
                    <?php esc_html_e('Closed Weekends & Public Holidays', 'nols-espa-theme-two'); ?>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Slideshow functionality
let slideIndex = 1;
showSlides(slideIndex);

function currentSlide(n) {
    showSlides(slideIndex = n);
}

function showSlides(n) {
    let slides = document.getElementsByClassName("slide");
    let dots = document.getElementsByClassName("slide-dot");

    if (n > slides.length) {slideIndex = 1}
    if (n < 1) {slideIndex = slides.length}

    for (let i = 0; i < slides.length; i++) {
        slides[i].classList.remove("active");
    }

    for (let i = 0; i < dots.length; i++) {
        dots[i].classList.remove("active");
    }

    if (slides[slideIndex-1]) {
        slides[slideIndex-1].classList.add("active");
    }
    if (dots[slideIndex-1]) {
        dots[slideIndex-1].classList.add("active");
    }
}

// Auto slideshow
function autoSlides() {
    slideIndex++;
    showSlides(slideIndex);
}

setInterval(autoSlides, 5000); // Change slide every 5 seconds

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading animations
window.addEventListener('load', function() {
    const animatedElements = document.querySelectorAll('.mp-card, .district-card, .event-item, .news-item');
    animatedElements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.1}s`;
    });
});

// Intersection Observer for scroll animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.querySelectorAll('.mp-card, .district-card, .contact-card').forEach(el => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(30px)';
    el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(el);
});
</script>

<?php get_footer(); ?>
