/**
 * Customizer controls styling
 *
 */
	.customize-control select,
	.customize-control input,
	.customize-control .customize-number-range-control input[type="number"] ~ .components-input-control__backdrop {
	margin-top: 1px;
	border-radius: 0;
	border: 1px solid #d7d7d7;
	}

	.customize-control select:focus,
	.customize-control input:focus,
	.customize-control .customize-number-range-control input[type="number"]:focus ~ .components-input-control__backdrop {
		border-color: #5fa1ff;
		box-shadow: 0 0 0 1px #5fa1ff;
	}

/***************************
  Responsive Box control
****************************/
	.block-content .responsive-control:hover,
	h2.block-heading:hover {
		cursor: initial;
	}

	.block-content .responsive-control {
		position: relative;
		margin-top: 20px;
	}

	.block-content .responsive-icons {
		position: absolute;
		right: 0;
		top: 0;
	}

	.block-content .responsive-icons:hover {
		cursor: pointer;
	}

	.block-content .components-base-control__label.components-text {
		text-transform: capitalize;
		font-size: 13px;
	}

	.block-content .components-base-control__label,
	.customize-responsive-range-control .components-base-control__label {
		font-size: 13px;
		text-transform: capitalize;
	}

/**********************************
  Color Picker / Color Group Picker Styles
**********************************/
	.customize-preset-color-picker-control .react-colorful,
	.customize-color-image-group-control .react-colorful,
	.customize-color-group-control .react-colorful,
	.customize-color-group-picker-control .react-colorful,
	.customize-color-picker-control .react-colorful {
		width: 100%;
	}

	.customize-preset-color-picker-control .react-colorful .react-colorful__saturation,
	.customize-color-image-group-control .react-colorful .react-colorful__saturation,
	.customize-color-group-control .react-colorful .react-colorful__saturation,
	.customize-color-group-picker-control .react-colorful .react-colorful__saturation,
	.customize-color-picker-control .react-colorful .react-colorful__saturation {
		height: 117px;
		border-radius: 5px;
		margin-bottom: 20px;
	}

	.customize-gradient-picker-control .customize-control-title,
	.customize-color-picker-control .customize-control-title,
	.customize-color-group-control .customize-control-title,
	.customize-color-image-group-control .customize-control-title,
	.customize-color-group-picker-control .customize-control-title {
		font-size: 11.5px;
	}

	.customize-preset-color-picker-control .control-header .control-header-trigger,
	.customize-preset-gradient-picker-control .control-header .control-header-trigger,
	.customize-gradient-picker-control .control-header .control-header-trigger,
	.customize-color-picker-control .control-header .control-header-trigger,
	.customize-color-group-control .control-header .control-header-trigger,
	.customize-color-image-group-control .control-header .control-header-trigger,
	.customize-advanced-color-group-control .control-header .control-header-trigger,
	.customize-color-group-picker-control .control-header .control-header-trigger,
	.customize-control-color-picker .control-header .control-header-trigger,
	.customize-background-color-group-picker-control .control-header-trigger  {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.customize-gradient-picker-control .control-header .component-color-indicator,
	.customize-color-picker-control .control-header .component-color-indicator,
	.customize-color-group-control .control-header .component-color-indicator,
	.customize-color-image-group-control .control-header .component-color-indicator,
	.customize-advanced-color-group-control .control-header .component-color-indicator,
	.customize-color-group-picker-control .control-header .component-color-indicator,
	.customize-control-color-picker .control-header .component-color-indicator,
	.customize-control-background-color-group-picker .components-dropdown .component-color-indicator {
		cursor: pointer;
		transform: scale(1);
		height: 28px;
		width: 28px;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.customize-gradient-picker-control,
	.customize-color-picker-control,
	.customize-control-advanced-color-group,
	.customize-control-color-group-picker, 
	.customize-control-color-picker {
		margin-bottom: 22px;
	}

	.customize-gradient-picker-control .customize-control-title,
	.customize-color-picker-control .customize-control-title,
	.customize-color-group-control .customize-control-title,
	.customize-color-image-group-control .customize-control-title,
	.customize-control-advanced-color-group .customize-control-title,
	.customize-control-color-group-picker .customize-control-title, 
	.customize-control-color-picker .customize-control-title {
		line-height: 2;
		font-size: 13px;
	}

	.customize-color-group-picker-control .control-content-wrap,
	.customize-color-group-control .control-content-wrap,
	.customize-color-image-group-control .control-content-wrap,
	.customize-control-background-color-group-picker .control-content-wrap {
		display: flex;
		align-items: center;
	}

	.customize-color-picker-control .control-content-wrap .reset-button,
	.customize-color-group-picker-control .control-content-wrap .reset-button,
	.customize-color-group-control .control-content-wrap .reset-button,
	.customize-color-image-group-control .control-content-wrap .reset-button,
	.customize-control-background-color-group-picker .control-content-wrap .reset-button,
	.customize-control-preset-color-picker .reset-button,
	.customize-control-preset-gradient-picker .reset-button {
		color: #006ba1;
		height: 20px;
		width: 20px;
		padding: 4px;
		background: 0 0;
		box-shadow: none;
	}

	.customize-color-picker-control .control-content-wrap .reset-button .dashicon,
	.customize-color-group-picker-control .control-content-wrap .reset-button .dashicon,
	.customize-color-group-control .control-content-wrap .reset-button .dashicon,
	.customize-color-image-group-control .control-content-wrap .reset-button .dashicon,
	.customize-control-background-color-group-picker .control-content-wrap .reset-button .dashicon,
	.customize-control-preset-color-picker .reset-button .dashicon
	.customize-control-preset-gradient-picker .reset-button .dashicon {
		width: 12px;
		height: 12px;
		font-size: 12px;
	}

	.customize-color-picker-control .control-content-wrap .reset-button.components-button.is-secondary:hover:not(:disabled):hover,
	.customize-color-group-picker-control .control-content-wrap .reset-button.components-button.is-secondary:hover:not(:disabled):hover,
	.customize-color-group-control .control-content-wrap .reset-button.components-button.is-secondary:hover:not(:disabled):hover,
	.customize-color-image-group-control .control-content-wrap .reset-button.components-button.is-secondary:hover:not(:disabled):hover,
	.customize-control-background-color-group-picker .control-content-wrap .reset-button.components-button.is-secondary:hover:not(:disabled):hover,
	.customize-control-preset-color-picker .reset-button.is-secondary:hover:not(:disabled):hover,
	.customize-control-preset-gradient-picker .reset-button.is-secondary:hover:not(:disabled):hover {
		color: #02abff;
		box-shadow: none;
	}

	.customize-preset-gradient-picker-control .components-popover__content .components-surface.components-card {
		padding: 1px 4px;
	}

/**********************************
  Tab Panel Styles
**********************************/
	.component-color-indicator {
		cursor: pointer;
	}

	#accordion-panel-blogzee_innerpages_settings_panel,
	#accordion-section-background_image,
	#accordion-section-footer_three_column_section {
		margin-top: 15px;
	}

	.customize-color-group-picker-control .components-dropdown,
	.customize-control-background-color-group-picker .components-dropdown {
		padding: 0 5px;
	}

/**********************************
  Block Controls
**********************************/
	.customize-video-playlist-control .block-item,
	.customize-block-repeater-control .block-item,
	.repeater-control-inner {
		position: relative;
	}

	.customize-video-playlist-control .block-item.block--hidden,
	.customize-block-repeater-control .block-item.block--hidden {
		opacity: 0.6;
	}

	.customize-video-playlist-control .block-item .block-header,
	.customize-block-repeater-control .block-item .block-header {
		display: flex;
		padding: 0 0 0 12px;
		border: 1px solid #ddd;
		color: #556068;
		background: #fff;
		cursor: move;
		font-size: 12px;
		margin: 0 0 10px;
		border-radius: 3px;
		align-items: center;
		position: relative;
	}

	.customize-video-playlist-control .block-item .block-header {
		margin-bottom: 0px;
	}

	.customize-video-playlist-control .block-item {
		margin-bottom: 10px;
	}

	.customize-video-playlist-control .block-item .block-content {
		border: 1px solid #ddd;
		border-top: none;
		background: #fff;
		padding: 2px 6px;
	}

	.block-item .block-header .control-title {
		flex: 1;
		display: block;
		text-transform: capitalize;
		font-size: 12px;
	}

	.block-item .block-header .display-icon,
	.block-item .block-header .setting-icon {
		cursor: pointer;
		float: right;
		position: relative;
		font-size: 16px;
		padding: 9px;
		line-height: 20px;
		border-left: 1px solid #ddd;
	}

	.block-item .block-header .remove-icon {
		display: none;
		position: absolute;
		right: -10px;
		border: 1px dashed red;
		border-radius: 70%;
		width: 20px;
		height: 20px;
		top: -10px;
		color: red;
		text-decoration: none;
	}

	.block-item .block-header:hover .remove-icon {
		display: block;
		text-decoration: none;
	} 

	.block-item .block-header .remove-icon:hover {
		cursor: pointer;
	}

	.customize-video-playlist-control .block-item .components-popover__content,
	.customize-block-repeater-control .block-item .components-popover__content {
		width: 280px;
		border-radius: 5px;
		box-shadow: 1px 1px 1px 3px rgb(251 251 251);
		-webkit-box-shadow: 1px 1px 1px 3px rgb(251 251 251);
		-moz-box-shadow: 1px 1px 1px 3px rgb(251 251 251);
		border: none;
	}

	.customize-control-item-sortable .sort-list .sort-item {
		display: block;
	}

/** post query field **/
	.block-content .post-query-field {
		margin-bottom: 6px;
		padding: 6px;
	}

	.block-content .post-query-field > div {
		float: right;
	}

	.block-content .post-query-field-label {
		display: inline-block;
		font-weight: 500;
		text-decoration: underline;
		font-size: 13px;
		text-transform: uppercase;
		color: #007cba;
	}

	.block-content .dashicons-edit {
		background-color: #f0f0f0;
		padding: 3px;
		border-radius: 50%;
		vertical-align: middle;
		border: 2px solid #007cba;
		color: #007cba;
	}

	.block-content .dashicons-edit:hover {
		cursor: pointer;
	}

	#customize-control-social_icons .repeater-control-inner .repeater-item .item-control-fields,
	#ccustomize-control-opinons_items .repeater-control-inner .repeater-item .item-control-fields.isShow {
		width: 213px;
		left: 22px;
	}

	#customize-control-social_icons .repeater-control .fontawesome-icon-picker .icon-header > div {
		flex: 50%;
		text-align: center;
		padding: 7px;
		vertical-align: middle;
		line-height: 1;
	}

	#customize-control-social_icons .repeater-control .fontawesome-icon-picker .icon-holder .icons-list {
		position: fixed;
		background-color: #fff;
		padding: 20px;
		height: 350px;
		overflow: auto;
		border: 1px solid #ffbcbc;
		margin-top: 8px;
		bottom: 0;
		left: 300px;
		width: 600px;
		margin-bottom: 5px;
		padding-top: 35px;
	}

	#customize-control-social_icons .repeater-control .fontawesome-icon-picker .icon-holder .icons-list i {
		cursor: pointer;
		font-size: 18px;
		padding: 12px 9px;
	}

	#customize-control-social_icons .repeater-control .fontawesome-icon-picker .icon-holder .icons-list i:hover {
		color: #d95f3d;
	}

	#customize-control-social_icons .repeater-control .fontawesome-icon-picker .icon-holder .icons-list i.selected {
		color: #d95f3d;
		font-size: 25px;
	}

/*******************
  Icon Select Icon
*******************/
	.customize-control-icon-text .field-wrap {
		display: flex;
	}

	.customize-control-icon-text .field-wrap .components-dropdown {
		flex: 0 1 15%;
	}

	.customize-control-icon-text .field-wrap .components-base-control {
		flex: 0 1 82%;
	}

	.customize-control-icon-text .field-wrap .components-dropdown {
		text-align: center;
		padding: 0;
		border: 1px solid #8d8d8d;
		margin-right: 5px;
		border-radius: 7px;
		position: relative;
		height: 30px;
		vertical-align: middle;
		box-shadow: 3px 5px 7px -3px rgba(0,0,0,0.75);
		-webkit-box-shadow: 3px 5px 7px -3px rgba(0,0,0,0.75);
		-moz-box-shadow: 3px 5px 7px -3px rgba(0,0,0,0.75);
	}

	.customize-control-icon-text .field-wrap .components-dropdown i {
		padding: 10px 17px;
		display: inline-block;
	}

	.customize-control-icon-text .field-wrap .components-dropdown:hover {
		cursor: pointer;
		box-shadow: 6px 7px 8px -3px rgba(0,0,0,0.75);
		-webkit-box-shadow: 6px 7px 8px -3px rgba(0,0,0,0.75);
		-moz-box-shadow: 6px 7px 8px -3px rgba(0,0,0,0.75);
	}

	.customize-control-icon-text .field-wrap .components-dropdown .components-popover__content {
		width: 230px;
		padding: 5px;
	}

	.customize-control-icon-text .field-wrap .components-dropdown .components-popover__content i {
		padding: 10px;
		display: inline-block;
	}

	.customize-control-icon-text .field-wrap .components-dropdown .components-popover__content i:after {
		content: none;
	}

	.customize-control-icon-text .field-wrap .components-dropdown .components-popover__content i:hover {
		cursor: pointer;
		color: #000;
	}

/***************************************
  Responsive Multiselect Tab Styles
*****************************************/
	.customize-block-repeater-control .components-button-group,
	.customize-radio-tab-control .components-button-group,
	.customize-responsive-multiselect-tab-control .components-button-group {
		display: flex;
	}

	.customize-block-repeater-control .components-button-group > button,
	.customize-radio-tab-control .components-button-group > button,
	.customize-responsive-multiselect-tab-control .components-button-group > button {
		flex: 1 1 auto;
		display: flex;
		justify-content: center;
	}

	.customize-color-image-group-control .components-popover.components-dropdown__content.components-custom-gradient-picker__color-picker-popover .components-popover__content {
		width: 247px;
		top: auto;
		left: -30px;
	}

	.media-field-image__preview {
		display: block;
		width: 100%;
		overflow: hidden;
		height: 79px;
		margin-bottom: 20px;
	}

	.media-field-image__preview img {
		width: 100%;
		height: 100px;
		object-fit: contain;
	}

	.media-field .media-field-image__toggle {
		border: 1px solid #545454;
	}

	.media-field-image__preview .components-responsive-wrapper div{
		display: none;
	}

	.layout-list {
		width: 180px;    
	}

	.layout-list li{
		padding: 5px 0;
		position: relative;
		font-weight: 500;
	}

	.layout-list li:hover{
		cursor: pointer;
		color: #d95f3d;
		font-weight: 500;
	}

	.layout-list li:hover:after {
		content: 'add';
		position: absolute;
		right: 0;
		background-color: #d95f3d;
		color: #fff;
		padding: 2px 5px;
		border-radius: 10px 0 0 10px;
		line-height: 1;
		font-weight: 500;
	}

/** customizer layout **/
	li#accordion-section-background_image:before {
		content: 'Core';
		font-weight: 600;
		color: #50575e;
		font-size: 13px;
		line-height: 1.3;
		padding: 10px 20px;
		display: block;
		padding-left: 10px;
	}

	li#accordion-section-background_image {
		border-top: 1px solid #e6e6e6!important;
		margin-top: 15px;
	}

	.customize-control-preset-color-picker .customize-preset-color-picker-control .control-header-trigger,
	.customize-control-preset-gradient-picker .customize-preset-gradient-picker-control .control-header-trigger {
		display: flex;
		align-items: center;
	}

	.customize-control-preset-color-picker .customize-preset-color-picker-control .control-header-trigger .customize-control-title,
	.customize-control-preset-gradient-picker .customize-preset-gradient-picker-control .control-header-trigger .customize-control-title,
	.customize-control-preset-color-picker .customize-preset-color-picker-control .control-header-trigger label,
	.customize-control-preset-gradient-picker .customize-preset-gradient-picker-control .control-header-trigger label,
	#fs_customizer_support span {
		flex: 1;
	}

	.customize-color-picker-control .control-content-wrap {
		display: flex;
		align-items: center;
	}

	li#accordion-panel-blogzee_site_identity_panel:before {
		content: 'General Options';
		font-weight: 600;
		color: #50575e;
		font-size: 13px;
		line-height: 1.3;
		padding: 10px 20px;
		display: block;
		padding-left: 10px;
	}

	li#accordion-panel-blogzee_site_identity_panel {
		margin-top: 20px;
	}

	li#accordion-section-blogzee_main_banner_section {
		border-top: 1px solid #e6e6e6!important;
		margin-top: 20px;
	}

	li#accordion-panel-blog_archive_panel:before {
		content: 'Page Options';
		font-weight: 600;
		color: #50575e;
		font-size: 13px;
		line-height: 1.3;
		padding: 10px 20px;
		display: block;
		padding-left: 10px;
	}

	li#accordion-panel-blogzee_blog_post_archive_panel {
		border-top: 1px solid #e6e6e6!important;
		margin-top: 20px;
	}

	li#accordion-section-top_header_section:before  {
		content: 'Header Options';
		font-weight: 600;
		color: #50575e;
		font-size: 13px;
		line-height: 1.3;
		padding: 10px 20px;
		display: block;
		padding-left: 10px;
	}

	li#accordion-section-top_header_section {
		border-top: 1px solid #e6e6e6!important;
		margin-top: 20px;
	}

	.customize-preset-color-picker-control .component-color-indicator,
	.customize-preset-gradient-picker-control .component-color-indicator {
		width: 28px;
		height: 28px;
		border-radius: 50%;
	}

	.block-content .components-button-group {
		width: 70%;
		padding: 5px 0;
	}

	.block-content .components-button-group .components-button {
		box-shadow: none;
		border: 1px solid #b3b3b3;
	}

	.block-content .components-button-group .components-button-group .components-button:hover {
		border: 1px solid #006ba1;
	}

	.dashicons.isActive {
		color: #0994d9;
	}

	li#accordion-section-ticker_news_section:before  {
		content: 'FrontPage Options';
		font-weight: 600;
		color: #50575e;
		font-size: 13px;
		line-height: 1.3;
		padding: 10px 20px;
		display: block;
		padding-left: 10px;
	}

	li#accordion-panel-footer_panel:before {
		content: 'Footer Options';
		font-weight: 600;
		color: #50575e;
		font-size: 13px;
		line-height: 1.3;
		padding: 10px 20px;
		display: block;
		padding-left: 10px;
	}

/** rtl css customizer **/
	body.rtl #customize-controls .control-section>h3.accordion-section-title:after {
		right: auto;
	}

	body.rtl .components-custom-gradient-picker__gradient-bar {
		border-radius: 2px;
		margin-top: 12px;
		width: 100%;
		height: 48px;
		margin-bottom: 20px;
		padding-right: 10px;
		padding-left: 0;
		margin-right: -5px;
	}

	body.rtl .components-popover.components-dropdown__content.components-custom-gradient-picker__color-picker-popover {
		left: auto!important;
		right: -270px!important;
	}

	body.rtl .customize-control-preset-gradient-picker .components-popover__content .components-popover__content {
		position: absolute!important;
		margin: 0!important;
		right: 178px;
		bottom: 50vh;
		left: auto;
	}

	body.rtl .customize-responsive-box-control .responsive-icons, 
	body.rtl .customize-responsive-range-control .responsive-icons {
		right: auto;
		left: 8px;
	}

	body.rtl .blogzee-typography-popover .components-popover__content .typo-fields .typo-field .responsive-icons {
		right: auto;
		left: 19px;
	}

	body.rtl .blogzee-typography-popover .typo-field .reset-button {
		right: auto;
		left: 80px;
	}

	body.rtl #customize-control-opinons_items .repeater-control-inner .repeater-item .item-control-fields.isShow {
		left: auto;
		right: 310px;
	}

	body.rtl #customize-control-opinons_items .repeater-control-inner .repeater-item,
	body.rtl .customize-video-playlist-control .block-item .block-header,
	body.rtl .customize-block-repeater-control .block-item .block-header {
		padding: 0 12px 0 0px;
	}

	body.rtl .customize-color-group-control .components-popover__content .components-popover__content, .customize-color-group-picker-control .components-popover__content .components-popover__content, 
	body.rtl .customize-color-image-group-control .components-popover__content .components-popover__content, .customize-color-picker-control .components-popover__content .components-popover__content, 
	body.rtl .customize-preset-color-picker-control .components-popover__content .components-popover__content {
		position: absolute!important;
		left: auto;
		bottom: 35vh;
		top: auto;
		right: 340px!important;
	}

	body.rtl .customize-color-image-group-control .components-popover.components-dropdown__content.components-custom-gradient-picker__color-picker-popover .components-popover__content {
		width: 247px;
		top: auto;
		left: auto;
		right: 425px!important;
	}

	body.rtl .post-query-field .components-popover.components-dropdown__content {
		left: auto!important;
		right: 80px;
	}

	body.rtl .layout-list li:hover:after {
		right: auto;
		left: 0px;
		border-radius: 0 10px 10px 0;
	}

	body #customize-control-opinons_items .repeater-control-inner .repeater-item .item-heading,
	body .repeater-control-inner .repeater-item .item-heading-wrap .display-icon, 
	body .repeater-control-inner .repeater-item .item-heading-wrap .item-heading {
		cursor: all-scroll;
	}

	.customize-control-title,
	label.components-flex-item {
		font-size: 13px;
		letter-spacing: 0.2px;
		line-height: 1.75;
		font-weight: 600;
	}

	.customize-control-description {
		margin-top: 5px;
	}

/* color dropdown fixed */
	#customize-theme-controls .customize-pane-child.accordion-section-content {
		height: 100%;
	}

	.components-custom-gradient-picker .components-popover__content {
		width: 250px;
	}

	#sub-accordion-section-blogzee_sidebar_options_section .customize-control-radio-image .buttonset label.ui-button {
		flex: 0 1 31%;
	}

/*************************
  Radio Bubble Control
*************************/
	.customize-block-repeater-control span.radio-bubble.isActive,
	.customize-radio-bubble-control span.radio-bubble.isActive {
		background: #007cba;
		border-color: #007cba;
		color: #fff;
	}

	.customize-block-repeater-control span.radio-bubble,
	.customize-radio-bubble-control span.radio-bubble,
	.customize-radio-bubble-control div.radio-bubble {
		cursor: pointer;
		padding: 4px;
		border: 1px solid #fff;
		border-radius: 5px;
		font-size: 10px;
		margin: 4px;
		background: #fff;
		box-shadow: 1px 1px 3px 3px #e4dddd;
	}

	.radio-bubbles.column-2 {
		display: flex;
		padding: 0 5px;
	}

	.radio-bubbles.column-4 {
		padding: 0 5px;
		text-align: center;
	}

	.radio-bubbles.column-2 > span,
	.radio-bubbles.column-4 > span {
		flex: 1;
		text-align: center;
		flex-wrap: wrap;
	}

	.customize-block-repeater-control .column-4 span.radio-bubble,
	.customize-radio-bubble-control .column-4 span.radio-bubble {
		width: 50px;
		display: inline-block;
	}

	span.radio-bubble:hover {
		background: #007cba;
		border-color: #007cba;
		color: #fff;
	}

	#customize-control-main_banner_date_filter,
	.components-popover__content .components-dropdown__content .radio-bubbles.column-4 {
		box-shadow: 0px 4px 4px 0 rgb(181 181 181 / 75%);
		-webkit-box-shadow: 0px 4px 4px 0 rgb(181 181 181 / 75%);
		-moz-box-shadow: 0px 4px 4px 0 rgb(181 181 181 / 75%);
		background-color: #fff;
		margin-top: 0;
		padding-bottom: 10px;
		margin-bottom: 25px;
	}

	#customize-control-main_banner_post_filter,
	.components-popover__content .components-dropdown__content .radio-bubbles.column-2 {
		box-shadow: 0px -2px 5px 0px rgba(181,181,181,0.75);
		-webkit-box-shadow: 0px -2px 5px 0px rgba(181,181,181,0.75);
		-moz-box-shadow: 0px -2px 5px 0px rgba(181,181,181,0.75);
		background-color: #fff;
		margin-bottom: 0;
		padding: 10px 5px 5px 5px;
		box-sizing: border-box;
		margin-top: 15px;
	}

	.post-query-field .radio-bubbles.column-2 {
		padding: 5px 5px 8px;
	}

	.post-query-field .radio-bubbles.column-4 {
		padding: 8px 5px 10px;
		margin-bottom: 0 !important;
	}

	.post-query-field .radio-bubbles.column-2>span,
	.post-query-field .radio-bubbles.column-4>span {
		line-height: 2;
	}

/* Repeater popup */
	.customize-control-block-repeater .components-popover {
		position: fixed!important;
	}

	.customize-control-block-repeater .components-dropdown__content .components-popover__content {
		padding: 10px;
		height: 370px;
		overflow-y: scroll;
	}

	.customize-control-block-repeater .components-popover.components-dropdown__content .components-popover__content .components-popover__content {
		height: 400px;
		overflow-y: scroll;
	}

/** Typography **/
	.blogzee-typography-popover .components-dropdown__content .components-popover__content {
		padding: 10px;
		overflow-y: hidden;
	}

	.customize-color-group-control .group-tab-panel {
		padding: 5px;
	}

	.customize-control-block-repeater .components-dropdown__content.-popover-content .components-popover__content  {
		height: auto;
		overflow: initial;
	}

	@media (min-width: 1600px ) {
		.customize-control-block-repeater .components-dropdown__content .components-popover__content {
			height: 460px;
		}
	}

	.customize-border-control .control-inner .border-width-element .components-base-control.components-range-control {
		position: absolute;
		bottom: 0;
		right: 20px;
		width: 87%;
	}

/** radio button **/
  	.customize-control.customize-control-section-heading-toggle .dashicons {
		padding-top: 3px;
	}

	.customize-control.customize-control-section-tab,
	.customize-control.customize-control-toggle-button{
		padding: 0;
		box-sizing: initial;
		background-color: initial;
  	}

/* customizer space control */
    #customize-control-top_header_datetime_color .customize-color-picker-control {
     	margin-bottom: 0;
    }

    .customize-spacing-control .field-main .control-title {
		display: flex;
		justify-content: space-between;
		-webkit-justify-content: space-between;
		align-items: center;
		-webkit-align-items: center;
    }

	.customize-spacing-control .field-main .control-title .customize-control-title {
		margin-bottom: 0;
	}

	.customize-spacing-control .field-main .control-title .dashicon:before {
		margin: 3px 6px 0 0;
	}

     .customize-spacing-control .field-wrap {
      	padding-top: 10px;
     }

    .customize-spacing-control .responsive-icons .control-reset {
		font-size: 12px;
		text-align: left;
		padding-top: 2px;
    }

/* Front sections ui */
#sub-accordion-section-blogzee_full_width_section #customize-control-homepage_content_order .sort-item.full_width_section,
#sub-accordion-section-blogzee_leftc_rights_section #customize-control-homepage_content_order .sort-item.leftc_rights_section,
#sub-accordion-section-blogzee_lefts_rightc_section #customize-control-homepage_content_order .sort-item.lefts_rightc_section,
#sub-accordion-section-blogzee_bottom_full_width_section #customize-control-homepage_content_order .sort-item.bottom_full_width_section,
#sub-accordion-section-blogzee_video_playlist_section #customize-control-homepage_content_order .sort-item.video_playlist {
 	border-top: 2px solid #0071a1;
}

.customize-control-color-group .group-tab-panel,
.customize-control-color-group .components-popover__content {
	width: min-content;
	margin-left: 5px;
}

/** color dropdown design **/
	.components-custom-gradient-picker__markers-container .components-popover.components-dropdown__content.components-color-palette__custom-color-dropdown-content {
		top: 120px!important;
	}

	.customize-upsell-control .upsell-inner-wrap .button-icon{
		text-align: center;
		background-color: #152384;
		border-radius: 5px;
	}

	.customize-upsell-control .upsell-inner-wrap .button-icon .components-button.is-primary {
		background-color: #152384;
		-webkit-box-shadow: 2px 2px 5px rgb(0 0 0 / 30%);
		box-shadow: 2px 2px 5px rgb(0 0 0 / 30%);
		border-radius: 5px;
	}

	.customize-upsell-control .upsell-inner-wrap .button-icon .components-button.is-primary:hover {
		box-shadow: 2px 2px 5px rgb(0 0 0 / 30%), inset 0 0 0 99999px rgb(0 0 0 / 20%);
		-webkit-box-shadow: 2px 2px 5px rgb(0 0 0 / 30%), inset 0 0 0 99999px rgb(0 0 0 / 20%);
	}

	.customize-upsell-control .upsell-inner-wrap {
		position: relative;
		padding: 0;
		background: #152384;
		border-radius: 5px;
	}

	.customize-upsell-control .upsell-inner-wrap .button-icon {
		height: 28px;
		transition: 0.3s;
	}

	.customize-upsell-control .upsell-inner-wrap .components-dropdown {
		width: 100%;
	}

	.customize-upsell-control .upsell-inner-wrap .button-icon i {
		color: #ffffff;
		font-size: 15px;
		vertical-align: middle;
		position: absolute;
		left: 8px;
		top: 5px;
	}

	.upsell-preview-frame {
		position: absolute;
		visibility: hidden;
	}

	.customize-upsell-control .upsell-inner-wrap img {
		border-radius: 5px;
		border: 2px solid #152384;
		right: 0;
		z-index: 99;
		position: absolute;
		bottom: auto;
		top: 39px;
		width: 170px;
		height: auto;
	}

	.customize-upsell-control .upsell-inner-wrap:hover img {
		visibility: visible;
	}

	.customize-upsell-control .upsell-inner-wrap:hover:after {
		content: '';
		position: absolute;
		right: 80px;
		top: 103%;
		width: 0;
		height: 0;
		border-left: 10px solid transparent;
		border-right: 10px solid transparent;
		border-bottom: 10px solid #152384;
		clear: both;
		background: transparent;
	}

	.customize-upsell-control .upsell-inner-wrap .upsell-button {
		width: 100%;
		padding-left: 30px;
		padding-top: 2px;
	}

	.customize-upsell-control .upsell-inner-wrap .button-icon:hover{
		box-shadow: 2px 2px 5px rgb(0 0 0 / 30%), inset 0 0 0 99999px rgb(0 0 0 / 20%);
		-webkit-box-shadow: 2px 2px 5px rgb(0 0 0 / 30%), inset 0 0 0 99999px rgb(0 0 0 / 20%);
	}

	.upsell-inner-wrap.request-button:hover:after {
		display: none;
	}

	li#accordion-panel-blogzee_archive_panel:before, 
	li#accordion-panel-blogzee_site_identity_panel:before, 
	li#accordion-section-top_header_section:before, 
	li#accordion-section-background_image:before, 
	li#accordion-section-main_banner_section:before,
	li#accordion-section-main_banner_section:before {
		font-weight: 600;
		color: #2271b1;
		font-size: 13px;
		line-height: 1.3;
		padding: 7px 20px 7px 10px;
		display: block;
		text-align: center;
		border-bottom: 1px solid #f1f1f1;
	}

/** Additional **/
	.customize-border-control .block-header {
		position: absolute;
	}

	.customize-control .dashicons,
	.customize-control .dashicons-before:before {
		width: 18px;
		height: 18px;
		font-size: 15px;
		text-decoration: none;
	}

	.customize-control button.components-range-control__reset {
		text-indent: -999px;
		box-shadow: none;
	}

	.customize-control button.components-range-control__reset:hover {
		text-indent: -999px!important;
		box-shadow: none!important;
	}

	.customize-control button.components-range-control__reset:before {
		content: "\f531";
		font-family: dashicons;
		display: inline-block;
		line-height: 1;
		font-weight: 400;
		font-style: normal;
		text-decoration: inherit;
		text-transform: none;
		text-rendering: auto;
		width: 17px;
		height: 17px;
		font-size: 17px;
		vertical-align: top;
		text-align: center;
		text-indent: initial;
	}

	.customize-control button.components-range-control__reset:hover:before {
		color: #02abff;
	}

	.blogzee-color-control-popover .blogzee-group-tab-panel .components-tab-panel__tabs {
		display: flex;
		margin: 0;
		margin-top: -10px;
		margin-left: -10px;
		margin-right: -10px;
		padding-bottom: 10px;
	}

	.components-dropdown__content .components-popover__content {
		padding: 12px 12px;
	}

	.customize-control-color .wp-picker-holder {
		margin-left: -10px;
	}

	.components-dropdown.blogzee-picker-one .components-popover.components-dropdown__content {
		margin-left: 35px;
	}

	.components-dropdown.blogzee-picker-one .components-popover.components-dropdown__content .components-popover__arrow {
		margin-left: -35px;
	}

	.picker-buttons-wrap .button-action:hover {
		cursor: pointer;
	}

/*** Overriding popup **/
	.blogzee-color-control-popover .components-popover__content,
	.blogzee-border-control-popover .components-popover__content {
		background-color: #fff;
		box-shadow: 0px 2px 4px 2px #0000001c;
		border-radius: 4px;
	}

	.blogzee-color-control-popover .components-popover__content .components-input-control__container,
	.blogzee-border-control-popover .components-popover__content .components-input-control__container {
		width: 100%;
	}

	.blogzee-color-control-popover .components-popover__content .components-input-control__container  .components-input-control__container input {
		height: 35px;
		min-height: 35px;
	}

	.blogzee-color-control-popover .components-popover__arrow .components-popover__triangle-border,
	.blogzee-typography-popover.components-popover .components-popover__arrow .components-popover__triangle-border {
		stroke: #e6e6e6c9;
	}

	.blogzee-color-control-popover .components-circular-option-picker__custom-clear-wrapper,
	.blogzee-color-control-popover .components-button.is-secondary.is-small {
		display: none;
	}

	.components-color-picker .components-base-control.components-input-control {
		margin-left: -5px;
	}

	.components-color-palette__custom-color-dropdown-content .components-popover__content{
		margin-left: 15px;
		box-shadow: 0px 2px 4px 2px #0000001c;
	}

	.components-color-palette__custom-color-dropdown-content .components-popover__content .react-colorful {
		width: 190px;
	}

	.components-color-palette__custom-color-dropdown-content .components-popover__content .components-color-picker {
		padding: 0;
	}

	.components-color-palette__custom-color-dropdown-content .components-input-control__container {
		width: 100%;
	}

	.remove-item .dashicons-trash {
		background-color: #ff4e7d;
		color: #fff;
		font-size: 12px;
		width: 21px;
		height: 15px;
		vertical-align: middle;
		padding-top: 4px;
		padding-bottom: 2px;
		border-radius: 50%;
		-webkit-box-shadow: 1px 1px 1px 1px rgb(255 0 0 / 48%);
		box-shadow: 1px 1px 1px 1px rgb(255 0 0 / 48%);
		margin-left: -10px;
		margin-top: -12px;
		line-height: 1.1;
		padding-left: 1px;
		text-decoration: none;
	}

	.remove-item .dashicons-trash {
		background-color: #f7275f;
	}

	button:hover {
		cursor: pointer;
	}

	li#accordion-panel-blogzee_site_identity_panel:before,
	li#accordion-section-top_header_section:before,
	li#accordion-section-main_banner_section:before,
	li#accordion-panel-blog_archive_panel:before,
	li#accordion-section-background_image:before,
	li#accordion-panel-footer_panel:before {
		font-weight: 600;
		color: #2271b1;
		font-size: 13px;
		line-height: 1.3;
		padding: 7px 20px;
		display: block;
		padding-left: 10px;
		text-align: center;
		border-bottom: 1px solid #f1f1f1;
	}

	ul.customize-pane-child.accordion-sub-container li.accordion-section.cannot-expand {
		margin-left: 0!important;
		margin-right: 0!important;
		margin-bottom: 15px!important;
	}

		#customize-theme-controls .customize-pane-parent #accordion-section-themes {
		margin: 0;
	}

	#customize-theme-controls #accordion-section-blogzee_about_section .accordion-section-title:hover:after {
		color: #ffffff;
	}

	#sub-accordion-panel-blogzee_global_panel li.accordion-section.cannot-expand,
	#sub-accordion-panel-blogzee_header_panel li.accordion-section.cannot-expand,
	#sub-accordion-panel-blogzee_front_sections_panel li.accordion-section.cannot-expand,
	#sub-accordion-panel-blogzee_blog_post_archive_panel li.accordion-section.cannot-expand,
	#sub-accordion-panel-blogzee_page_panel li.accordion-section.cannot-expand,
	#sub-accordion-panel-nav_menus li.accordion-section.customize-info,
	#sub-accordion-panel-widgets li.accordion-section.customize-info {
		margin: 0;
		margin-bottom: 10px;
	}

	#sub-accordion-panel-blogzee_global_panel li.accordion-section,
	#sub-accordion-panel-blogzee_site_identity_panel li.accordion-section,
	#sub-accordion-panel-blogzee_header_panel li.accordion-section,
	#sub-accordion-panel-blogzee_front_sections_panel li.accordion-section,
	#sub-accordion-panel-blogzee_blog_post_archive_panel li.accordion-section,
	#sub-accordion-panel-blogzee_page_panel li.accordion-section,
	#sub-accordion-panel-nav_menus li.accordion-section,
	#sub-accordion-panel-widgets li.accordion-section,
	#sub-accordion-panel-blogzee_site_identity_panel li.accordion-section,
	#sub-accordion-panel-blog_archive_panel li.accordion-section,
	#sub-accordion-panel-blogzee_theme_header_panel li.accordion-section,
	#sub-accordion-panel-blog_single_section_panel li.accordion-section,
	#sub-accordion-panel-page_setting_panel li.accordion-section,
	#sub-accordion-panel-blogzee_colors_panel li.accordion-section,
	#sub-accordion-panel-general_settings_panel li.accordion-section {
		border: none;
		background: #fff;
		margin: 0 10px;
	}

	#accordion-section-add_menu {
		padding-right: 20px;
		padding-bottom: 15px;
	}

	#accordion-section-menu_locations {
		padding-top: 10px;
	}

	#accordion-section-blogzee_seo_misc_section,
	#accordion-section-title_tagline {
		margin-top: 10px;
	}

	li#accordion-panel-blogzee_site_identity_panel:before{
		content: 'General Options';
	}

	li#accordion-panel-main_banner_section:before{
		content: 'Banner Options';
	}

	li#accordion-panel-blog_archive_panel:before{
		content: 'Archive/Posts Options';
	}

	li#accordion-panel-background_image:before{
		content: 'Core';
	}

	li#accordion-panel-blogzee_site_identity_panel:before,
	li#accordion-panel-blogzee_theme_header_panel:before,
	li#accordion-section-ticker_news_section:before,
	li#accordion-panel-blog_archive_panel:before,
	li#accordion-section-background_image:before,
	li#accordion-panel-archive_panel:before {
		font-weight: 600;
		color: #2271b1;
		font-size: 13px;
		line-height: 1.3;
		padding: 7px 20px;
		display: block;
		padding-left: 10px;
		text-align: center;
		border-bottom: 1px solid #f1f1f1;
	}

	li#accordion-panel-archive_panel:before {
		content: 'Page Options';
	}

	ul.customize-pane-child.accordion-sub-container li.accordion-section.cannot-expand {
		margin-left: 0!important;
		margin-right: 0!important;
		margin-bottom: 15px!important;
	}

 /**********************************
  3.0 Item Sortable Control Styles
**********************************/
    .customize-item-sortable-control .sort-list .sort-item {
		border: 1px solid #ddd;
		color: #556068;
		background: #fff;
		cursor: move;
		font-size: 12px;
		line-height: 38px;
		margin: 0 0 10px;
		border-radius: 3px;
		display: flex;
		justify-content: center;
		align-items: center;
		padding-left: 15px;
	}

	.customize-item-sortable-control .sort-list .sort-item.invisible {
		opacity: .6;
	}

	.customize-item-sortable-control .sort-list .sort-item span.movable-field-icon.dashicon {
		border-left: 1px solid #ddd;
		padding: 0 10px;
		cursor: move;
	}

	.customize-item-sortable-control .sort-list .sort-item span.movable-field-icon.dashicon:before {
		font-size: 12px;
		cursor: move;
	}

	.customize-item-sortable-control .sort-list .sort-item .sort-title {
		flex: 0 1 85%;
	}

	.customize-control-categories-multiselect {
		z-index: 99;
		position: relative;
	}

	.customize-item-sortable-control .sort-list .sort-item .redirect-icon {
		width: initial;
		height: initial;
	}
	
	.customize-item-sortable-control .sort-list .sort-item .redirect-icon:before {
		font-size: 14px;
		display: block;
		padding: 10px;
		line-height: 18px;
		background: #efefef;
	}

	.customize-item-sortable-control .sort-list .sort-item .redirect-icon:hover:before {
		background: #f5f5f5;
	}

	.wp-customizer .media-modal {
		z-index: 5600000;
	}

/*
========================================================
31.0 Loader Css
========================================================
*/

	:root {
		--nm-loading-color : #1b8415;
		--nm-loader-icon-width: 20px;
		--nm-loader-icon-height: 20px;
	}

	.blogzee_loading_box {
		text-align: center;
	}

	.box {
		display: inline-block;
		border: 1px solid #289dcc;
		border-radius: 3px;
		font-size: 3px;
		position: relative;
		margin-bottom: 0.25em;
		vertical-align: top;
		transition: .3s color,.3s border,.3s transform,.3s opacity;
		text-align: center;
		vertical-align: middle;
		width: 50px;
		height: 30px;
		top: 0;
		padding-bottom: 0;
		padding: 5px;
		vertical-align: middle;
		padding-top: 15px;
		margin: 5px;
	}

	.box.active {
		margin: 4px;
		border-width: 2px;
		border-color: #2271b1;
	}

	.blogzee_loading_box button {
		display: inline-block;
		background: transparent;
		border: 2px solid #289dcc;
		color: white;
		padding: 0.5em 1em;
		border-radius: 5px;
		font-size: 20px;
	}

	.blogzee_loading_box [class*=loader-] {
		display: inline-block;
		width: var(--nm-loader-icon-width);
		height: var(--nm-loader-icon-height);
		color: inherit;
		vertical-align: middle;
		pointer-events: none;
	}

	.blogzee_loading_box .loader-1 {
		border: 0.2em dotted var(--nm-loading-color);
		border-radius: 50%;
		-webkit-animation: 1s loader-1 linear infinite;
		animation: 1s loader-1 linear infinite;
	}

	@-webkit-keyframes loader-1 {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	@keyframes loader-1 {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

/* Tooltip */
	.ui-button {
		position: relative;
	}

	.blogzee-tooltip-text {
		position: absolute;
		top: -30px;
		right: 0;
		background-color: #333;
		color: #fff;
		padding: 2px 7px;
		font-size: 10px;
		visibility: hidden;
		opacity: 0;
		-webkit-transition: all 200ms ease-in-out 0s;
		-moz-transition: all 200ms ease-in-out 0s;
		-o-transition: all 200ms ease-in-out 0s;
		transition: all 200ms ease-in-out 0s;
	}

	.blogzee-tooltip-text:before {
		content: '';
		position: absolute;
		left: 50%;
		bottom: -5px;
		width: 0;
		height: 0;
		margin-left: -6px;
		border-left: 6px solid transparent;
		border-right: 6px solid transparent;
		border-top: 6px solid #333;
	}

	.ui-button:hover .blogzee-tooltip-text {
		visibility: visible;
		opacity: 1;
	}

/** number field */
	.customize-number-control .field-main .control-title {
		display: flex;
		justify-content: space-between;
	}

	.customize-number-control .field-main .control-title .dashicon.isactive {
		color: #007cba;
	}

	.customize-number-control .field-main .control-title .dashicon:hover {
		cursor: pointer;
		color: #007cba;
	}

/** spacing **/
	.responsive-icons .dashicons:hover {
		cursor: pointer;
		color: #007cba;
	}

	.customize-spacing-control .responsive-icons .dashicons.isactive {
		color: #007cba;
	}

	.blogzee-select-two-dropdown {
		z-index: 999999;
	}

/* background media */
.blogzee-color-control-popover .more-settings > div .components-input-control__label {
    font-size: 13px;
    text-transform: none;
    font-weight: 400;
}

.blogzee-color-control-popover .more-settings > div div.components-input-control__label {
  	margin-bottom: 9px;
}

#sub-accordion-section-advertisement_section .image-holder .image-element img {
    height: 125px;
    display: block;
    width: 100%;
}

/* Customizer */
#customize-theme-controls .customize-pane-parent,
#customize-controls #customize-theme-controls .customize-pane-child {
    background: #e7f1ff;
	height: 100%;
}

#customize-controls .customize-info {
	border: none;
	margin: 0;
}

.customize-control-async-multiselect input[type="text"] {
	min-height: initial;
}

.customize-control.customize-control-checkbox .customize-inside-control-row {
	padding: 0;
}

.customize-control-checkbox input[type="checkbox"] {
	width: 14px;
	min-width: 14px;
	height: 14px;
	margin-bottom: 3px;
}

.customize-control-checkbox input[type="checkbox"]:before {
	width: 18px;
	height: 18px;
}

.customize-control-checkbox input[type="checkbox"] ~ label {
	margin-left: 5px;
	display: inline;
}

/*  Info Box control */
	.customize-info-box-control .info-box-label {
		margin: 0;
		font-size: 13px;
	}
	
	.customize-info-box-control .info-box-button {
		display: block;
		text-align: center;
		background-color: #5fa1ff;
	}

	.components-button.is-primary:hover:not(:disabled):hover {
		background-color: #448bf1;
	}

/* accordion */
	.customize-pane-parent > .accordion-section,
	.customize-pane-child > .accordion-section {
		background: #fff;
		box-sizing: border-box;
		margin: 0 7px 0 7px;
	}

	#customize-controls .control-section > h3.accordion-section-title {
		display: flex;
		align-items: center;
		font-size: 13px;
		font-weight: 400;
		line-height: 13px;
		border-left: none;
		border-bottom: none;
	}

	#customize-controls .control-section > h3.accordion-section-title button {
		padding: 14px 26px;
	}

	#accordion-section-about_section h3.accordion-section-title,
	#accordion-section-about_section h3.accordion-section-title .accordion-trigger {
		background-color: #5fa1ff !important;
		color: #fff !important;
	}

	#customize-controls .control-section > h3.accordion-section-title:after {
		font-size: 12px;
		top: calc(50% - 6px);
		right: 23px;
		color: inherit;
	}

	#accordion-section-about_section.control-section h3.accordion-section-title:hover:after {
		color: #fff;
	}

/* section tab */
	.customize-section-tab-control .components-tab-panel__tabs {
		margin-top: -5px;
		margin-left: -24px;
		margin-right: -24px;
		border: 1px solid #dddddd;
		background: #ffffff;
	}
	
	.customize-section-tab-control .components-tab-panel__tabs button {
		-webkit-box-flex: 1;
		flex: 1 1 0;
		display: -webkit-box;
		display: flex;
		-webkit-box-align: center;
		align-items: center;
		-webkit-box-pack: center;
		justify-content: center;
		-webkit-justify-content: center;
		font-size: 11.5px;
		font-weight: 500;
		letter-spacing: 1px;
		text-transform: uppercase;
		height: 40px;
		box-sizing: content-box;
		padding: 0 10px;
		border: 0;
		border-top: 2px solid transparent;
	}
	
	.customize-section-tab-control .components-tab-panel__tabs button.active-tab {
		border-top-color: #5fa1ff;
		color: #5fa1ff;
		background-image: linear-gradient( 180deg, #f3f5f6, #ffffff);
	}

	#customize-theme-controls #accordion-section-blogzee_bottom_footer_section,
	#customize-theme-controls .customize-pane-parent #accordion-panel-page_setting_panel,
	#customize-theme-controls .customize-pane-parent #accordion-section-mobile_options_section,
	#customize-theme-controls .customize-pane-parent #accordion-panel-theme_header_panel,
	#customize-theme-controls .customize-pane-parent #accordion-section-video_playlist_section,
	#customize-theme-controls .customize-pane-parent #accordion-section-bottom_footer_section,
 	#customize-theme-controls .customize-pane-parent #accordion-section-header_builder_section_settings,
	#accordion-section-carousel_section,
	#accordion-section-page_settings_section {
		margin-bottom: 15px !important;
	}

	/* range control */
	.components-range-control__root .components-range-control__wrapper {
		color: #5fa1ff;
	}

	.components-range-control__wrapper span:nth-child(2),
	.components-range-control__track {
		height: 2px;
		margin-top: 14px;
	}

	.components-range-control__thumb-wrapper {
		height: 10px;
		width: 10px;
	}

	.components-range-control__thumb-wrapper span {
		background-color: #5fa1ff;
	}

	.customize-control-number-range .control-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 5px;
	}

  /* list style */
    .customize-control .components-elevation {
		box-shadow: none;
		border-bottom: 1px solid #efefef;
    }

    .components-base-control.components-toggle-control {
     	margin-bottom: 0;
    }

    .customize-control {
		box-sizing: border-box;
		padding: 0 16px 10px;
		background-color: #fff;
    }

	.customize-control {
		margin: 0;
	}

	.customize-control-section-tab {
		margin-bottom: 12px;
	}

	.customize-control-info-box,
	#customize-control-custom_logo.customize-control,
	.customize-control.customize-control-site_icon,
	#customize-control-blogname.customize-control,
	#customize-control-site_title_tag_for_frontpage.customize-control,
	#customize-control-site_title_typo.customize-control,
	#customize-control-header_textcolor.customize-control,
	#customize-control-site_date_to_show.customize-control,
	#customize-control-preloader_styles.customize-control,
	#customize-control-website_layout.customize-control,
	#customize-control-website_layout_background_color.customize-control,
	#customize-control-aos_animation_effects.customize-control,
	#customize-control-post_title_hover_effects.customize-control,
	#customize-control-social_icons_target.customize-control,
	#customize-control-global_button_redirect.customize-control,
	#customize-control-global_button_typo.customize-control,
	#customize-control-global_button_color.customize-control,
	#customize-control-global_button_border.customize-control,
	#customize-control-global_button_box_shadow_initial.customize-control,
	#customize-control-standard_post_format_icon_picker.customize-control,
	#customize-control-site_breadcrumb_option.customize-control,
	#customize-control-breadcrumb_typo.customize-control,
	#customize-control-stt_text.customize-control,
	#customize-control-stt_color_group.customize-control,
	#customize-control-social_share_repeater.customize-control,
	#customize-control-theme_color.customize-control,
	.customize-control.customize-control-preset,
	#customize-control-advertisement_repeater.customize-control,
	#customize-control-heading_one_typo.customize-control,
	#customize-control-typography_presets.customize-control,
	#customize-control-sidebar_border_radius.customize-control,
	#customize-control-widgets_inner_background_color.customize-control,
	#customize-control-sidebar_block_title_typography.customize-control,
	#customize-control-sidebar_heading_one_typography.customize-control,
	#customize-control-sidebar_pagination_button_color.customize-control,
	#customize-control-show_top_header_on_mobile.customize-control,
	#customize-control-show_top_header_search_on_mobile.customize-control,
	#customize-control-show_main_banner_excerpt_mobile_option.customize-control,
	#customize-control-show_video_playlist_in_mobile.customize-control,
	#customize-control-show_archive_excerpt_mobile_option.customize-control,
	#customize-control-show_left_sidebar_mobile_option.customize-control,
	#customize-control-show_breadcrumb_on_mobile.customize-control,
	#customize-control-top_header_date_time_option.customize-control,
	#customize-control-top_header_section_background.customize-control,
	#customize-control-header_layout.customize-control,
	#customize-control-header_sortable_options.customize-control,
	#customize-control-header_background.customize-control,
	#customize-control-header_vertical_padding.customize-control,
	#customize-control-menu_options_menu_alignment.customize-control,
	#customize-control-menu_cutoff_option.customize-control,
	#customize-control-main_menu_typo.customize-control,
	#customize-control-header_menu_color.customize-control,
	#customize-control-enable_menu_background.customize-control,
	#customize-control-header_sub_menu_color.customize-control,
	#customize-control-search_type.customize-control,
	#customize-control-search_view_all_button_text.customize-control,
	#customize-control-search_icon_color.customize-control,
	#customize-control-custom_button_label.customize-control,
	#customize-control-custom_button_icon_prefix_suffix.customize-control,
	#customize-control-custom_button_icon_size.customize-control,
	#customize-control-custom_button_text_typography.customize-control,
	#customize-control-custom_button_text_color.customize-control,
	#customize-control-header_custom_button_border.customize-control,
	#customize-control-theme_mode_dark_icon.customize-control,
	#customize-control-theme_mode_dark_icon_color.customize-control,
	#customize-control-canvas_menu_position.customize-control,
	#customize-control-canvas_menu_icon_color.customize-control,
	#customize-control-header_ads_banner_image.customize-control,
	#customize-control-main_banner_layouts.customize-control,
	#customize-control-main_banner_slider_categories.customize-control,
	#customize-control-main_banner_post_elements_alignment.customize-control,
	#customize-control-main_banner_show_arrows.customize-control,
	#customize-control-main_banner_show_fade.customize-control,
	#customize-control-main_banner_image_sizes.customize-control,
	#customize-control-main_banner_border_radius.customize-control,
	#customize-control-main_banner_design_post_title_typography.customize-control,
	#customize-control-carousel_layouts.customize-control,
	#customize-control-carousel_slider_categories.customize-control,
	#customize-control-carousel_post_order.customize-control,
	#customize-control-carousel_post_elements_show_title.customize-control,
	#customize-control-carousel_show_arrows.customize-control,
	#customize-control-carousel_show_fade.customize-control,
	#customize-control-carousel_image_sizes.customize-control,
	#customize-control-carousel_content_background.customize-control,
	#customize-control-carousel_design_post_title_typography.customize-control,
	#customize-control-video_playlist_api_key.customize-control,
	#customize-control-video_playlist_display_position.customize-control,
	#customize-control-video_playlist_border_radius.customize-control,
	#customize-control-video_playlist_slider_arrow.customize-control,
	#customize-control-video_playlist_active_title_typo.customize-control,
	#customize-control-video_playlist_active_title_color.customize-control,
	#customize-control-video_playlist_box_shadow.customize-control,
	#customize-control-video_playlist_slider_icon_color.customize-control,
	#customize-control-category_collection_show_count.customize-control,
	#customize-control-category_to_include.customize-control,
	#customize-control-category_collection_slider_option.customize-control,
	#customize-control-category_collection_image_size.customize-control,
	#customize-control-category_collection_typo.customize-control,
	#customize-control-archive_post_column.customize-control,
	#customize-control-archive_sidebar_layout.customize-control,
	#customize-control-archive_post_elements_alignment.customize-control,
	#customize-control-archive_category_option.customize-control,
	#customize-control-archive_image_stretch.customize-control,
	#customize-control-archive_section_border_radius.customize-control,
	#customize-control-archive_title_typo.customize-control,
	#customize-control-archive_category_info_box_icon_option.customize-control,
	#customize-control-archive_category_info_box_background.customize-control,
	#customize-control-archive_category_info_box_title_typo.customize-control,
	#customize-control-archive_tag_info_box_icon_option.customize-control,
	#customize-control-archive_tag_info_box_background.customize-control,
	#customize-control-archive_tag_info_box_title_typo.customize-control,
	#customize-control-archive_author_info_box_image_option.customize-control,
	#customize-control-archive_author_info_box_background.customize-control,
	#customize-control-archive_author_info_box_title_typo.customize-control,
	#customize-control-archive_pagination_type.customize-control,
	#customize-control-pagination_text_color.customize-control,
	#customize-control-pagination_button_label.customize-control,
	#customize-control-single_post_layout.customize-control,
	#customize-control-single_sidebar_layout.customize-control,
	#customize-control-single_article_width.customize-control,
	#customize-control-single_author_box_option.customize-control,
	#customize-control-single_post_navigation_option.customize-control,
	#customize-control-single_image_size.customize-control,
	#customize-control-single_reorder_option.customize-control,
	#customize-control-single_title_typo.customize-control,
	#customize-control-single_page_border_radius.customize-control,
	#customize-control-single_title_option.customize-control,
	#customize-control-single_author_option.customize-control,
	#customize-control-toc_heading_option.customize-control,
	#customize-control-toc_hierarchical.customize-control,
	#customize-control-related_posts_layouts.customize-control,
	#customize-control-single_post_related_posts_title.customize-control,
	#customize-control-related_posts_filter_by.customize-control,
	#customize-control-page_settings_sidebar_layout.customize-control,
	#customize-control-page_title_option.customize-control,
	#customize-control-page_image_size.customize-control,
	#customize-control-page_title_typo.customize-control,
	#customize-control-page_border_radius.customize-control,
	#customize-control-error_page_sidebar_layout.customize-control,
	#customize-control-error_page_title_text.customize-control,
	#customize-control-error_page_button_show_hide.customize-control,
	#customize-control-error_page_title_typo.customize-control,
	#customize-control-error_page_background_color.customize-control,
	#customize-control-search_page_sidebar_layout.customize-control,
	#customize-control-search_page_form_show_hide.customize-control,
	#customize-control-page_toc_heading_option.customize-control,
	#customize-control-page_toc_hierarchical.customize-control,
	#customize-control-page_toc_enable_accordion.customize-control,
	#customize-control-you_may_have_missed_no_of_columns.customize-control,
	#customize-control-you_may_have_missed_title_option.customize-control,
	#customize-control-you_may_have_missed_categories.customize-control,
	#customize-control-you_may_have_missed_post_order.customize-control,
	#customize-control-you_may_have_missed_post_elements_alignment.customize-control,
	#customize-control-you_may_have_missed_image_sizes.customize-control,
	#customize-control-you_may_have_missed_title_color.customize-control,
	#customize-control-you_may_have_missed_background_color_group.customize-control,
	#customize-control-you_may_have_missed_design_section_title_typography.customize-control,
	#customize-control-footer_widget_column.customize-control,
	#customize-control-footer_title_typography.customize-control,
	#customize-control-footer_text_color.customize-control,
	#customize-control-bottom_footer_site_info.customize-control,
	#customize-control-bottom_footer_show_social_icons.customize-control,
	#customize-control-bottom_footer_text_typography.customize-control,
	#customize-control-bottom_footer_text_color.customize-control,
	#customize-control-background_image.customize-control,
	#customize-control-site_background_animation.customize-control,
	#customize-control-cei-setting.customize-control,
	#sub-accordion-section-category_colors_section .customize-control-color-field,
	#sub-accordion-section-tag_colors_section .customize-control-color-field,
	#customize-control-nav_menu-13-name.customize-control-nav_menu_name,
	#customize-control-nav_menu_item-136.customize-control,
	#customize-control-add_menu-name.customize-control,
	#customize-control-nav_menu_locations-menu-1.customize-control,
	#customize-control-show_on_front,
	#customize-control-date_time_typography,
	#customize-control-block_title_layout,
	#customize-control-reset_setting_option,
	#customize-control-header_sticky_on_scroll_up,
	#customize-control-main_banner_trailing_slider_categories,
	#customize-control-carousel_slider_infinite_loop,
	#customize-control-footer_first_row_row_direction,
	#customize-control-footer_second_row_row_direction,
	#customize-control-footer_third_row_row_direction,
	#customize-control-bottom_footer_logo_option,
	#customize-control-footer_social_icons,
	#customize-control-footer_social_icon_color,
	#customize-control-footer_menu_hover_effect,
	#customize-control-footer_menu_typography,
	#customize-control-category_border_radius,
	#customize-control-ticker_news_render_in,
	#customize-control-ticker_news_categories ,
	#customize-control-ticker_news_post_order,
	#customize-control-ticker_news_show_thumbnail_image,
	#customize-control-ticker_news_date_icon_size,
	#customize-control-ticker_show_controller,
	#customize-control-ticker_news_border_radius,
	#customize-control-ticker_news_post_title_typo,
	#customize-control-main_banner_post_order,
	#customize-control-main_banner_trailing_post_order,
	#customize-control-main_banner_trailing_post_heading,
	#customize-control-main_banner_sidebar_block_typography,
	#customize-control-archive_image_size,
	#customize-control-page_toc_sticky_width,
	#customize-control-category_collection_orderby,
	#customize-control-carousel_post_elements_alignment {
		padding-top: 10px;
	}

	#customize-control-site_icon,
	#customize-control-blogdescription,
	#customize-control-carousel_content_background {
		margin-bottom: 16px;
	}

	/* icon picker */
		.customize-control-icon-picker .customize-icon-picker {
			display: flex;
			justify-content: space-between;
			-webkit-justify-content: space-between;
			align-items: center;
			-webkit-align-items: center;
			gap: 5px;
			position: relative;
		}

		.customize-control-icon-picker .customize-control-title {
			margin: 0;
		}

		.customize-control-icon-picker .picker-buttons-wrap {
			display: flex;
			align-items: center;
			-webkit-align-items: center;
			border: 1px solid #5fa1ff;
		}

		.customize-control-icon-picker .button-action {
			flex: 0 1 33%;
			padding: 5px 10px;
			text-align: center;
			font-size: 13px;
		}

		.customize-control-icon-picker .select-none {
			letter-spacing: 0.3px;
		}

		.customize-control-icon-picker .button-action.active {
			background: #5fa1ff;
			color: #fff;
		}

		.customize-control-icon-picker .select-upload {
			border-right: 1px solid #5fa1ff;
    		border-left: 1px solid #5fa1ff;
		}

		.customize-control-icon-picker .select-icon {
			width: 15px;
		}

		.customize-control-icon-picker .icon-picker-modal {
			height: 190px;
			overflow: auto;
			padding: 16px;
			background-color: #fff;
			box-shadow: 0 0 4px 3px rgba(174 174 174 / 20%);
			position: absolute;
			top: 45px;
			z-index: 2;
		}

		.customize-control-icon-picker .icon-picker-search {
			margin-bottom: 10px;
		}
		
		.customize-control-icon-picker .icon-item i {
			cursor: pointer;
			font-size: 16px;
			padding: 9px;
			width: 15px;
		}
		
		.customize-control-icon-picker .icon-item.selected:hover i {
		  	color: #fff;
		}
		
		.customize-control-icon-picker .icon-item.selected {
			color: #fff;
			background-color: #92bcf7;
			padding: 5px 0;
			border-radius: 2px;
		}
		
		.customize-control-icon-picker .icon-item i:hover {
		  color: #007cba;
		}
	
	/* menu */
	#menu-to-edit .customize-control.assigned-menu-locations-title,
	#menu-to-edit .customize-control.customize-control-checkbox {
		padding: 0 0 10px;
	}

	#sub-accordion-section-menu_locations .customize-section-description,
	#sub-accordion-section-static_front_page .customize-section-description {
		padding: 10px 15px;
		background-color: #fff;
	}

	/* alignment */
		.customize-radio-tab-control .components-button:nth-child(2) {
			border-right: 1px solid #5fa1ff;
			border-left: 1px solid #5fa1ff;
		}

		.customize-radio-tab-control .components-button:last-child {
			border-right: 1px solid #5fa1ff;
			margin: 0;
		}

		.customize-radio-tab-control .components-button .dashicon:before {
			font-size: 14px;
		}

	/* spacing control */
		.customize-spacing-control .field-wrap .dimensions,
		.customize-border-control .field-wrap .dimensions {
			display: flex;
			align-items: baseline;
			-webkit-align-items: baseline;
		}

		.customize-spacing-control .components-range-control__wrapper,
		.customize-border-control .components-range-control__wrapper {
			display: none;
		}

		.customize-spacing-control .components-input-control,
		.customize-border-control .components-input-control {
			margin-left: 0 !important;
		}

		.customize-spacing-control .components-range-control__root > span {
			display: none;
		}

		.customize-spacing-control .components-input-control__container,
		.customize-border-control .components-input-control__container {
			width: 50px;
			border-radius: 0;
		}

		.customize-spacing-control .field-main .field-wrap .components-input-control__backdrop,
		.customize-border-control .control-border-inner .field-wrap .components-input-control__backdrop {
			border-color: #5fa1ff;
		}

		.customize-spacing-control .field-main .field-wrap .components-base-control:first-child .components-input-control__backdrop,
		.customize-border-control .field-wrap .components-base-control:first-child .components-input-control__backdrop {
			border-right: none;
		}

		.customize-spacing-control .field-main .field-wrap .components-base-control:nth-child(3) .components-input-control__backdrop,
		.customize-border-control .field-wrap .components-base-control:nth-child(3) .components-input-control__backdrop {
			border-right: none;
			border-left: none;
		}

		.customize-spacing-control .components-base-control__field,
		.customize-border-control .components-base-control__field {
			display: flex;
			flex-direction: column;
		}

		.customize-spacing-control .components-base-control__label,
		.customize-border-control .components-base-control__label {
			font-size: 9px;
			text-transform: capitalize;
			letter-spacing: 0.6px;
			order: 2;
			margin: 5px 0 0;
		}

		.customize-spacing-control .link-wrap,
		.customize-border-control .link-wrap {
			display: flex;
			flex-direction: column;
		}

		.customize-spacing-control .link-wrap,
		.customize-border-control .link-wrap {
			pointer-events: none;
		}

		.customize-spacing-control .link-wrap .dashicon,
		.customize-border-control .link-wrap .dashicon {
			pointer-events: initial;
		}

		.customize-spacing-control .link-wrap .components-base-control__label,
		.customize-border-control .link-wrap .components-base-control__label {
			visibility: hidden;
			margin-bottom: 3px;
		}

		.customize-spacing-control .field-wrap .dimensions .link-wrap span.linked,
		.customize-border-control .field-wrap .dimensions .link-wrap span.linked {
			background-color: #fff;
			border: 1px solid #5fa1ff;
			border-left: none;
			color: #333;
			padding: 6px 6px 4px;
			line-height: 14px;
			cursor: pointer;
		}

		#customize-control-sidebar_pagination_button_padding .customize-spacing-control .field-wrap .dimensions .link-wrap span.linked,
		#customize-control-custom_button_padding .customize-spacing-control .field-wrap .dimensions .link-wrap span.linked,
		#customize-control-you_may_have_missed_image_border_radius .customize-spacing-control .field-wrap .dimensions .link-wrap span.linked {
			padding: 5px 6px 6px;
			line-height: 16px;
		}

		.customize-spacing-control .field-wrap .dimensions.isactive .link-wrap span.linked,
		.customize-border-control .field-wrap .dimensions.isactive .link-wrap span.linked {
			background-color: #5fa1ff;
			color: #fff;
			cursor: pointer;
		}

		.customize-spacing-control .field-wrap .dimensions .link-wrap span.linked:hover:before,
		.customize-border-control .field-wrap .dimensions .link-wrap span.linked:hover:before {
			font-size: 14px;
			-webkit-transition: all 0.3s ease;
			-moz-transition: all 0.3s ease;
			transition: all 0.3s ease;
		}

		.customize-spacing-control .dimensions.not-active .dashicon:hover:before,
		.customize-border-control .dimensions.not-active .dashicon:hover:before {
			color: #5fa1ff;
		}

  	/* Toggle Button */
		.customize-control-simple-toggle .components-form-toggle {
			order: 2;
		}

		.components-form-toggle .components-form-toggle__input:focus+.components-form-toggle__track,
		.customize-border-control .control-border-inner .components-dropdown button:focus {
			box-shadow: none;
			outline: none;
		}

		.components-form-toggle .components-form-toggle__track {
			background-color: #e7f1ff;
			border: 1px solid #007cba5e;
			border-radius: 9px;
			box-sizing: border-box;
			content: "";
			display: inline-block;
			height: 17px;
			transition: transfrom .2s ease;
			vertical-align: top;
			width: 32px;
		}

		.components-form-toggle .components-form-toggle__thumb {
			background-color: #5fa1ff;
			border: 5px solid #5fa1ff;
			border-radius: 50%;
			box-sizing: border-box;
			display: block;
			height: 11px;
			left: 4px;
			position: absolute;
			top: 3px;
			transition: transform .1s ease;
			width: 11px;
		}

		.components-form-toggle.is-checked .components-form-toggle__track {
			border-width: 1px;
			background-color: #5fa1ff;
		}

		.components-form-toggle.is-checked .components-form-toggle__thumb {
			transform: translateX(14px);
		}

  	/* color control */
		.customize-color-field-control .field-main {
			display: flex;
			justify-content: space-between;
			-webkit-justify-content: space-between;
			align-items: center;
			-webkit-align-items: center;
			gap: 7px;
		}

		.customize-color-field-control .customize-control-title {
			margin: 0;
		}

		.customize-color-field-control .field-wrap {
			display: flex;
			gap: 7px;
		}

		.color-indicator-wrapper {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 35px;
			height: 21px;
			border-radius: 3px;
			background-color: rgba(255, 255, 255, .8);
			background-size: 10px 10px;
			background-image: url(data:image/webp;base64,UklGRjIAAABXRUJQVlA4TCUAAAAvE8AEAA9w7/97/9/7f/7jAYraNmI6AJQ/1xvvMojof2BSvVEBAA==);
		}

    	.components-dropdown .component-color-indicator {
			width: 35px;
			height: inherit;
			border-radius: 3px;
			vertical-align: middle;
		}

		.components-popover__content .react-colorful__hue .react-colorful__pointer,
		.components-popover__content .react-colorful__alpha .react-colorful__pointer {
			width: 13px;
			height: 13px;
			margin-left: 4px;
		}

		.components-popover__content .react-colorful__alpha .react-colorful__pointer {
			margin-left: -4px;
			border: 1px solid #a9a9a9;
		}

	/* image */
		.editor-post-featured-image__preview,
		.editor-post-featured-image__toggle {
			min-height: 120px;
			width: 260px;
		}

		.blogzee-color-control-popover .editor-post-featured-image .components-button:not(:first-child) {
			margin-top: 12px;
		}

		.blogzee-color-control-popover .editor-post-featured-image .components-button.is-secondary {
			font-size: 13px;
			padding: 5px 11px;
			line-height: 1;
			height: 31px;
			box-shadow: none;
			background-color: #fbfbfb;
			color: #5fa1ff;
			letter-spacing: 0.3px;
			border-radius: 2px;
			border: 1px solid #5fa1ff;
		}

		.blogzee-color-control-popover .editor-post-featured-image .components-button.is-link {
			font-size: 13px;
			padding: 5px 11px;
			line-height: 1;
			height: 31px;
			box-shadow: none;
			background-color: #fbfbfb;
			letter-spacing: 0.3px;
			border-radius: 2px;
			border: 1px solid #cc1818;
			text-decoration: none;
			margin-left: 8px;
		}

		.blogzee-color-control-popover .editor-post-featured-image .components-button:hover {
			background-color: #f0f0f1;
		}

		.blogzee-color-control-popover .more-settings {
			max-height: 180px;
			overflow-y: auto;
			padding-right: 10px;
			margin-top: 15px;
		}

		.blogzee-color-control-popover .more-settings::-webkit-scrollbar {
			width: 4px;
		}

		.blogzee-color-control-popover .more-settings::-webkit-scrollbar-thumb {
			background-color: #5fa1ff;
			border-radius: 20px;
		}

		.blogzee-color-control-popover .more-settings::-webkit-scrollbar-track {
			box-shadow: inset 0 0 5px #0000002e;
			border-radius: 20px;
		}

		.blogzee-color-control-popover .more-settings > div {
			margin-bottom: 15px;
		}

		.blogzee-color-control-popover .more-settings > div:last-child {
			margin: 0;
		}

		.blogzee-color-control-popover .more-settings .components-button.has-icon.has-text {
			margin: 0px 0 15px;
			background: #5fa1ff;
			color: #fff;
			padding: 5px 12px;
			min-width: initial;
			height: initial;
			align-items: center;
			letter-spacing: 0.3px;
			font-size: 13px;
			gap: 0px;
		}

		.blogzee-color-control-popover .more-settings .components-button:focus,
		.blogzee-box-shadow-control-popover .inner-fields .components-button:focus {
			box-shadow: none;
		}

		.blogzee-color-control-popover .more-settings .components-button .dashicon {
			font-size: 11px;
			padding: 0;
			order: 2;
			justify-content: flex-end;
		}

		.blogzee-color-control-popover .more-settings .components-button,
		.blogzee-box-shadow-control-popover .inner-fields .components-button {
			box-shadow: none;
			border: 1px solid #5fa1ff;
		}

		.blogzee-color-control-popover .more-settings .components-button.is-primary,
		.blogzee-box-shadow-control-popover .inner-fields .components-button.is-primary {
			background-color: #5fa1ff;
		}

		.blogzee-color-control-popover .more-settings .components-button.is-secondary:hover:not(:disabled),
		.blogzee-box-shadow-control-popover .inner-fields .components-button.is-secondary:hover:not(:disabled) {
			box-shadow: inset 0 0 0px 1px #5fa1ff57;
		}

  	/* Tab */
	  	.customize-radio-tab-control .radio-tab-wrapper {
			display: flex;
			justify-content: space-between;
			-webkit-justify-content: space-between;
			align-items: center;
			-webkit-align-items: center;
			gap: 7px;
		}

		.customize-radio-tab-control .radio-tab-wrapper.double-line {
			flex-direction: column;
			align-items: initial;
		}

		.customize-control-number .customize-control-title {
			display: inline-block;
			margin: 4px 0 0;
		}

		.customize-control.customize-control-number input[type=number] {
			width: 25%;
			float: right;
		}

		.customize-control.customize-control-number input[type=number].blogzee-full-width {
			width: 100%;
			margin-top: 5px;
		}

		.customize-radio-tab-control .components-button {
			height: 29px;
			box-shadow: none;
		}

		.customize-radio-tab-control .components-button.is-icon {
			padding-top: 9px;
		}

		.customize-radio-tab-control .components-button:hover {
			background-color: #f0f0f1;
		}

		.customize-radio-tab-control .components-button.is-primary:focus:not(:disabled) {
			outline: none;
			letter-spacing: 0.3px;
			box-shadow: none;
		}

		.customize-radio-tab-control .components-button-group {
			border: 1px solid #5fa1ff;
			border-right: none;
		}

		.customize-radio-tab-control .components-button.is-primary {
			background: #5fa1ff;
			border: none;
			box-shadow: none;
			border-radius: 0;
		}

		.customize-radio-tab-control .components-button.is-primary:hover:not(:disabled) {
			background-color: #5fa1ff;
			box-shadow: none;
		}

		.customize-radio-tab-control .components-button.is-secondary:hover:not(:disabled),
		.customize-radio-tab-control .components-button:focus:not(:disabled) {
			box-shadow: none;
		}

		.customize-radio-tab-control .customize-control .dashicon {
			padding-top: 3px;
		}

		.customize-radio-tab-control .customize-control .dashicon:before {
			font-size: 14px;
		}

	/* dashicon */
        .customize-control .dashicon {
            box-shadow: none;
            font-family: 'dashicons';
        }

        .customize-control .dashicon:hover:not(:disabled) {
            box-shadow: none;
        }
		
		.customize-control .dashicon:before {
			font-size: 12px;
			cursor: pointer;
		}

	/* reset dashicon */
		.reset-button.dashico,
		.customize-control-number-range .components-range-control__root button.components-range-control__reset {
			color: #5fa1ff;
		}
		
		.reset-button.dashicon:before,
		.customize-control-number-range .components-range-control__root button.components-range-control__reset:before {
			font-size: 10px;
			display: block;
			cursor: pointer;
		}

		.customize-number-range-control .reset-button.dashicon:before {
			margin-top: 3px;
		}

		.reset-button.dashicon:hover:before,
		.customize-control-number-range .components-range-control__root button.components-range-control__reset:hover:before {
			transform: scale(1.2);
			-webkit-transition: 0.3s ease;
			transition: 0.3s ease;
		}

		.reset-button.dashicon:hover:not(:disabled):hover,
		.customize-control-number-range .components-range-control__root button.components-range-control__reset:focus:not(:disabled) {
			color: #02abff;
			box-shadow: none;
		}

		.customize-control-number-range .components-range-control__root button.components-range-control__reset {
			position: absolute;
			top: -30px;
			right: 50px;
		}

	/* border control */
		.customize-border-control .control-border-inner .control-title-wrapper {
			display: flex;
			justify-content: space-between;
			gap: 7px;
			align-items: center;
		}

		.customize-border-control .control-inner {
			display: flex;
			justify-content: flex-end;
		}

		.customize-border-control .components-dropdown div {
			line-height: 16px;
			background: #5fa1ff;
			color: #fff;
			border-radius: 2px;
			padding: 2px 10px 5px;
			letter-spacing: 0.3px;
			cursor: pointer;
			font-size: 12px;
			text-transform: capitalize;
		}

		.customize-border-control .field-wrap .dimensions {
			justify-content: flex-end;
			-webkit-justify-content: flex-end;
			margin-top: 7px;
		}

		/* popup */
		.customize-border-control .field-wrap .components-range-control__wrapper,
		.customize-border-control .field-wrap .components-button {
			display: none;
		}

		.customize-border-control .field-wrap .components-range-control__root {
			display: initial;
		}

		.customize-border-control .control-inner .components-dropdown:nth-child(2) {
			display: flex;
			margin-left: 8px;
		}

		.customize-border-control .control-inner .components-dropdown .component-color-indicator {
			width: 40px;
			height: 23px;
		}
		
		/* border popup */
		.type-wrapper {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			width: 90px;
		}

		.type-wrapper .components-button:focus:not(:disabled) {
			box-shadow: none;
		}

		.type-wrapper button {
			min-height: 23px;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 5px 2px;
			font-size: 11px;
			letter-spacing: 0.4px;
			cursor: pointer;
		}

		.type-wrapper button:hover {
			background-color: #5fa1ff;
			color: #fff;
		}

		.type-wrapper button.type:hover:before {
			border-color: #fff;
		}

		button.type:not(:first-child):before {
			content: '';
			width: 18px;
			display: block;
			border-bottom-width: 2px;
			border-bottom-color: #000;
		}

		button.dotted:before {
			border-bottom-style: dotted;
		}

		button.type.dashed:before {
			border-bottom-style: dashed;
		}

		button.type.solid:before {
			border-bottom-style: solid;
		}
		
		.components-menu-item__item {
			margin-right: initial;
			min-width: initial;
		}

	/* typography */
	.customize-typography-control {
		position: relative;
	}

	.customize-typography-control .dashicon {
		position: absolute;
		top: 0;
		right: 0;
	}
		/* popup */
		.blogzee-typography-popover.components-popover {
			min-width: 300px;
			max-width: 600px;
			width: 18%;
			margin-left: 28px;
		}

		.blogzee-typography-popover.components-popover .components-popover__content {
			padding: 12px 15px;
			width: 90%;
			margin: 0 auto;
			background-color: #fff;
			box-shadow: 0px 2px 4px 2px #0000001c;
			border-radius: 4px;
		}

		.blogzee-typography-popover.components-popover .components-popover__content .components-tab-panel__tabs {
			justify-content: space-between;
			margin-inline: 5px;
			border: 1px solid #efefef;
			margin-bottom: 8px;
			border-radius: 3px;
		}

		.blogzee-typography-popover.components-popover .components-popover__content .components-tab-panel__tabs-item {
			height: initial;
			width: 100%;
			padding: 8px 5px;
			text-transform: uppercase;
			letter-spacing: 0.7px;
			font-size: 12px;
			justify-content: center;
		}

		.blogzee-typography-popover.components-popover .components-popover__content .components-tab-panel__tabs-item .preset-indicator-wrapper {
			position: relative;
		}

		.preset-indicator-wrapper:before {
			content: '\f57d';
			font-family: 'Font Awesome 5 Free';
			font-weight: 900;
			position: absolute;
			right: -19px;
			top: 50%;
			transform: translateY(-50%);
			font-size: 12px;
		}

		.customize-typography-control > .preset-indicator-wrapper:before {
			transform: initial;
			color: #5fa1ff;
			font-size: 11px;
			top: 0;
			right: 20px;
		}

		.blogzee-typography-popover.components-popover .components-popover__content .components-tab-panel__tabs-item.active-tab {
			background: #efefef;
			color: #5fa1ff;
		}

		.blogzee-typography-popover.components-popover .typo-fields {
			margin: 0;
		}

		.blogzee-typography-popover .typo-field {
			position: relative;
			margin: 5px;
			padding: 5px 0;
			box-sizing: border-box;
			border-bottom: 1px dashed #eee;
		}

		.blogzee-typography-popover .typo-field .inner-field > div {
			cursor: pointer;
		}

		.blogzee-typography-popover .typo-field:first-child,
		.blogzee-typography-popover .typo-field:nth-child(2) {
			border: none;
		}

		.blogzee-typography-popover .inner-field.font-family input {
			box-shadow: none;
			min-height: 27px;
		}

		.blogzee-typography-popover .inner-field.font-family input:after {
			content: none;
		}

		.blogzee-typography-popover .components-popover__content .field-group {
			display: flex;
			align-items: center;
			position: relative;
			box-sizing: border-box;
			border-bottom: none;
			gap: 7px;
		}

		.blogzee-typography-popover .components-popover__content .field-group .inner-field {
			flex: 1;
			display: flex;
			justify-content: space-evenly;
			border: 1px solid #5fa1ff;
		}

		.blogzee-typography-popover .inner-field.text-decoration span,
		.blogzee-typography-popover .inner-field.text-transform span {
			flex: 1;
			display: flex;
			justify-content: center;
			position: relative;
			margin: 0;
			padding: 5px 0;
			cursor: pointer;
		}

		.blogzee-typography-popover .inner-field.text-decoration span:nth-child(2),
		.blogzee-typography-popover .inner-field.text-transform span:nth-child(2) {
			border-right: 1px solid #5fa1ff;
			border-left: 1px solid #5fa1ff;
		}

		.blogzee-typography-popover .inner-field.text-transform span:nth-child(3) {
			border-right: 1px solid #5fa1ff;
		}

		.blogzee-typography-popover .inner-field.text-decoration span:hover,
		.blogzee-typography-popover .inner-field.text-transform span:hover {
			background-color: #f0f0f1;
			/* color: #fff; */
		}

		.blogzee-typography-popover .inner-field.text-decoration span.isActive,
		.blogzee-typography-popover .inner-field.text-transform span.isActive {
			color: #fff;
			background: #5fa1ff;
		}
		
		/* responsive icon */
		.blogzee-typography-popover .components-popover__content .responsive-icons {
			cursor: pointer;
			position: absolute;
			right: 0;
		}

		.blogzee-typography-popover .components-popover__content .responsive-icons .dashicon:hover {
			color: #5fa1ff;
		}

		.blogzee-typography-popover .responsive-icons .dashicons.isActive {
			color: #5fa1ff;
		}

		.blogzee-typography-popover .responsive-icons .dashicons {
			font-size: 11px;
		}

		/* reset icon */
		.blogzee-typography-popover .typo-field .reset-button {
			position: absolute;
			right: 60px;
		}

		/* Scrollbar styling */
		.components-popover__content::-webkit-scrollbar {
			width: 5px;
		}
		
		/* Track */
		.components-popover__content::-webkit-scrollbar-track {
			box-shadow: inset 0 0 5px #65a4ff; 
			border-radius: 10px;
		}
		
		/* Handle */
		.components-popover__content::-webkit-scrollbar-thumb {
			background: #65a4ff; 
			border-radius: 20px;
		}
		
		/*  */
		.customize-typography-control .control-title {
			font-size: 12.5px;
			font-weight: 500;
			letter-spacing: .1px;
			cursor: default;
		}
		
		.customize-typography-control .control-title .reset-button {
			float: right;
		}
		
		.customize-typography-control .components-dropdown,
		.customize-typography-preset-control .components-dropdown {
			display: block;
			margin-top: 6px;
		}
		
		.customize-typography-control .typo-value-holder,
		.customize-typography-preset-control .typo-value-holder {
			position: relative;
		}
		
		.customize-typography-control .typo-summ-value:hover,
		.customize-typography-preset-control .typo-summ-value:hover,
		.box-shadow-value-holder:hover .box-shadow-summ-value {
			border-color: #0e8ecc;
		}
		
		.customize-typography-control .typo-summ-value,
		.customize-typography-preset-control .typo-summ-value {
			display: flex;
			align-items: center;
			justify-content: space-between;
			cursor: pointer;
			background: #fff;
			border-radius: 3px;
			border: 1px solid #ddd;
			padding-right: 8px;
			transition: border-color .1s linear;
		}
		
		.customize-typography-control .summ-vals,
		.customize-typography-preset-control .summ-vals {
			padding: 6px 0 6px 8px;
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
			width: 100%;
			box-sizing: border-box;
		}
		
		.customize-typography-control .summ-vals .summ-val,
		.customize-typography-preset-control .summ-vals .summ-val {
			display: inline-flex;
			align-items: center;
		}
		
		.customize-typography-control .summ-vals i,
		.customize-typography-preset-control .summ-vals i {
			margin: 0 7px;
			font-style: normal;
			font-size: 70%;
			opacity: .5;
			line-height: normal;
			position: relative;
			margin-top: -5px;
		}
		
		.customize-typography-control .append-icon,
		.customize-typography-preset-control .append-icon {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			width: 17px;
			height: 17px;
			font-size: 10px;
			font-weight: 700;
			letter-spacing: .1em;
			opacity: .7;
			color: inherit;
		}

	/* box-shadow */
		.customize-box-shadow-control .control-inner-content .components-dropdown {
			display: block;
			clear: both;
			width: 100%;
		}

		.blogzee-box-shadow-control-popover .components-popover__content {
			padding: 12px;
			width: 290px;
			margin: 0 auto;
			background-color: #fff;
			box-shadow: 0px 2px 4px 2px #0000001c;
			border-radius: 4px;
		}

		.blogzee-box-shadow-control-popover .inner-fields {
			margin: 0;
		}

        .blogzee-box-shadow-control-popover .inner-fields .inner-field:first-child {
            margin-bottom: 15px;
        }

		/* box shadow color */
			.customize-box-shadow-control {
				display: flex; 
				flex-direction: column;
			}

			.customize-box-shadow-control .control-inner-content {
				display: flex;
				justify-content: flex-end;
				-webkit-justify-content: flex-end;
				align-items: center;
				-webkit-align-items: center;
			}

			.customize-box-shadow-control .control-inner-content .components-dropdown:first-child {
				flex: 1 1 90%;
			}

			.customize-box-shadow-control .control-inner-content .components-dropdown + .components-dropdown {
				display: flex;
				flex: 0 1 23%;
				justify-content: flex-end;
			}

			.customize-box-shadow-control .component-color-indicator {
				width: 40px;
				height: 24px;
			}

			/* box shadow popup */			
			.customize-box-shadow-control .control-title {
				display: flex;
				margin-bottom: 6px;
				justify-content: space-between;
				-webkit-justify-content: space-between;
				align-items: center;
				-webkit-align-items: center;
			}
			
			.customize-box-shadow-control .control-title .reset-button {
				padding: 0;
				height: initial;
			}
			  
			.customize-box-shadow-control .box-shadow-reflector {
				background-color: #1b841545;
				border-radius: 5px;
			}
			
			.customize-box-shadow-control .box-shadow-reflector:hover {
				cursor: pointer;
			}
			
			.box-shadow-value-holder .box-shadow-summ-value {
				display: flex;
				align-items: center;
				justify-content: space-between;
				cursor: pointer;
				background: #fff;
				border-radius: 3px;
				border: 1px solid #ddd;
				padding-right: 8px;
			}

			.customize-box-shadow-control .box-shadow-value-holder .summ-vals {
				padding: 4px 7px 4px 7px;
				text-overflow: ellipsis;
				white-space: nowrap;
				box-sizing: border-box;
			}
			
			.box-shadow-summ-value .summ-vals .summ-val {
				display: inline-flex;
				align-items: center;
			}
			
			.box-shadow-summ-value .summ-vals i {
				margin: 0 7px;
				font-style: normal;
				font-size: 70%;
				opacity: .5;
				line-height: normal;
				position: relative;
				margin-top: -5px;
			}
			
			.box-shadow-summ-value .append-icon {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				width: 17px;
				height: 17px;
				font-size: 10px;
				font-weight: 700;
				letter-spacing: .1em;
				opacity: .7;
				color: inherit;
			}
			
			.customize-box-shadow-control .components-dropdown:first-child .components-popover__content {
				width: 200px;
				text-align: left;
			}

			.customize-box-shadow-control .control-title .customize-control-title {
				margin: 0;
			}

	/* divider */
		.customize-control.blogzee-bottom-separator {
			margin-bottom: 16px;
		}

	/* color */
		.customize-theme-color-control .control-header-trigger {
			display: flex;
			justify-content: space-between;
			-webkit-justify-content: space-between;
			align-items: center;
			-webkit-align-items: center;
			position: relative;
		}

		.customize-theme-color-control .dashicon.dashicons.components-button {
			font-family: 'dashicons';
			box-shadow: none;
			position: absolute;
    		right: 45px;
			top: 44%;
			transform: translateY(-50%);
		}

		/* preset */
			.customize-preset-control .components-flex {
				gap: 15px;
				margin-top: 15px;
			}

			.customize-preset-control .field-wrap .components-radio-control__option > input[type="radio"] {
				display: none;
			}

			.customize-preset-control label {
				border: 1px solid #efefef;
				padding: 15px;
				display: block;
				border-radius: 3px;
				box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    			transition: border-color .15s ease, box-shadow .15s ease;
				position: relative;
			}

			.customize-preset-control label:hover,
			.customize-preset-control input[type="radio"]:checked ~ label {
				border-color: #5fa1ff;
    			box-shadow: 0 0 0 1px #5fa1ff;
			}

            .customize-preset-control input[type="radio"]:checked ~ label:after {
                content: '\f00c';
                font-family: 'Font Awesome 5 Free';
                font-weight: 900;
                background: #5fa1ff;
                color: #fff;
                padding: 0 6px 0 7px;
                font-size: 8px;
                border-radius: 0 2px 0 2px;
                position: absolute;
                top: 0;
                right: 0;
            }

			.customize-preset-control .palette {
				display: grid;
				grid-template-columns: repeat(5, 1fr);
				gap: 5px;
			}

			.customize-preset-control .palette .item {
				position: relative;
			}

			.customize-preset-control .palette .item .dashicons {
				position: absolute;
				top: -9px;
				right: 0;
				visibility: hidden;
				opacity: 0;
			}

			.customize-preset-control .palette .item:hover .dashicons {
				visibility: visible;
				opacity: 1;
			}

			.customize-preset-control .palette .item .dashicon:before {
				padding: 5px;
				color: #fff;
				border-radius: 50%;
				font-size: 8px;
				background: #363f42;
    			transition: .2s cubic-bezier(0.455, 0.03, 0.515, 0.955);
			}

			.customize-preset-control .components-radio-control__option .palette button[type="button"],
			.customize-preset-control .add-to-list,
			.customize-social-share-control .add-to-list,
			.customize-control.customize-control-typography-preset .customize-typography-preset-control .add-to-list {
				background-color: #5fa1ff;
				padding: 0 20px;
			}

			.customize-preset-control .components-radio-control__option .palette button[type="button"] {
				background-color: #515151;
				padding: 1px 12px 0px 11px;
				height: 21px;
			}

			.customize-preset-control .components-radio-control__option .palette button[type="button"]:focus,
			.customize-preset-control .add-to-list[type="button"]:focus,
			.customize-social-share-control .add-to-list[type="button"]:focus,
			.customize-control.customize-control-typography-preset .customize-typography-preset-control .add-to-list[type="button"]:focus {
				box-shadow: none;
			}

			/* trash icon */
			.customize-preset-control .palette .remove-from-list {
				position: absolute;
				font-size: 8px;
				top: -10px;
				left: -10px;
				background: #fb1a1a;
				border-radius: 50%;
				text-align: center;
				width: 18px;
				color: #fff;
				line-height: 17px;
				opacity: 0;
				visibility: hidden;
			 }
	
			 .customize-preset-control .components-radio-control__option > label:hover .palette .remove-from-list {
				 opacity: 1;
				 visibility: visible;
			 }

			.customize-preset-control .palette .remove-from-list:before {
				font-size: 11px;
			}

			/* pallet add button */
			.customize-preset-control .field-main > button.components-button.add-to-list,
			#customize-control-typography_presets .add-to-list {
				margin-top: 15px;
				text-align: center;
				height: 30px;
				width: initial;
				min-width: initial;
    			padding: 0 14px 0 8px;
			}

			.customize-preset-control .field-main > .add-to-list .dashicon,
			.customize-control-social-share .customize-social-share-control .add-to-list .dashicon {
				padding: 0;
				align-items: center;
				justify-content: center;
				vertical-align: middle;
				margin-right: 2px;
			}

			.customize-preset-control .field-main > .add-to-list:hover,
			.customize-control-social-share .customize-social-share-control .add-to-list:hover,
			.customize-control.customize-control-typography-preset .customize-typography-preset-control .add-to-list:hover {
				background-color: #4a93fb;
			}

		/* category color */
			.customize-control.customize-control-section-heading-toggle {
				margin-left: -20px;
			}

	/* color picker */
		.components-popover .components-color-picker {
			padding: 5px;
			box-sizing: border-box;
			width: 100%;
		}

		.react-colorful {
			margin: 0 auto;
		}
		
		.components-popover__content .components-color-picker .react-colorful,
		.components-popover__content .components-popover__content .react-colorful,
		.components-popover .components-custom-gradient-picker__gradient-bar { 
			width: 250px;
		}

		.customize-control-box-shadow .react-colorful__saturation,
		.components-color-picker .react-colorful__saturation{
			height: 125px !important;
			border-radius: 4px !important;
		}

		.components-popover__content .react-colorful .react-colorful__hue,
		.components-popover__content .react-colorful .react-colorful__alpha {
			width: 244px;
			margin: 0;
		}

		.components-popover__content .react-colorful .react-colorful__hue {
			margin-bottom: 15px;
		}

		.components-popover__content .components-color-picker > div > .components-flex {
			padding: 0 6px;
			margin: 0 6px;
		}

        .components-popover__content .components-color-picker > div > .components-flex:last-child {
            padding: 0 3px;
        }

		.components-popover__content .components-color-picker .components-flex .components-input-control__container:first-child {
			margin-left: 2px;
		}

		.components-popover__content .components-color-picker .components-flex select.components-select-control__input {
			border: 1px solid #949494;
		}

		.components-popover__content .components-color-picker .components-flex select.components-select-control__input:focus {
			box-shadow: none;
			outline: none;
		}

		.components-popover__content .components-color-picker .components-flex svg {
			font-size: 12px;
			width: 20px;
		}

		.components-popover__content .components-color-picker .components-flex .components-base-control__field {
			margin-top: 9px;
		}

		/* tab */
		.blogzee-color-control-popover .blogzee-group-tab-panel .components-tab-panel__tabs button {
			flex: 1 1 100%;
			justify-content: center;
			align-items: center;
			border-right: 1px solid #eee;
			border-bottom: 1px solid #eee;
			position: relative;
			height: 40px;
		}

		.blogzee-color-control-popover .blogzee-group-tab-panel .components-tab-panel__tabs button:last-child {
			border-right: 0px solid transparent;
		} 	

		.blogzee-color-control-popover .blogzee-group-tab-panel .components-tab-panel__tabs button.active-tab:after {
			position: absolute;
			content: "";
			left: -1px;
			bottom: -1px;
			width: calc(100% + 2px);
			height: 2px;
			background: #5fa1ff;
		}

		/* preset */
			.components-popover .components-tab-panel__tabs button[aria-disabled] {
				display: none;
			}
			
			.components-popover .preset-colors {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				padding: 10px 0;
				box-sizing: border-box;
			}

			.components-popover .preset-colors .preset-colors-inner {
				display: grid;
				grid-template-columns: repeat(7, 1fr);
				gap: 7px;
				margin: 0;
				padding: 0 5px;
			}

			.components-popover .preset-colors .preset-colors-inner li {
				width: 30px;
				height: 20px;
			}

			.components-popover .preset-colors .preset-colors-inner li span {
				width: 30px;
				height: 20px;
				cursor: pointer;
				border-radius: 3px;
				position: relative;
				box-sizing: border-box;
				box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .2);
			}

			.components-popover .preset-colors .preset-colors-inner li span.active:before {
				position: absolute;
				content: "";
				z-index: 0;
				width: 30px;
				height: 20px;
				background: rgb(0 0 0 / 17%);
				border-radius: 3px;
			}

			.components-popover .preset-colors .preset-colors-inner li span.active:after {
				display: flex;
				align-items: center;
				justify-content: center;
				position: absolute;
				top: 1px;
				left: -1px;
				right: 0px;
				bottom: 0px;
				content: "\f147";
				font-family: dashicons;
				font-size: 15px;
				color: rgb(255, 255, 255);
				text-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;
			}

			/* gradient color */
			.components-popover .components-custom-gradient-picker {
				padding: 0 5px;
			}

			.components-popover .components-custom-gradient-picker {
				padding-bottom: 5px;
			}

			.components-popover .components-circular-option-picker {
				display: none;
			}

			.components-popover .components-custom-gradient-picker__gradient-bar-background {
				border-radius: 3px;
			}

			/* preset active */
			.preset-isactive span.component-color-indicator:before {
				content: '\f57d';
				font-family: 'Font Awesome 5 Free';
				font-weight: 800;
				color: #fff;
				position: absolute;
				top: 52%;
				left: 50%;
				transform: translate(-50%, -50%);
				font-size: 12px;
			}

			.preset-isactive span.component-color-indicator {
				position: relative;
			}

	/* typography preset */
	/* typo field */
	.customize-control.customize-control-typography-preset .control-title {
        position: relative;
        margin-bottom: 20px;
    }

    .customize-control.customize-control-typography-preset .control-title .reset-button {
        position: absolute;
        top: 0;
        right: 0;
    }

	.customize-control.customize-control-typography-preset .control-title .reset-button:before {
		font-size: 13px;
	}

    .customize-control.customize-control-typography-preset .field-wrap .typography-wrapper + .typography-wrapper {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px dashed #d7d7d7;
    }

    .customize-control.customize-control-typography-preset .field-wrap .typography-wrapper input[type="text"] {
        border: 1px dashed #bad7ff;
        background: #f1f6ff;
        color: #484848;
        font-size: 14px;
        letter-spacing: 0.3px;
        padding: 7px 10px;
		font-size: 13px;
    }

    .customize-control.customize-control-typography-preset .field-wrap .typography-wrapper input[type="text"]:focus {
        border: 1px solid #5fa1ff;
        box-shadow: none;
    }

	/* typo item */
	.customize-control-typography-preset .typography-item {
		display: flex;
		align-items: center;
		border: 1px solid #ddd;
		padding: 8px 10px;
		border-radius: 2px;
		margin-top: 12px;
	}

	.customize-control-typography-preset .components-dropdown {
		width: 73%;
		margin: 0;
	}

	.customize-control-typography-preset .typo-value-holder .typo-summ-value {
		border: none;
		width: 100%;
		padding-right: 0;
	}

	.customize-control-typography-preset .typo-value-holder .typo-summ-value span.dashicons {
		display: none;
	}

	.customize-control-typography-preset .summ-vals {
		padding: 0;
	}

	.customize-control-typography-preset .summ-vals .summ-val {
		font-size: 11px;
	}

	/* buttons */
	.customize-control-typography-preset .trash-reset-wrapper {
		order: 2;
		flex: 0 1 30%;
		text-align: right;
		opacity: 0;
		visibility: hidden;
		-webkit-transition: 0.3s ease;
    	transition: 0.3s ease;
	}

	.customize-control-typography-preset .typography-item:hover .trash-reset-wrapper {
		opacity: 1;
		visibility: visible;
	}

	.customize-control-typography-preset .trash-reset-wrapper > span {
		border-left: 1px solid #d7d7d7;
		padding: 0 4px 0 4px;
	}

	.customize-control-typography-preset .trash-reset-wrapper .reset-button {
		padding-left: 8px;
	}

	.customize-control.customize-control-typography-preset .customize-typography-preset-control .add-to-list {
        margin-top: 10px;
	}

    /* popop */
    .blogzee-typography-popover .components-radio-control__option {
		display: initial;
		margin: 5px;
		border-radius: 3px;
		overflow: hidden;
    }

	.blogzee-typography-popover .components-radio-control__option:last-child {
		margin-bottom: 5px;
	}

	.components-radio-control__option {
		background: #f1f6ff;
	}

    .components-radio-control__option input[type="radio"]:checked ~ label h2:after {
        content: '\f00c';
		font-family: 'Font Awesome 5 Free';
		font-weight: 900;
		font-size: 12px;
		position: absolute;
		top: 50%;
		right: 0;
		transform: translateY(-50%);
		color: #5fa1ff;
		background: #fff;
		border-radius: 50%;
		width: 25px;
		height: 25px;
		text-align: center;
		line-height: 25px;
		margin-right: 11px;
		margin-left: 10px;
    }

	.components-radio-control__option input[type="radio"]:checked ~ label h2 {
		color: #5fa1ff;
	}

    .components-radio-control__option h2 {
        margin: 0;
		position: relative;
		padding: 10px 40px 10px 10px;
		line-height: 23px;
		border: 1px dashed #d7d7d7;
    }

	.components-radio-control__option h2:hover {
		color: #5fa1ff;
	}

    .components-radio-control__option .components-radio-control__input {
        display: none;
    }

	.css-1nmdiq5-menu {
		z-index: 9 !important;
	}

/* RTL */
.rtl .components-form-toggle.is-checked .components-form-toggle__thumb {
	transform: translateX(-14px);
}

.rtl .customize-radio-tab-control .components-button {
	border-left: none;
	border-right: 1px solid #5fa1ff;
}

.rtl .components-truncate {
	margin: 0 14px 0 0;
}

.rtl .box-shadow-value-holder .box-shadow-summ-value {
	padding: 0 0 0 8px;
}

.rtl .customize-number-range-control .components-base-control,
.rtl .typo-field .components-base-control,
.rtl .inner-field .components-range-control__root .components-base-control {
	margin: 0 16px 0 0 !important;
}

.rtl .customize-typography-control .dashicon {
	left: 0;
	right: initial;
}

.rtl .customize-typography-control .typo-summ-value {
	padding: 0 8px 0;
}

.rtl .customize-control.customize-control-number input[type=number] {
	float: left;
}

.rtl .components-menu-item__item {
	margin-left: initial;
}

.rtl .customize-spacing-control .field-main .field-wrap .components-base-control:first-child .components-input-control__backdrop,
.rtl .customize-border-control .field-wrap .components-base-control:first-child .components-input-control__backdrop {
	border-right: 1px solid;
	border-left: none;
}

.rtl .customize-control-social-share .customize-social-share-control .item .social-icon-dropdown-wrapper span,
.rtl .blogzee-repeater-control-inner .item-heading-wrap .visibility-dropdown-wrapper span {
	border-left: 0;
    border-right: 1px solid #d7d7d7;
}

.rtl .customize-control-social-share .customize-social-share-control .item .social-icon-dropdown-wrapper span.dashicon,
.rtl .blogzee-repeater-control-inner .item-heading-wrap .visibility-dropdown-wrapper span:last-child {
	padding-left: 0;
	padding-right: 9px;
}

.rtl .customize-control-social-share .customize-social-share-control .item .social-icon-dropdown-wrapper span.dashicon.dashicons-image-rotate {
	padding: 0 9px;
}

.rtl .customize-theme-color-control .dashicon.dashicons.components-button {
	left: 45px;
	right: initial;
}

.rtl .customize-control.customize-control-section-heading-toggle {
	margin-left: initial;
	margin-right: -20px;
}

.rtl .customize-control.customize-control-typography-preset .control-title .reset-button {
	left: 0;
	right: initial;
}

.rtl .blogzee-typography-popover.components-popover {
	margin-left: -28px;
}

.rtl .customize-border-control .control-inner .components-dropdown:nth-child(2) {
	margin: 0 8px 0 0;
}

.rtl .blogzee-typography-popover .inner-field.text-transform span:nth-child(3) {
	border-right: none;
	border-left: 1px solid #5fa1ff;
}

.rtl .components-radio-control__option h2 {
	padding: 10px 10px 10px 40px;
}

.rtl .components-radio-control__option input[type="radio"]:checked ~ label h2:after {
	left: 0;
    right: initial;
}

.rtl .customize-typography-control > .preset-indicator-wrapper:before {
	left: 20px;
	right: initial;
}

/* header builder */
.rtl #customize-theme-controls .blogzee-builder.is-active .customize-control-builder.is-active,
.rtl #customize-theme-controls .blogzee-builder .customize-control-responsive-builder.is-active {
	left: 0;
	right: 18%;
}

.rtl .builder-footer-wrapper .upgrade-notice-wrapper .components-button {
	margin: 0 20px 0 0;
}

.rtl .customize-responsive-radio-tab-control .components-button:first-child {
	border-right: 1px solid #5fa1ff;
}

.rtl .customize-responsive-radio-tab-control .components-button:last-child {
	border: none;
}

.rtl .blogzee-builder .rows-wrapper .row-settings {
	margin: 0 0 0 10px;
}

.rtl .row .components-dropdown .column.has-children .column-item .item-label:first-child:before,
.rtl .customize-control-social-share .customize-social-share-control .item .social-share-wrapper > .current-icon-label:before,
.rtl .blogzee-repeater-control .blogzee-repeater-control-inner .blogzee-repeater-item .item-heading-wrap > .item-heading:before {
	display: none;
}

.rtl .row .components-dropdown .column.has-children .column-item .item-label:first-child:after,
.rtl .customize-control-social-share .customize-social-share-control .item .social-share-wrapper > .current-icon-label:after,
.rtl .blogzee-repeater-control .blogzee-repeater-control-inner .blogzee-repeater-item .item-heading-wrap > .item-heading:after {
	content: '\f58e';
	padding: 0 0 0 8px;
	font-weight: 900;
    font-family: 'Font Awesome 5 Free';
    font-size: 11px;
    color: #00000070;
}

/* upsell */
.customize-control-upsell {
	padding: 10px 21px;
  box-sizing: border-box;
  background: #fff;
}

.customize-control-upsell .customize-control-title {
	font-size: 12px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12px;
  line-height: 21px;
  position: relative;
  padding-top: 26px;
  text-align: center;
	letter-spacing: 0;
}

.customize-control-upsell .customize-control-title:before {
	content: '\f521';
    font-family: 'Font Awesome 5 Free';
    color: #efbf04;
    font-size: 15px;
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
}

.customize-control-upsell .features-wrap {
	padding: 0 5px;
}

.customize-control-upsell .features-wrap li {
	margin: 7px 0;
    font-weight: 500;
    color: #5a5a5a;
    font-size: 12px;
    letter-spacing: 0.2px;
}

.customize-control-upsell li.feature:before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 10px;
    margin-right: 8px;
    color: #10b981;
}

.customize-control-upsell a.upsell-btn {
	outline: 1px solid #0073aa;
    width: 100%;
    display: inline-block;
    text-align: center;
    padding: 8px 0;
    text-decoration: none;
    font-size: 13px;
    letter-spacing: 0.3px;
    margin-top: 14px;
    color: #0073aa;
    transition: all .3s ease-in-out;
    border-radius: 1px;
}

.customize-control-upsell a.upsell-btn:hover {
	background-color: #0073aa;
	color: #fff;
}

.customize-control-upsell a.upsell-btn:focus {
	box-shadow: none;
}